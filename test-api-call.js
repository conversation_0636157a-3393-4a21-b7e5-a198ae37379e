#!/usr/bin/env node

// Test script to call the review refresh API directly
import fetch from 'node-fetch';

const BASE_URL = 'http://localhost:5001';

async function testReviewRefreshAPI() {
  try {
    console.log('🧪 Testing review refresh API directly...');
    
    // Test the review refresh endpoint without authentication first
    console.log('🔄 Calling review refresh endpoint...');
    const refreshResponse = await fetch(`${BASE_URL}/api/analytics/reviews/refresh`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    console.log(`📊 Response status: ${refreshResponse.status}`);
    
    if (refreshResponse.status === 401) {
      console.log('🔐 Authentication required - this is expected behavior');
      console.log('✅ API endpoint is accessible and properly secured');
      return;
    }

    const refreshData = await refreshResponse.json();
    console.log('📋 Response data:', refreshData);

  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

// Test basic connectivity
async function testConnectivity() {
  try {
    console.log('🔗 Testing basic connectivity...');
    const response = await fetch(`${BASE_URL}/api/health`, {
      method: 'GET',
    });
    
    if (response.ok) {
      console.log('✅ Backend server is accessible');
    } else {
      console.log(`⚠️ Backend responded with status: ${response.status}`);
    }
  } catch (error) {
    console.log('❌ Cannot connect to backend:', error.message);
  }
}

// Run tests
async function runTests() {
  await testConnectivity();
  await testReviewRefreshAPI();
}

runTests();
