var ly=Object.defineProperty;var cy=(e,t,r)=>t in e?ly(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r;var Pa=(e,t,r)=>(cy(e,typeof t!="symbol"?t+"":t,r),r);import{g as Et,r as p,a as Wf}from"./vendor-280e31ee.js";function Uf(e){var t,r,n="";if(typeof e=="string"||typeof e=="number")n+=e;else if(typeof e=="object")if(Array.isArray(e)){var i=e.length;for(t=0;t<i;t++)e[t]&&(r=Uf(e[t]))&&(n&&(n+=" "),n+=r)}else for(r in e)e[r]&&(n&&(n+=" "),n+=r);return n}function U(){for(var e,t,r=0,n="",i=arguments.length;r<i;r++)(e=arguments[r])&&(t=Uf(e))&&(n&&(n+=" "),n+=t);return n}var Ai={},qf={};(function(e){Object.defineProperty(e,Symbol.toStringTag,{value:"Module"});function t(r){return r==="__proto__"}e.isUnsafeProperty=t})(qf);var qo={};(function(e){Object.defineProperty(e,Symbol.toStringTag,{value:"Module"});function t(r){switch(typeof r){case"number":case"symbol":return!1;case"string":return r.includes(".")||r.includes("[")||r.includes("]")}}e.isDeepKey=t})(qo);var Yo={};(function(e){Object.defineProperty(e,Symbol.toStringTag,{value:"Module"});function t(r){var n;return typeof r=="string"||typeof r=="symbol"?r:Object.is((n=r==null?void 0:r.valueOf)==null?void 0:n.call(r),-0)?"-0":String(r)}e.toKey=t})(Yo);var Si={};(function(e){Object.defineProperty(e,Symbol.toStringTag,{value:"Module"});function t(r){const n=[],i=r.length;if(i===0)return n;let a=0,o="",u="",c=!1;for(r.charCodeAt(0)===46&&(n.push(""),a++);a<i;){const s=r[a];u?s==="\\"&&a+1<i?(a++,o+=r[a]):s===u?u="":o+=s:c?s==='"'||s==="'"?u=s:s==="]"?(c=!1,n.push(o),o=""):o+=s:s==="["?(c=!0,o&&(n.push(o),o="")):s==="."?o&&(n.push(o),o=""):o+=s,a++}return o&&n.push(o),n}e.toPath=t})(Si);(function(e){Object.defineProperty(e,Symbol.toStringTag,{value:"Module"});const t=qf,r=qo,n=Yo,i=Si;function a(u,c,s){if(u==null)return s;switch(typeof c){case"string":{if(t.isUnsafeProperty(c))return s;const l=u[c];return l===void 0?r.isDeepKey(c)?a(u,i.toPath(c),s):s:l}case"number":case"symbol":{typeof c=="number"&&(c=n.toKey(c));const l=u[c];return l===void 0?s:l}default:{if(Array.isArray(c))return o(u,c,s);if(Object.is(c==null?void 0:c.valueOf(),-0)?c="-0":c=String(c),t.isUnsafeProperty(c))return s;const l=u[c];return l===void 0?s:l}}}function o(u,c,s){if(c.length===0)return s;let l=u;for(let f=0;f<c.length;f++){if(l==null||t.isUnsafeProperty(c[f]))return s;l=l[c[f]]}return l===void 0?s:l}e.get=a})(Ai);var sy=Ai.get;const Rt=Et(sy);var Yf={exports:{}},Z={};/**
 * @license React
 * react-is.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Ho=Symbol.for("react.transitional.element"),Go=Symbol.for("react.portal"),Ei=Symbol.for("react.fragment"),_i=Symbol.for("react.strict_mode"),Ti=Symbol.for("react.profiler"),ji=Symbol.for("react.consumer"),Ci=Symbol.for("react.context"),ki=Symbol.for("react.forward_ref"),Mi=Symbol.for("react.suspense"),Di=Symbol.for("react.suspense_list"),Ii=Symbol.for("react.memo"),$i=Symbol.for("react.lazy"),fy=Symbol.for("react.view_transition"),dy=Symbol.for("react.client.reference");function nt(e){if(typeof e=="object"&&e!==null){var t=e.$$typeof;switch(t){case Ho:switch(e=e.type,e){case Ei:case Ti:case _i:case Mi:case Di:case fy:return e;default:switch(e=e&&e.$$typeof,e){case Ci:case ki:case $i:case Ii:return e;case ji:return e;default:return t}}case Go:return t}}}Z.ContextConsumer=ji;Z.ContextProvider=Ci;Z.Element=Ho;Z.ForwardRef=ki;Z.Fragment=Ei;Z.Lazy=$i;Z.Memo=Ii;Z.Portal=Go;Z.Profiler=Ti;Z.StrictMode=_i;Z.Suspense=Mi;Z.SuspenseList=Di;Z.isContextConsumer=function(e){return nt(e)===ji};Z.isContextProvider=function(e){return nt(e)===Ci};Z.isElement=function(e){return typeof e=="object"&&e!==null&&e.$$typeof===Ho};Z.isForwardRef=function(e){return nt(e)===ki};Z.isFragment=function(e){return nt(e)===Ei};Z.isLazy=function(e){return nt(e)===$i};Z.isMemo=function(e){return nt(e)===Ii};Z.isPortal=function(e){return nt(e)===Go};Z.isProfiler=function(e){return nt(e)===Ti};Z.isStrictMode=function(e){return nt(e)===_i};Z.isSuspense=function(e){return nt(e)===Mi};Z.isSuspenseList=function(e){return nt(e)===Di};Z.isValidElementType=function(e){return typeof e=="string"||typeof e=="function"||e===Ei||e===Ti||e===_i||e===Mi||e===Di||typeof e=="object"&&e!==null&&(e.$$typeof===$i||e.$$typeof===Ii||e.$$typeof===Ci||e.$$typeof===ji||e.$$typeof===ki||e.$$typeof===dy||e.getModuleId!==void 0)};Z.typeOf=nt;Yf.exports=Z;var vy=Yf.exports,me=e=>e===0?0:e>0?1:-1,We=e=>typeof e=="number"&&e!=+e,Jt=e=>typeof e=="string"&&e.indexOf("%")===e.length-1,k=e=>(typeof e=="number"||e instanceof Number)&&!We(e),vt=e=>k(e)||typeof e=="string",hy=0,ur=e=>{var t=++hy;return"".concat(e||"").concat(t)},Te=function(t,r){var n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:0,i=arguments.length>3&&arguments[3]!==void 0?arguments[3]:!1;if(!k(t)&&typeof t!="string")return n;var a;if(Jt(t)){if(r==null)return n;var o=t.indexOf("%");a=r*parseFloat(t.slice(0,o))/100}else a=+t;return We(a)&&(a=n),i&&r!=null&&a>r&&(a=r),a},Hf=e=>{if(!Array.isArray(e))return!1;for(var t=e.length,r={},n=0;n<t;n++)if(!r[e[n]])r[e[n]]=!0;else return!0;return!1},Be=(e,t)=>k(e)&&k(t)?r=>e+r*(t-e):()=>t;function Gf(e,t,r){if(!(!e||!e.length))return e.find(n=>n&&(typeof t=="function"?t(n):Rt(n,t))===r)}var X=e=>e===null||typeof e>"u",hn=e=>X(e)?e:"".concat(e.charAt(0).toUpperCase()).concat(e.slice(1)),py=["viewBox","children"],my=["aria-activedescendant","aria-atomic","aria-autocomplete","aria-busy","aria-checked","aria-colcount","aria-colindex","aria-colspan","aria-controls","aria-current","aria-describedby","aria-details","aria-disabled","aria-errormessage","aria-expanded","aria-flowto","aria-haspopup","aria-hidden","aria-invalid","aria-keyshortcuts","aria-label","aria-labelledby","aria-level","aria-live","aria-modal","aria-multiline","aria-multiselectable","aria-orientation","aria-owns","aria-placeholder","aria-posinset","aria-pressed","aria-readonly","aria-relevant","aria-required","aria-roledescription","aria-rowcount","aria-rowindex","aria-rowspan","aria-selected","aria-setsize","aria-sort","aria-valuemax","aria-valuemin","aria-valuenow","aria-valuetext","className","color","height","id","lang","max","media","method","min","name","style","target","width","role","tabIndex","accentHeight","accumulate","additive","alignmentBaseline","allowReorder","alphabetic","amplitude","arabicForm","ascent","attributeName","attributeType","autoReverse","azimuth","baseFrequency","baselineShift","baseProfile","bbox","begin","bias","by","calcMode","capHeight","clip","clipPath","clipPathUnits","clipRule","colorInterpolation","colorInterpolationFilters","colorProfile","colorRendering","contentScriptType","contentStyleType","cursor","cx","cy","d","decelerate","descent","diffuseConstant","direction","display","divisor","dominantBaseline","dur","dx","dy","edgeMode","elevation","enableBackground","end","exponent","externalResourcesRequired","fill","fillOpacity","fillRule","filter","filterRes","filterUnits","floodColor","floodOpacity","focusable","fontFamily","fontSize","fontSizeAdjust","fontStretch","fontStyle","fontVariant","fontWeight","format","from","fx","fy","g1","g2","glyphName","glyphOrientationHorizontal","glyphOrientationVertical","glyphRef","gradientTransform","gradientUnits","hanging","horizAdvX","horizOriginX","href","ideographic","imageRendering","in2","in","intercept","k1","k2","k3","k4","k","kernelMatrix","kernelUnitLength","kerning","keyPoints","keySplines","keyTimes","lengthAdjust","letterSpacing","lightingColor","limitingConeAngle","local","markerEnd","markerHeight","markerMid","markerStart","markerUnits","markerWidth","mask","maskContentUnits","maskUnits","mathematical","mode","numOctaves","offset","opacity","operator","order","orient","orientation","origin","overflow","overlinePosition","overlineThickness","paintOrder","panose1","pathLength","patternContentUnits","patternTransform","patternUnits","pointerEvents","pointsAtX","pointsAtY","pointsAtZ","preserveAlpha","preserveAspectRatio","primitiveUnits","r","radius","refX","refY","renderingIntent","repeatCount","repeatDur","requiredExtensions","requiredFeatures","restart","result","rotate","rx","ry","seed","shapeRendering","slope","spacing","specularConstant","specularExponent","speed","spreadMethod","startOffset","stdDeviation","stemh","stemv","stitchTiles","stopColor","stopOpacity","strikethroughPosition","strikethroughThickness","string","stroke","strokeDasharray","strokeDashoffset","strokeLinecap","strokeLinejoin","strokeMiterlimit","strokeOpacity","strokeWidth","surfaceScale","systemLanguage","tableValues","targetX","targetY","textAnchor","textDecoration","textLength","textRendering","to","transform","u1","u2","underlinePosition","underlineThickness","unicode","unicodeBidi","unicodeRange","unitsPerEm","vAlphabetic","values","vectorEffect","version","vertAdvY","vertOriginX","vertOriginY","vHanging","vIdeographic","viewTarget","visibility","vMathematical","widths","wordSpacing","writingMode","x1","x2","x","xChannelSelector","xHeight","xlinkActuate","xlinkArcrole","xlinkHref","xlinkRole","xlinkShow","xlinkTitle","xlinkType","xmlBase","xmlLang","xmlns","xmlnsXlink","xmlSpace","y1","y2","y","yChannelSelector","z","zoomAndPan","ref","key","angle"],ec=["points","pathLength"],Oa={svg:py,polygon:ec,polyline:ec},Vo=["dangerouslySetInnerHTML","onCopy","onCopyCapture","onCut","onCutCapture","onPaste","onPasteCapture","onCompositionEnd","onCompositionEndCapture","onCompositionStart","onCompositionStartCapture","onCompositionUpdate","onCompositionUpdateCapture","onFocus","onFocusCapture","onBlur","onBlurCapture","onChange","onChangeCapture","onBeforeInput","onBeforeInputCapture","onInput","onInputCapture","onReset","onResetCapture","onSubmit","onSubmitCapture","onInvalid","onInvalidCapture","onLoad","onLoadCapture","onError","onErrorCapture","onKeyDown","onKeyDownCapture","onKeyPress","onKeyPressCapture","onKeyUp","onKeyUpCapture","onAbort","onAbortCapture","onCanPlay","onCanPlayCapture","onCanPlayThrough","onCanPlayThroughCapture","onDurationChange","onDurationChangeCapture","onEmptied","onEmptiedCapture","onEncrypted","onEncryptedCapture","onEnded","onEndedCapture","onLoadedData","onLoadedDataCapture","onLoadedMetadata","onLoadedMetadataCapture","onLoadStart","onLoadStartCapture","onPause","onPauseCapture","onPlay","onPlayCapture","onPlaying","onPlayingCapture","onProgress","onProgressCapture","onRateChange","onRateChangeCapture","onSeeked","onSeekedCapture","onSeeking","onSeekingCapture","onStalled","onStalledCapture","onSuspend","onSuspendCapture","onTimeUpdate","onTimeUpdateCapture","onVolumeChange","onVolumeChangeCapture","onWaiting","onWaitingCapture","onAuxClick","onAuxClickCapture","onClick","onClickCapture","onContextMenu","onContextMenuCapture","onDoubleClick","onDoubleClickCapture","onDrag","onDragCapture","onDragEnd","onDragEndCapture","onDragEnter","onDragEnterCapture","onDragExit","onDragExitCapture","onDragLeave","onDragLeaveCapture","onDragOver","onDragOverCapture","onDragStart","onDragStartCapture","onDrop","onDropCapture","onMouseDown","onMouseDownCapture","onMouseEnter","onMouseLeave","onMouseMove","onMouseMoveCapture","onMouseOut","onMouseOutCapture","onMouseOver","onMouseOverCapture","onMouseUp","onMouseUpCapture","onSelect","onSelectCapture","onTouchCancel","onTouchCancelCapture","onTouchEnd","onTouchEndCapture","onTouchMove","onTouchMoveCapture","onTouchStart","onTouchStartCapture","onPointerDown","onPointerDownCapture","onPointerMove","onPointerMoveCapture","onPointerUp","onPointerUpCapture","onPointerCancel","onPointerCancelCapture","onPointerEnter","onPointerEnterCapture","onPointerLeave","onPointerLeaveCapture","onPointerOver","onPointerOverCapture","onPointerOut","onPointerOutCapture","onGotPointerCapture","onGotPointerCaptureCapture","onLostPointerCapture","onLostPointerCaptureCapture","onScroll","onScrollCapture","onWheel","onWheelCapture","onAnimationStart","onAnimationStartCapture","onAnimationEnd","onAnimationEndCapture","onAnimationIteration","onAnimationIterationCapture","onTransitionEnd","onTransitionEndCapture"],Xo=(e,t)=>{if(!e||typeof e=="function"||typeof e=="boolean")return null;var r=e;if(p.isValidElement(e)&&(r=e.props),typeof r!="object"&&typeof r!="function")return null;var n={};return Object.keys(r).forEach(i=>{Vo.includes(i)&&(n[i]=t||(a=>r[i](r,a)))}),n},yy=(e,t,r)=>n=>(e(t,r,n),null),pn=(e,t,r)=>{if(e===null||typeof e!="object"&&typeof e!="function")return null;var n=null;return Object.keys(e).forEach(i=>{var a=e[i];Vo.includes(i)&&typeof a=="function"&&(n||(n={}),n[i]=yy(a,t,r))}),n},tc=e=>typeof e=="string"?e:e?e.displayName||e.name||"Component":"",rc=null,Aa=null,Vf=e=>{if(e===rc&&Array.isArray(Aa))return Aa;var t=[];return p.Children.forEach(e,r=>{X(r)||(vy.isFragment(r)?t=t.concat(Vf(r.props.children)):t.push(r))}),Aa=t,rc=e,t};function mn(e,t){var r=[],n=[];return Array.isArray(t)?n=t.map(i=>tc(i)):n=[tc(t)],Vf(e).forEach(i=>{var a=Rt(i,"type.displayName")||Rt(i,"type.name");n.indexOf(a)!==-1&&r.push(i)}),r}var Xf=e=>e&&typeof e=="object"&&"clipDot"in e?!!e.clipDot:!0,gy=(e,t,r,n)=>{var i,a=(i=n&&(Oa==null?void 0:Oa[n]))!==null&&i!==void 0?i:[];return t.startsWith("data-")||typeof e!="function"&&(n&&a.includes(t)||my.includes(t))||r&&Vo.includes(t)},z=(e,t,r)=>{if(!e||typeof e=="function"||typeof e=="boolean")return null;var n=e;if(p.isValidElement(e)&&(n=e.props),typeof n!="object"&&typeof n!="function")return null;var i={};return Object.keys(n).forEach(a=>{var o;gy((o=n)===null||o===void 0?void 0:o[a],a,t,r)&&(i[a]=n[a])}),i},by=["children","width","height","viewBox","className","style","title","desc"];function Ga(){return Ga=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Ga.apply(null,arguments)}function wy(e,t){if(e==null)return{};var r,n,i=xy(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],t.indexOf(r)===-1&&{}.propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}function xy(e,t){if(e==null)return{};var r={};for(var n in e)if({}.hasOwnProperty.call(e,n)){if(t.indexOf(n)!==-1)continue;r[n]=e[n]}return r}var Zo=p.forwardRef((e,t)=>{var{children:r,width:n,height:i,viewBox:a,className:o,style:u,title:c,desc:s}=e,l=wy(e,by),f=a||{width:n,height:i,x:0,y:0},d=U("recharts-surface",o);return p.createElement("svg",Ga({},z(l,!0,"svg"),{className:d,width:n,height:i,style:u,viewBox:"".concat(f.x," ").concat(f.y," ").concat(f.width," ").concat(f.height),ref:t}),p.createElement("title",null,c),p.createElement("desc",null,s),r)}),Py=["children","className"];function Va(){return Va=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Va.apply(null,arguments)}function Oy(e,t){if(e==null)return{};var r,n,i=Ay(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],t.indexOf(r)===-1&&{}.propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}function Ay(e,t){if(e==null)return{};var r={};for(var n in e)if({}.hasOwnProperty.call(e,n)){if(t.indexOf(n)!==-1)continue;r[n]=e[n]}return r}var ye=p.forwardRef((e,t)=>{var{children:r,className:n}=e,i=Oy(e,Py),a=U("recharts-layer",n);return p.createElement("g",Va({className:a},z(i,!0),{ref:t}),r)}),Zf=p.createContext(null),Sy=()=>p.useContext(Zf);function Q(e){return function(){return e}}const Jf=Math.cos,Un=Math.sin,st=Math.sqrt,qn=Math.PI,Ni=2*qn,Xa=Math.PI,Za=2*Xa,Vt=1e-6,Ey=Za-Vt;function Qf(e){this._+=e[0];for(let t=1,r=e.length;t<r;++t)this._+=arguments[t]+e[t]}function _y(e){let t=Math.floor(e);if(!(t>=0))throw new Error(`invalid digits: ${e}`);if(t>15)return Qf;const r=10**t;return function(n){this._+=n[0];for(let i=1,a=n.length;i<a;++i)this._+=Math.round(arguments[i]*r)/r+n[i]}}class Ty{constructor(t){this._x0=this._y0=this._x1=this._y1=null,this._="",this._append=t==null?Qf:_y(t)}moveTo(t,r){this._append`M${this._x0=this._x1=+t},${this._y0=this._y1=+r}`}closePath(){this._x1!==null&&(this._x1=this._x0,this._y1=this._y0,this._append`Z`)}lineTo(t,r){this._append`L${this._x1=+t},${this._y1=+r}`}quadraticCurveTo(t,r,n,i){this._append`Q${+t},${+r},${this._x1=+n},${this._y1=+i}`}bezierCurveTo(t,r,n,i,a,o){this._append`C${+t},${+r},${+n},${+i},${this._x1=+a},${this._y1=+o}`}arcTo(t,r,n,i,a){if(t=+t,r=+r,n=+n,i=+i,a=+a,a<0)throw new Error(`negative radius: ${a}`);let o=this._x1,u=this._y1,c=n-t,s=i-r,l=o-t,f=u-r,d=l*l+f*f;if(this._x1===null)this._append`M${this._x1=t},${this._y1=r}`;else if(d>Vt)if(!(Math.abs(f*c-s*l)>Vt)||!a)this._append`L${this._x1=t},${this._y1=r}`;else{let v=n-o,h=i-u,y=c*c+s*s,m=v*v+h*h,g=Math.sqrt(y),b=Math.sqrt(d),w=a*Math.tan((Xa-Math.acos((y+d-m)/(2*g*b)))/2),P=w/b,x=w/g;Math.abs(P-1)>Vt&&this._append`L${t+P*l},${r+P*f}`,this._append`A${a},${a},0,0,${+(f*v>l*h)},${this._x1=t+x*c},${this._y1=r+x*s}`}}arc(t,r,n,i,a,o){if(t=+t,r=+r,n=+n,o=!!o,n<0)throw new Error(`negative radius: ${n}`);let u=n*Math.cos(i),c=n*Math.sin(i),s=t+u,l=r+c,f=1^o,d=o?i-a:a-i;this._x1===null?this._append`M${s},${l}`:(Math.abs(this._x1-s)>Vt||Math.abs(this._y1-l)>Vt)&&this._append`L${s},${l}`,n&&(d<0&&(d=d%Za+Za),d>Ey?this._append`A${n},${n},0,1,${f},${t-u},${r-c}A${n},${n},0,1,${f},${this._x1=s},${this._y1=l}`:d>Vt&&this._append`A${n},${n},0,${+(d>=Xa)},${f},${this._x1=t+n*Math.cos(a)},${this._y1=r+n*Math.sin(a)}`)}rect(t,r,n,i){this._append`M${this._x0=this._x1=+t},${this._y0=this._y1=+r}h${n=+n}v${+i}h${-n}Z`}toString(){return this._}}function Jo(e){let t=3;return e.digits=function(r){if(!arguments.length)return t;if(r==null)t=null;else{const n=Math.floor(r);if(!(n>=0))throw new RangeError(`invalid digits: ${r}`);t=n}return e},()=>new Ty(t)}function Qo(e){return typeof e=="object"&&"length"in e?e:Array.from(e)}function ed(e){this._context=e}ed.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._point=0},lineEnd:function(){(this._line||this._line!==0&&this._point===1)&&this._context.closePath(),this._line=1-this._line},point:function(e,t){switch(e=+e,t=+t,this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;default:this._context.lineTo(e,t);break}}};function Li(e){return new ed(e)}function td(e){return e[0]}function rd(e){return e[1]}function nd(e,t){var r=Q(!0),n=null,i=Li,a=null,o=Jo(u);e=typeof e=="function"?e:e===void 0?td:Q(e),t=typeof t=="function"?t:t===void 0?rd:Q(t);function u(c){var s,l=(c=Qo(c)).length,f,d=!1,v;for(n==null&&(a=i(v=o())),s=0;s<=l;++s)!(s<l&&r(f=c[s],s,c))===d&&((d=!d)?a.lineStart():a.lineEnd()),d&&a.point(+e(f,s,c),+t(f,s,c));if(v)return a=null,v+""||null}return u.x=function(c){return arguments.length?(e=typeof c=="function"?c:Q(+c),u):e},u.y=function(c){return arguments.length?(t=typeof c=="function"?c:Q(+c),u):t},u.defined=function(c){return arguments.length?(r=typeof c=="function"?c:Q(!!c),u):r},u.curve=function(c){return arguments.length?(i=c,n!=null&&(a=i(n)),u):i},u.context=function(c){return arguments.length?(c==null?n=a=null:a=i(n=c),u):n},u}function jn(e,t,r){var n=null,i=Q(!0),a=null,o=Li,u=null,c=Jo(s);e=typeof e=="function"?e:e===void 0?td:Q(+e),t=typeof t=="function"?t:Q(t===void 0?0:+t),r=typeof r=="function"?r:r===void 0?rd:Q(+r);function s(f){var d,v,h,y=(f=Qo(f)).length,m,g=!1,b,w=new Array(y),P=new Array(y);for(a==null&&(u=o(b=c())),d=0;d<=y;++d){if(!(d<y&&i(m=f[d],d,f))===g)if(g=!g)v=d,u.areaStart(),u.lineStart();else{for(u.lineEnd(),u.lineStart(),h=d-1;h>=v;--h)u.point(w[h],P[h]);u.lineEnd(),u.areaEnd()}g&&(w[d]=+e(m,d,f),P[d]=+t(m,d,f),u.point(n?+n(m,d,f):w[d],r?+r(m,d,f):P[d]))}if(b)return u=null,b+""||null}function l(){return nd().defined(i).curve(o).context(a)}return s.x=function(f){return arguments.length?(e=typeof f=="function"?f:Q(+f),n=null,s):e},s.x0=function(f){return arguments.length?(e=typeof f=="function"?f:Q(+f),s):e},s.x1=function(f){return arguments.length?(n=f==null?null:typeof f=="function"?f:Q(+f),s):n},s.y=function(f){return arguments.length?(t=typeof f=="function"?f:Q(+f),r=null,s):t},s.y0=function(f){return arguments.length?(t=typeof f=="function"?f:Q(+f),s):t},s.y1=function(f){return arguments.length?(r=f==null?null:typeof f=="function"?f:Q(+f),s):r},s.lineX0=s.lineY0=function(){return l().x(e).y(t)},s.lineY1=function(){return l().x(e).y(r)},s.lineX1=function(){return l().x(n).y(t)},s.defined=function(f){return arguments.length?(i=typeof f=="function"?f:Q(!!f),s):i},s.curve=function(f){return arguments.length?(o=f,a!=null&&(u=o(a)),s):o},s.context=function(f){return arguments.length?(f==null?a=u=null:u=o(a=f),s):a},s}class id{constructor(t,r){this._context=t,this._x=r}areaStart(){this._line=0}areaEnd(){this._line=NaN}lineStart(){this._point=0}lineEnd(){(this._line||this._line!==0&&this._point===1)&&this._context.closePath(),this._line=1-this._line}point(t,r){switch(t=+t,r=+r,this._point){case 0:{this._point=1,this._line?this._context.lineTo(t,r):this._context.moveTo(t,r);break}case 1:this._point=2;default:{this._x?this._context.bezierCurveTo(this._x0=(this._x0+t)/2,this._y0,this._x0,r,t,r):this._context.bezierCurveTo(this._x0,this._y0=(this._y0+r)/2,t,this._y0,t,r);break}}this._x0=t,this._y0=r}}function jy(e){return new id(e,!0)}function Cy(e){return new id(e,!1)}const eu={draw(e,t){const r=st(t/qn);e.moveTo(r,0),e.arc(0,0,r,0,Ni)}},ky={draw(e,t){const r=st(t/5)/2;e.moveTo(-3*r,-r),e.lineTo(-r,-r),e.lineTo(-r,-3*r),e.lineTo(r,-3*r),e.lineTo(r,-r),e.lineTo(3*r,-r),e.lineTo(3*r,r),e.lineTo(r,r),e.lineTo(r,3*r),e.lineTo(-r,3*r),e.lineTo(-r,r),e.lineTo(-3*r,r),e.closePath()}},ad=st(1/3),My=ad*2,Dy={draw(e,t){const r=st(t/My),n=r*ad;e.moveTo(0,-r),e.lineTo(n,0),e.lineTo(0,r),e.lineTo(-n,0),e.closePath()}},Iy={draw(e,t){const r=st(t),n=-r/2;e.rect(n,n,r,r)}},$y=.8908130915292852,od=Un(qn/10)/Un(7*qn/10),Ny=Un(Ni/10)*od,Ly=-Jf(Ni/10)*od,Ry={draw(e,t){const r=st(t*$y),n=Ny*r,i=Ly*r;e.moveTo(0,-r),e.lineTo(n,i);for(let a=1;a<5;++a){const o=Ni*a/5,u=Jf(o),c=Un(o);e.lineTo(c*r,-u*r),e.lineTo(u*n-c*i,c*n+u*i)}e.closePath()}},Sa=st(3),By={draw(e,t){const r=-st(t/(Sa*3));e.moveTo(0,r*2),e.lineTo(-Sa*r,-r),e.lineTo(Sa*r,-r),e.closePath()}},Ve=-.5,Xe=st(3)/2,Ja=1/st(12),Ky=(Ja/2+1)*3,zy={draw(e,t){const r=st(t/Ky),n=r/2,i=r*Ja,a=n,o=r*Ja+r,u=-a,c=o;e.moveTo(n,i),e.lineTo(a,o),e.lineTo(u,c),e.lineTo(Ve*n-Xe*i,Xe*n+Ve*i),e.lineTo(Ve*a-Xe*o,Xe*a+Ve*o),e.lineTo(Ve*u-Xe*c,Xe*u+Ve*c),e.lineTo(Ve*n+Xe*i,Ve*i-Xe*n),e.lineTo(Ve*a+Xe*o,Ve*o-Xe*a),e.lineTo(Ve*u+Xe*c,Ve*c-Xe*u),e.closePath()}};function Fy(e,t){let r=null,n=Jo(i);e=typeof e=="function"?e:Q(e||eu),t=typeof t=="function"?t:Q(t===void 0?64:+t);function i(){let a;if(r||(r=a=n()),e.apply(this,arguments).draw(r,+t.apply(this,arguments)),a)return r=null,a+""||null}return i.type=function(a){return arguments.length?(e=typeof a=="function"?a:Q(a),i):e},i.size=function(a){return arguments.length?(t=typeof a=="function"?a:Q(+a),i):t},i.context=function(a){return arguments.length?(r=a??null,i):r},i}function Yn(){}function Hn(e,t,r){e._context.bezierCurveTo((2*e._x0+e._x1)/3,(2*e._y0+e._y1)/3,(e._x0+2*e._x1)/3,(e._y0+2*e._y1)/3,(e._x0+4*e._x1+t)/6,(e._y0+4*e._y1+r)/6)}function ud(e){this._context=e}ud.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=NaN,this._point=0},lineEnd:function(){switch(this._point){case 3:Hn(this,this._x1,this._y1);case 2:this._context.lineTo(this._x1,this._y1);break}(this._line||this._line!==0&&this._point===1)&&this._context.closePath(),this._line=1-this._line},point:function(e,t){switch(e=+e,t=+t,this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;break;case 2:this._point=3,this._context.lineTo((5*this._x0+this._x1)/6,(5*this._y0+this._y1)/6);default:Hn(this,e,t);break}this._x0=this._x1,this._x1=e,this._y0=this._y1,this._y1=t}};function Wy(e){return new ud(e)}function ld(e){this._context=e}ld.prototype={areaStart:Yn,areaEnd:Yn,lineStart:function(){this._x0=this._x1=this._x2=this._x3=this._x4=this._y0=this._y1=this._y2=this._y3=this._y4=NaN,this._point=0},lineEnd:function(){switch(this._point){case 1:{this._context.moveTo(this._x2,this._y2),this._context.closePath();break}case 2:{this._context.moveTo((this._x2+2*this._x3)/3,(this._y2+2*this._y3)/3),this._context.lineTo((this._x3+2*this._x2)/3,(this._y3+2*this._y2)/3),this._context.closePath();break}case 3:{this.point(this._x2,this._y2),this.point(this._x3,this._y3),this.point(this._x4,this._y4);break}}},point:function(e,t){switch(e=+e,t=+t,this._point){case 0:this._point=1,this._x2=e,this._y2=t;break;case 1:this._point=2,this._x3=e,this._y3=t;break;case 2:this._point=3,this._x4=e,this._y4=t,this._context.moveTo((this._x0+4*this._x1+e)/6,(this._y0+4*this._y1+t)/6);break;default:Hn(this,e,t);break}this._x0=this._x1,this._x1=e,this._y0=this._y1,this._y1=t}};function Uy(e){return new ld(e)}function cd(e){this._context=e}cd.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=NaN,this._point=0},lineEnd:function(){(this._line||this._line!==0&&this._point===3)&&this._context.closePath(),this._line=1-this._line},point:function(e,t){switch(e=+e,t=+t,this._point){case 0:this._point=1;break;case 1:this._point=2;break;case 2:this._point=3;var r=(this._x0+4*this._x1+e)/6,n=(this._y0+4*this._y1+t)/6;this._line?this._context.lineTo(r,n):this._context.moveTo(r,n);break;case 3:this._point=4;default:Hn(this,e,t);break}this._x0=this._x1,this._x1=e,this._y0=this._y1,this._y1=t}};function qy(e){return new cd(e)}function sd(e){this._context=e}sd.prototype={areaStart:Yn,areaEnd:Yn,lineStart:function(){this._point=0},lineEnd:function(){this._point&&this._context.closePath()},point:function(e,t){e=+e,t=+t,this._point?this._context.lineTo(e,t):(this._point=1,this._context.moveTo(e,t))}};function Yy(e){return new sd(e)}function nc(e){return e<0?-1:1}function ic(e,t,r){var n=e._x1-e._x0,i=t-e._x1,a=(e._y1-e._y0)/(n||i<0&&-0),o=(r-e._y1)/(i||n<0&&-0),u=(a*i+o*n)/(n+i);return(nc(a)+nc(o))*Math.min(Math.abs(a),Math.abs(o),.5*Math.abs(u))||0}function ac(e,t){var r=e._x1-e._x0;return r?(3*(e._y1-e._y0)/r-t)/2:t}function Ea(e,t,r){var n=e._x0,i=e._y0,a=e._x1,o=e._y1,u=(a-n)/3;e._context.bezierCurveTo(n+u,i+u*t,a-u,o-u*r,a,o)}function Gn(e){this._context=e}Gn.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=this._t0=NaN,this._point=0},lineEnd:function(){switch(this._point){case 2:this._context.lineTo(this._x1,this._y1);break;case 3:Ea(this,this._t0,ac(this,this._t0));break}(this._line||this._line!==0&&this._point===1)&&this._context.closePath(),this._line=1-this._line},point:function(e,t){var r=NaN;if(e=+e,t=+t,!(e===this._x1&&t===this._y1)){switch(this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;break;case 2:this._point=3,Ea(this,ac(this,r=ic(this,e,t)),r);break;default:Ea(this,this._t0,r=ic(this,e,t));break}this._x0=this._x1,this._x1=e,this._y0=this._y1,this._y1=t,this._t0=r}}};function fd(e){this._context=new dd(e)}(fd.prototype=Object.create(Gn.prototype)).point=function(e,t){Gn.prototype.point.call(this,t,e)};function dd(e){this._context=e}dd.prototype={moveTo:function(e,t){this._context.moveTo(t,e)},closePath:function(){this._context.closePath()},lineTo:function(e,t){this._context.lineTo(t,e)},bezierCurveTo:function(e,t,r,n,i,a){this._context.bezierCurveTo(t,e,n,r,a,i)}};function Hy(e){return new Gn(e)}function Gy(e){return new fd(e)}function vd(e){this._context=e}vd.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x=[],this._y=[]},lineEnd:function(){var e=this._x,t=this._y,r=e.length;if(r)if(this._line?this._context.lineTo(e[0],t[0]):this._context.moveTo(e[0],t[0]),r===2)this._context.lineTo(e[1],t[1]);else for(var n=oc(e),i=oc(t),a=0,o=1;o<r;++a,++o)this._context.bezierCurveTo(n[0][a],i[0][a],n[1][a],i[1][a],e[o],t[o]);(this._line||this._line!==0&&r===1)&&this._context.closePath(),this._line=1-this._line,this._x=this._y=null},point:function(e,t){this._x.push(+e),this._y.push(+t)}};function oc(e){var t,r=e.length-1,n,i=new Array(r),a=new Array(r),o=new Array(r);for(i[0]=0,a[0]=2,o[0]=e[0]+2*e[1],t=1;t<r-1;++t)i[t]=1,a[t]=4,o[t]=4*e[t]+2*e[t+1];for(i[r-1]=2,a[r-1]=7,o[r-1]=8*e[r-1]+e[r],t=1;t<r;++t)n=i[t]/a[t-1],a[t]-=n,o[t]-=n*o[t-1];for(i[r-1]=o[r-1]/a[r-1],t=r-2;t>=0;--t)i[t]=(o[t]-i[t+1])/a[t];for(a[r-1]=(e[r]+i[r-1])/2,t=0;t<r-1;++t)a[t]=2*e[t+1]-i[t+1];return[i,a]}function Vy(e){return new vd(e)}function Ri(e,t){this._context=e,this._t=t}Ri.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x=this._y=NaN,this._point=0},lineEnd:function(){0<this._t&&this._t<1&&this._point===2&&this._context.lineTo(this._x,this._y),(this._line||this._line!==0&&this._point===1)&&this._context.closePath(),this._line>=0&&(this._t=1-this._t,this._line=1-this._line)},point:function(e,t){switch(e=+e,t=+t,this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;default:{if(this._t<=0)this._context.lineTo(this._x,t),this._context.lineTo(e,t);else{var r=this._x*(1-this._t)+e*this._t;this._context.lineTo(r,this._y),this._context.lineTo(r,t)}break}}this._x=e,this._y=t}};function Xy(e){return new Ri(e,.5)}function Zy(e){return new Ri(e,0)}function Jy(e){return new Ri(e,1)}function Ar(e,t){if((o=e.length)>1)for(var r=1,n,i,a=e[t[0]],o,u=a.length;r<o;++r)for(i=a,a=e[t[r]],n=0;n<u;++n)a[n][1]+=a[n][0]=isNaN(i[n][1])?i[n][0]:i[n][1]}function Qa(e){for(var t=e.length,r=new Array(t);--t>=0;)r[t]=t;return r}function Qy(e,t){return e[t]}function eg(e){const t=[];return t.key=e,t}function tg(){var e=Q([]),t=Qa,r=Ar,n=Qy;function i(a){var o=Array.from(e.apply(this,arguments),eg),u,c=o.length,s=-1,l;for(const f of a)for(u=0,++s;u<c;++u)(o[u][s]=[0,+n(f,o[u].key,s,a)]).data=f;for(u=0,l=Qo(t(o));u<c;++u)o[l[u]].index=u;return r(o,l),o}return i.keys=function(a){return arguments.length?(e=typeof a=="function"?a:Q(Array.from(a)),i):e},i.value=function(a){return arguments.length?(n=typeof a=="function"?a:Q(+a),i):n},i.order=function(a){return arguments.length?(t=a==null?Qa:typeof a=="function"?a:Q(Array.from(a)),i):t},i.offset=function(a){return arguments.length?(r=a??Ar,i):r},i}function rg(e,t){if((n=e.length)>0){for(var r,n,i=0,a=e[0].length,o;i<a;++i){for(o=r=0;r<n;++r)o+=e[r][i][1]||0;if(o)for(r=0;r<n;++r)e[r][i][1]/=o}Ar(e,t)}}function ng(e,t){if((i=e.length)>0){for(var r=0,n=e[t[0]],i,a=n.length;r<a;++r){for(var o=0,u=0;o<i;++o)u+=e[o][r][1]||0;n[r][1]+=n[r][0]=-u/2}Ar(e,t)}}function ig(e,t){if(!(!((o=e.length)>0)||!((a=(i=e[t[0]]).length)>0))){for(var r=0,n=1,i,a,o;n<a;++n){for(var u=0,c=0,s=0;u<o;++u){for(var l=e[t[u]],f=l[n][1]||0,d=l[n-1][1]||0,v=(f-d)/2,h=0;h<u;++h){var y=e[t[h]],m=y[n][1]||0,g=y[n-1][1]||0;v+=m-g}c+=f,s+=v*f}i[n-1][1]+=i[n-1][0]=r,c&&(r-=s/c)}i[n-1][1]+=i[n-1][0]=r,Ar(e,t)}}var ag=["type","size","sizeType"];function eo(){return eo=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},eo.apply(null,arguments)}function uc(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function lc(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?uc(Object(r),!0).forEach(function(n){og(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):uc(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function og(e,t,r){return(t=ug(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function ug(e){var t=lg(e,"string");return typeof t=="symbol"?t:t+""}function lg(e,t){if(typeof e!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t||"default");if(typeof n!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function cg(e,t){if(e==null)return{};var r,n,i=sg(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],t.indexOf(r)===-1&&{}.propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}function sg(e,t){if(e==null)return{};var r={};for(var n in e)if({}.hasOwnProperty.call(e,n)){if(t.indexOf(n)!==-1)continue;r[n]=e[n]}return r}var hd={symbolCircle:eu,symbolCross:ky,symbolDiamond:Dy,symbolSquare:Iy,symbolStar:Ry,symbolTriangle:By,symbolWye:zy},fg=Math.PI/180,dg=e=>{var t="symbol".concat(hn(e));return hd[t]||eu},vg=(e,t,r)=>{if(t==="area")return e;switch(r){case"cross":return 5*e*e/9;case"diamond":return .5*e*e/Math.sqrt(3);case"square":return e*e;case"star":{var n=18*fg;return 1.25*e*e*(Math.tan(n)-Math.tan(n*2)*Math.tan(n)**2)}case"triangle":return Math.sqrt(3)*e*e/4;case"wye":return(21-10*Math.sqrt(3))*e*e/8;default:return Math.PI*e*e/4}},hg=(e,t)=>{hd["symbol".concat(hn(e))]=t},tu=e=>{var{type:t="circle",size:r=64,sizeType:n="area"}=e,i=cg(e,ag),a=lc(lc({},i),{},{type:t,size:r,sizeType:n}),o=()=>{var f=dg(t),d=Fy().type(f).size(vg(r,n,t));return d()},{className:u,cx:c,cy:s}=a,l=z(a,!0);return c===+c&&s===+s&&r===+r?p.createElement("path",eo({},l,{className:U("recharts-symbols",u),transform:"translate(".concat(c,", ").concat(s,")"),d:o()})):null};tu.registerSymbol=hg;function to(){return to=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},to.apply(null,arguments)}function cc(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function pg(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?cc(Object(r),!0).forEach(function(n){ru(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):cc(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function ru(e,t,r){return(t=mg(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function mg(e){var t=yg(e,"string");return typeof t=="symbol"?t:t+""}function yg(e,t){if(typeof e!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t||"default");if(typeof n!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var Ze=32;class nu extends p.PureComponent{renderIcon(t,r){var{inactiveColor:n}=this.props,i=Ze/2,a=Ze/6,o=Ze/3,u=t.inactive?n:t.color,c=r??t.type;if(c==="none")return null;if(c==="plainline")return p.createElement("line",{strokeWidth:4,fill:"none",stroke:u,strokeDasharray:t.payload.strokeDasharray,x1:0,y1:i,x2:Ze,y2:i,className:"recharts-legend-icon"});if(c==="line")return p.createElement("path",{strokeWidth:4,fill:"none",stroke:u,d:"M0,".concat(i,"h").concat(o,`
            A`).concat(a,",").concat(a,",0,1,1,").concat(2*o,",").concat(i,`
            H`).concat(Ze,"M").concat(2*o,",").concat(i,`
            A`).concat(a,",").concat(a,",0,1,1,").concat(o,",").concat(i),className:"recharts-legend-icon"});if(c==="rect")return p.createElement("path",{stroke:"none",fill:u,d:"M0,".concat(Ze/8,"h").concat(Ze,"v").concat(Ze*3/4,"h").concat(-Ze,"z"),className:"recharts-legend-icon"});if(p.isValidElement(t.legendIcon)){var s=pg({},t);return delete s.legendIcon,p.cloneElement(t.legendIcon,s)}return p.createElement(tu,{fill:u,cx:i,cy:i,size:Ze,sizeType:"diameter",type:c})}renderItems(){var{payload:t,iconSize:r,layout:n,formatter:i,inactiveColor:a,iconType:o}=this.props,u={x:0,y:0,width:Ze,height:Ze},c={display:n==="horizontal"?"inline-block":"block",marginRight:10},s={display:"inline-block",verticalAlign:"middle",marginRight:4};return t.map((l,f)=>{var d=l.formatter||i,v=U({"recharts-legend-item":!0,["legend-item-".concat(f)]:!0,inactive:l.inactive});if(l.type==="none")return null;var h=l.inactive?a:l.color,y=d?d(l.value,l,f):l.value;return p.createElement("li",to({className:v,style:c,key:"legend-item-".concat(f)},pn(this.props,l,f)),p.createElement(Zo,{width:r,height:r,viewBox:u,style:s,"aria-label":"".concat(y," legend icon")},this.renderIcon(l,o)),p.createElement("span",{className:"recharts-legend-item-text",style:{color:h}},y))})}render(){var{payload:t,layout:r,align:n}=this.props;if(!t||!t.length)return null;var i={padding:0,margin:0,textAlign:r==="horizontal"?n:"left"};return p.createElement("ul",{className:"recharts-default-legend",style:i},this.renderItems())}}ru(nu,"displayName","Legend");ru(nu,"defaultProps",{align:"center",iconSize:14,inactiveColor:"#ccc",layout:"horizontal",verticalAlign:"middle"});var pd={},md={};(function(e){Object.defineProperty(e,Symbol.toStringTag,{value:"Module"});function t(r,n){const i=new Map;for(let a=0;a<r.length;a++){const o=r[a],u=n(o);i.has(u)||i.set(u,o)}return Array.from(i.values())}e.uniqBy=t})(md);var iu={};(function(e){Object.defineProperty(e,Symbol.toStringTag,{value:"Module"});function t(r){return r}e.identity=t})(iu);var yd={},Bi={},gd={};(function(e){Object.defineProperty(e,Symbol.toStringTag,{value:"Module"});function t(r){return Number.isSafeInteger(r)&&r>=0}e.isLength=t})(gd);(function(e){Object.defineProperty(e,Symbol.toStringTag,{value:"Module"});const t=gd;function r(n){return n!=null&&typeof n!="function"&&t.isLength(n.length)}e.isArrayLike=r})(Bi);var bd={};(function(e){Object.defineProperty(e,Symbol.toStringTag,{value:"Module"});function t(r){return typeof r=="object"&&r!==null}e.isObjectLike=t})(bd);(function(e){Object.defineProperty(e,Symbol.toStringTag,{value:"Module"});const t=Bi,r=bd;function n(i){return r.isObjectLike(i)&&t.isArrayLike(i)}e.isArrayLikeObject=n})(yd);var wd={},xd={};(function(e){Object.defineProperty(e,Symbol.toStringTag,{value:"Module"});const t=Ai;function r(n){return function(i){return t.get(i,n)}}e.property=r})(xd);var Pd={},_a={},Ta={},au={};(function(e){Object.defineProperty(e,Symbol.toStringTag,{value:"Module"});function t(r){return r!==null&&(typeof r=="object"||typeof r=="function")}e.isObject=t})(au);var ou={};(function(e){Object.defineProperty(e,Symbol.toStringTag,{value:"Module"});function t(r){return r==null||typeof r!="object"&&typeof r!="function"}e.isPrimitive=t})(ou);var Ki={};(function(e){Object.defineProperty(e,Symbol.toStringTag,{value:"Module"});function t(r,n){return r===n||Number.isNaN(r)&&Number.isNaN(n)}e.eq=t})(Ki);var sc;function gg(){return sc||(sc=1,function(e){Object.defineProperty(e,Symbol.toStringTag,{value:"Module"});const t=uu(),r=au,n=ou,i=Ki;function a(f,d,v){return typeof v!="function"?t.isMatch(f,d):o(f,d,function h(y,m,g,b,w,P){const x=v(y,m,g,b,w,P);return x!==void 0?!!x:o(y,m,h,P)},new Map)}function o(f,d,v,h){if(d===f)return!0;switch(typeof d){case"object":return u(f,d,v,h);case"function":return Object.keys(d).length>0?o(f,{...d},v,h):i.eq(f,d);default:return r.isObject(f)?typeof d=="string"?d==="":!0:i.eq(f,d)}}function u(f,d,v,h){if(d==null)return!0;if(Array.isArray(d))return s(f,d,v,h);if(d instanceof Map)return c(f,d,v,h);if(d instanceof Set)return l(f,d,v,h);const y=Object.keys(d);if(f==null)return y.length===0;if(y.length===0)return!0;if(h&&h.has(d))return h.get(d)===f;h&&h.set(d,f);try{for(let m=0;m<y.length;m++){const g=y[m];if(!n.isPrimitive(f)&&!(g in f)||d[g]===void 0&&f[g]!==void 0||d[g]===null&&f[g]!==null||!v(f[g],d[g],g,f,d,h))return!1}return!0}finally{h&&h.delete(d)}}function c(f,d,v,h){if(d.size===0)return!0;if(!(f instanceof Map))return!1;for(const[y,m]of d.entries()){const g=f.get(y);if(v(g,m,y,f,d,h)===!1)return!1}return!0}function s(f,d,v,h){if(d.length===0)return!0;if(!Array.isArray(f))return!1;const y=new Set;for(let m=0;m<d.length;m++){const g=d[m];let b=!1;for(let w=0;w<f.length;w++){if(y.has(w))continue;const P=f[w];let x=!1;if(v(P,g,m,f,d,h)&&(x=!0),x){y.add(w),b=!0;break}}if(!b)return!1}return!0}function l(f,d,v,h){return d.size===0?!0:f instanceof Set?s([...f],[...d],v,h):!1}e.isMatchWith=a,e.isSetMatch=l}(Ta)),Ta}var fc;function uu(){return fc||(fc=1,function(e){Object.defineProperty(e,Symbol.toStringTag,{value:"Module"});const t=gg();function r(n,i){return t.isMatchWith(n,i,()=>{})}e.isMatch=r}(_a)),_a}var Od={},lu={},cu={};(function(e){Object.defineProperty(e,Symbol.toStringTag,{value:"Module"});function t(r){return Object.getOwnPropertySymbols(r).filter(n=>Object.prototype.propertyIsEnumerable.call(r,n))}e.getSymbols=t})(cu);var zi={};(function(e){Object.defineProperty(e,Symbol.toStringTag,{value:"Module"});function t(r){return r==null?r===void 0?"[object Undefined]":"[object Null]":Object.prototype.toString.call(r)}e.getTag=t})(zi);var Fi={};(function(e){Object.defineProperty(e,Symbol.toStringTag,{value:"Module"});const t="[object RegExp]",r="[object String]",n="[object Number]",i="[object Boolean]",a="[object Arguments]",o="[object Symbol]",u="[object Date]",c="[object Map]",s="[object Set]",l="[object Array]",f="[object Function]",d="[object ArrayBuffer]",v="[object Object]",h="[object Error]",y="[object DataView]",m="[object Uint8Array]",g="[object Uint8ClampedArray]",b="[object Uint16Array]",w="[object Uint32Array]",P="[object BigUint64Array]",x="[object Int8Array]",O="[object Int16Array]",A="[object Int32Array]",E="[object BigInt64Array]",_="[object Float32Array]",D="[object Float64Array]";e.argumentsTag=a,e.arrayBufferTag=d,e.arrayTag=l,e.bigInt64ArrayTag=E,e.bigUint64ArrayTag=P,e.booleanTag=i,e.dataViewTag=y,e.dateTag=u,e.errorTag=h,e.float32ArrayTag=_,e.float64ArrayTag=D,e.functionTag=f,e.int16ArrayTag=O,e.int32ArrayTag=A,e.int8ArrayTag=x,e.mapTag=c,e.numberTag=n,e.objectTag=v,e.regexpTag=t,e.setTag=s,e.stringTag=r,e.symbolTag=o,e.uint16ArrayTag=b,e.uint32ArrayTag=w,e.uint8ArrayTag=m,e.uint8ClampedArrayTag=g})(Fi);var Ad={};(function(e){Object.defineProperty(e,Symbol.toStringTag,{value:"Module"});function t(r){return ArrayBuffer.isView(r)&&!(r instanceof DataView)}e.isTypedArray=t})(Ad);(function(e){Object.defineProperty(e,Symbol.toStringTag,{value:"Module"});const t=cu,r=zi,n=Fi,i=ou,a=Ad;function o(l,f){return u(l,void 0,l,new Map,f)}function u(l,f,d,v=new Map,h=void 0){const y=h==null?void 0:h(l,f,d,v);if(y!=null)return y;if(i.isPrimitive(l))return l;if(v.has(l))return v.get(l);if(Array.isArray(l)){const m=new Array(l.length);v.set(l,m);for(let g=0;g<l.length;g++)m[g]=u(l[g],g,d,v,h);return Object.hasOwn(l,"index")&&(m.index=l.index),Object.hasOwn(l,"input")&&(m.input=l.input),m}if(l instanceof Date)return new Date(l.getTime());if(l instanceof RegExp){const m=new RegExp(l.source,l.flags);return m.lastIndex=l.lastIndex,m}if(l instanceof Map){const m=new Map;v.set(l,m);for(const[g,b]of l)m.set(g,u(b,g,d,v,h));return m}if(l instanceof Set){const m=new Set;v.set(l,m);for(const g of l)m.add(u(g,void 0,d,v,h));return m}if(typeof Buffer<"u"&&Buffer.isBuffer(l))return l.subarray();if(a.isTypedArray(l)){const m=new(Object.getPrototypeOf(l)).constructor(l.length);v.set(l,m);for(let g=0;g<l.length;g++)m[g]=u(l[g],g,d,v,h);return m}if(l instanceof ArrayBuffer||typeof SharedArrayBuffer<"u"&&l instanceof SharedArrayBuffer)return l.slice(0);if(l instanceof DataView){const m=new DataView(l.buffer.slice(0),l.byteOffset,l.byteLength);return v.set(l,m),c(m,l,d,v,h),m}if(typeof File<"u"&&l instanceof File){const m=new File([l],l.name,{type:l.type});return v.set(l,m),c(m,l,d,v,h),m}if(l instanceof Blob){const m=new Blob([l],{type:l.type});return v.set(l,m),c(m,l,d,v,h),m}if(l instanceof Error){const m=new l.constructor;return v.set(l,m),m.message=l.message,m.name=l.name,m.stack=l.stack,m.cause=l.cause,c(m,l,d,v,h),m}if(typeof l=="object"&&s(l)){const m=Object.create(Object.getPrototypeOf(l));return v.set(l,m),c(m,l,d,v,h),m}return l}function c(l,f,d=l,v,h){const y=[...Object.keys(f),...t.getSymbols(f)];for(let m=0;m<y.length;m++){const g=y[m],b=Object.getOwnPropertyDescriptor(l,g);(b==null||b.writable)&&(l[g]=u(f[g],g,d,v,h))}}function s(l){switch(r.getTag(l)){case n.argumentsTag:case n.arrayTag:case n.arrayBufferTag:case n.dataViewTag:case n.booleanTag:case n.dateTag:case n.float32ArrayTag:case n.float64ArrayTag:case n.int8ArrayTag:case n.int16ArrayTag:case n.int32ArrayTag:case n.mapTag:case n.numberTag:case n.objectTag:case n.regexpTag:case n.setTag:case n.stringTag:case n.symbolTag:case n.uint8ArrayTag:case n.uint8ClampedArrayTag:case n.uint16ArrayTag:case n.uint32ArrayTag:return!0;default:return!1}}e.cloneDeepWith=o,e.cloneDeepWithImpl=u,e.copyProperties=c})(lu);(function(e){Object.defineProperty(e,Symbol.toStringTag,{value:"Module"});const t=lu;function r(n){return t.cloneDeepWithImpl(n,void 0,n,new Map,void 0)}e.cloneDeep=r})(Od);(function(e){Object.defineProperty(e,Symbol.toStringTag,{value:"Module"});const t=uu(),r=Od;function n(i){return i=r.cloneDeep(i),a=>t.isMatch(a,i)}e.matches=n})(Pd);var Sd={},Ed={},_d={};(function(e){Object.defineProperty(e,Symbol.toStringTag,{value:"Module"});const t=lu,r=Fi;function n(i,a){return t.cloneDeepWith(i,(o,u,c,s)=>{const l=a==null?void 0:a(o,u,c,s);if(l!=null)return l;if(typeof i=="object")switch(Object.prototype.toString.call(i)){case r.numberTag:case r.stringTag:case r.booleanTag:{const f=new i.constructor(i==null?void 0:i.valueOf());return t.copyProperties(f,i),f}case r.argumentsTag:{const f={};return t.copyProperties(f,i),f.length=i.length,f[Symbol.iterator]=i[Symbol.iterator],f}default:return}})}e.cloneDeepWith=n})(_d);(function(e){Object.defineProperty(e,Symbol.toStringTag,{value:"Module"});const t=_d;function r(n){return t.cloneDeepWith(n)}e.cloneDeep=r})(Ed);var Td={},su={};(function(e){Object.defineProperty(e,Symbol.toStringTag,{value:"Module"});const t=/^(?:0|[1-9]\d*)$/;function r(n,i=Number.MAX_SAFE_INTEGER){switch(typeof n){case"number":return Number.isInteger(n)&&n>=0&&n<i;case"symbol":return!1;case"string":return t.test(n)}}e.isIndex=r})(su);var jd={};(function(e){Object.defineProperty(e,Symbol.toStringTag,{value:"Module"});const t=zi;function r(n){return n!==null&&typeof n=="object"&&t.getTag(n)==="[object Arguments]"}e.isArguments=r})(jd);(function(e){Object.defineProperty(e,Symbol.toStringTag,{value:"Module"});const t=qo,r=su,n=jd,i=Si;function a(o,u){let c;if(Array.isArray(u)?c=u:typeof u=="string"&&t.isDeepKey(u)&&(o==null?void 0:o[u])==null?c=i.toPath(u):c=[u],c.length===0)return!1;let s=o;for(let l=0;l<c.length;l++){const f=c[l];if((s==null||!Object.hasOwn(s,f))&&!((Array.isArray(s)||n.isArguments(s))&&r.isIndex(f)&&f<s.length))return!1;s=s[f]}return!0}e.has=a})(Td);(function(e){Object.defineProperty(e,Symbol.toStringTag,{value:"Module"});const t=uu(),r=Yo,n=Ed,i=Ai,a=Td;function o(u,c){switch(typeof u){case"object":{Object.is(u==null?void 0:u.valueOf(),-0)&&(u="-0");break}case"number":{u=r.toKey(u);break}}return c=n.cloneDeep(c),function(s){const l=i.get(s,u);return l===void 0?a.has(s,u):c===void 0?l===void 0:t.isMatch(l,c)}}e.matchesProperty=o})(Sd);(function(e){Object.defineProperty(e,Symbol.toStringTag,{value:"Module"});const t=iu,r=xd,n=Pd,i=Sd;function a(o){if(o==null)return t.identity;switch(typeof o){case"function":return o;case"object":return Array.isArray(o)&&o.length===2?i.matchesProperty(o[0],o[1]):n.matches(o);case"string":case"symbol":case"number":return r.property(o)}}e.iteratee=a})(wd);(function(e){Object.defineProperty(e,Symbol.toStringTag,{value:"Module"});const t=md,r=iu,n=yd,i=wd;function a(o,u=r.identity){return n.isArrayLikeObject(o)?t.uniqBy(Array.from(o),i.iteratee(u)):[]}e.uniqBy=a})(pd);var bg=pd.uniqBy;const dc=Et(bg);function Cd(e,t,r){return t===!0?dc(e,r):typeof t=="function"?dc(e,t):e}var kd={exports:{}},Md={},Dd={exports:{}},Id={};/**
 * @license React
 * use-sync-external-store-shim.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Sr=p;function wg(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var xg=typeof Object.is=="function"?Object.is:wg,Pg=Sr.useState,Og=Sr.useEffect,Ag=Sr.useLayoutEffect,Sg=Sr.useDebugValue;function Eg(e,t){var r=t(),n=Pg({inst:{value:r,getSnapshot:t}}),i=n[0].inst,a=n[1];return Ag(function(){i.value=r,i.getSnapshot=t,ja(i)&&a({inst:i})},[e,r,t]),Og(function(){return ja(i)&&a({inst:i}),e(function(){ja(i)&&a({inst:i})})},[e]),Sg(r),r}function ja(e){var t=e.getSnapshot;e=e.value;try{var r=t();return!xg(e,r)}catch{return!0}}function _g(e,t){return t()}var Tg=typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"?_g:Eg;Id.useSyncExternalStore=Sr.useSyncExternalStore!==void 0?Sr.useSyncExternalStore:Tg;Dd.exports=Id;var jg=Dd.exports;/**
 * @license React
 * use-sync-external-store-shim/with-selector.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Wi=p,Cg=jg;function kg(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var Mg=typeof Object.is=="function"?Object.is:kg,Dg=Cg.useSyncExternalStore,Ig=Wi.useRef,$g=Wi.useEffect,Ng=Wi.useMemo,Lg=Wi.useDebugValue;Md.useSyncExternalStoreWithSelector=function(e,t,r,n,i){var a=Ig(null);if(a.current===null){var o={hasValue:!1,value:null};a.current=o}else o=a.current;a=Ng(function(){function c(v){if(!s){if(s=!0,l=v,v=n(v),i!==void 0&&o.hasValue){var h=o.value;if(i(h,v))return f=h}return f=v}if(h=f,Mg(l,v))return h;var y=n(v);return i!==void 0&&i(h,y)?(l=v,h):(l=v,f=y)}var s=!1,l,f,d=r===void 0?null:r;return[function(){return c(t())},d===null?void 0:function(){return c(d())}]},[t,r,n,i]);var u=Dg(e,a[0],a[1]);return $g(function(){o.hasValue=!0,o.value=u},[u]),Lg(u),u};kd.exports=Md;var Rg=kd.exports,fu=p.createContext(null),Bg=e=>e,ne=()=>{var e=p.useContext(fu);return e?e.store.dispatch:Bg},Wn=()=>{},Kg=()=>Wn,zg=(e,t)=>e===t;function $(e){var t=p.useContext(fu);return Rg.useSyncExternalStoreWithSelector(t?t.subscription.addNestedSub:Kg,t?t.store.getState:Wn,t?t.store.getState:Wn,t?e:Wn,zg)}function Fg(e,t=`expected a function, instead received ${typeof e}`){if(typeof e!="function")throw new TypeError(t)}function Wg(e,t=`expected an object, instead received ${typeof e}`){if(typeof e!="object")throw new TypeError(t)}function Ug(e,t="expected all items to be functions, instead received the following types: "){if(!e.every(r=>typeof r=="function")){const r=e.map(n=>typeof n=="function"?`function ${n.name||"unnamed"}()`:typeof n).join(", ");throw new TypeError(`${t}[${r}]`)}}var vc=e=>Array.isArray(e)?e:[e];function qg(e){const t=Array.isArray(e[0])?e[0]:e;return Ug(t,"createSelector expects all input-selectors to be functions, but received the following types: "),t}function Yg(e,t){const r=[],{length:n}=e;for(let i=0;i<n;i++)r.push(e[i].apply(null,t));return r}var Hg=class{constructor(e){this.value=e}deref(){return this.value}},Gg=typeof WeakRef<"u"?WeakRef:Hg,Vg=0,hc=1;function Cn(){return{s:Vg,v:void 0,o:null,p:null}}function $d(e,t={}){let r=Cn();const{resultEqualityCheck:n}=t;let i,a=0;function o(){var f;let u=r;const{length:c}=arguments;for(let d=0,v=c;d<v;d++){const h=arguments[d];if(typeof h=="function"||typeof h=="object"&&h!==null){let y=u.o;y===null&&(u.o=y=new WeakMap);const m=y.get(h);m===void 0?(u=Cn(),y.set(h,u)):u=m}else{let y=u.p;y===null&&(u.p=y=new Map);const m=y.get(h);m===void 0?(u=Cn(),y.set(h,u)):u=m}}const s=u;let l;if(u.s===hc)l=u.v;else if(l=e.apply(null,arguments),a++,n){const d=((f=i==null?void 0:i.deref)==null?void 0:f.call(i))??i;d!=null&&n(d,l)&&(l=d,a!==0&&a--),i=typeof l=="object"&&l!==null||typeof l=="function"?new Gg(l):l}return s.s=hc,s.v=l,l}return o.clearCache=()=>{r=Cn(),o.resetResultsCount()},o.resultsCount=()=>a,o.resetResultsCount=()=>{a=0},o}function Xg(e,...t){const r=typeof e=="function"?{memoize:e,memoizeOptions:t}:e,n=(...i)=>{let a=0,o=0,u,c={},s=i.pop();typeof s=="object"&&(c=s,s=i.pop()),Fg(s,`createSelector expects an output function after the inputs, but received: [${typeof s}]`);const l={...r,...c},{memoize:f,memoizeOptions:d=[],argsMemoize:v=$d,argsMemoizeOptions:h=[],devModeChecks:y={}}=l,m=vc(d),g=vc(h),b=qg(i),w=f(function(){return a++,s.apply(null,arguments)},...m),P=v(function(){o++;const O=Yg(b,arguments);return u=w.apply(null,O),u},...g);return Object.assign(P,{resultFunc:s,memoizedResultFunc:w,dependencies:b,dependencyRecomputations:()=>o,resetDependencyRecomputations:()=>{o=0},lastResult:()=>u,recomputations:()=>a,resetRecomputations:()=>{a=0},memoize:f,argsMemoize:v})};return Object.assign(n,{withTypes:()=>n}),n}var S=Xg($d),Zg=Object.assign((e,t=S)=>{Wg(e,`createStructuredSelector expects first argument to be an object where each property is a selector, instead received a ${typeof e}`);const r=Object.keys(e),n=r.map(a=>e[a]);return t(n,(...a)=>a.reduce((o,u,c)=>(o[r[c]]=u,o),{}))},{withTypes:()=>Zg}),Nd={},Ld={},Rd={};(function(e){Object.defineProperty(e,Symbol.toStringTag,{value:"Module"});function t(n){return typeof n=="symbol"?1:n===null?2:n===void 0?3:n!==n?4:0}const r=(n,i,a)=>{if(n!==i){const o=t(n),u=t(i);if(o===u&&o===0){if(n<i)return a==="desc"?1:-1;if(n>i)return a==="desc"?-1:1}return a==="desc"?u-o:o-u}return 0};e.compareValues=r})(Rd);var Bd={},du={};(function(e){Object.defineProperty(e,Symbol.toStringTag,{value:"Module"});function t(r){return typeof r=="symbol"||r instanceof Symbol}e.isSymbol=t})(du);(function(e){Object.defineProperty(e,Symbol.toStringTag,{value:"Module"});const t=du,r=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,n=/^\w*$/;function i(a,o){return Array.isArray(a)?!1:typeof a=="number"||typeof a=="boolean"||a==null||t.isSymbol(a)?!0:typeof a=="string"&&(n.test(a)||!r.test(a))||o!=null&&Object.hasOwn(o,a)}e.isKey=i})(Bd);(function(e){Object.defineProperty(e,Symbol.toStringTag,{value:"Module"});const t=Rd,r=Bd,n=Si;function i(a,o,u,c){if(a==null)return[];u=c?void 0:u,Array.isArray(a)||(a=Object.values(a)),Array.isArray(o)||(o=o==null?[null]:[o]),o.length===0&&(o=[null]),Array.isArray(u)||(u=u==null?[]:[u]),u=u.map(v=>String(v));const s=(v,h)=>{let y=v;for(let m=0;m<h.length&&y!=null;++m)y=y[h[m]];return y},l=(v,h)=>h==null||v==null?h:typeof v=="object"&&"key"in v?Object.hasOwn(h,v.key)?h[v.key]:s(h,v.path):typeof v=="function"?v(h):Array.isArray(v)?s(h,v):typeof h=="object"?h[v]:h,f=o.map(v=>(Array.isArray(v)&&v.length===1&&(v=v[0]),v==null||typeof v=="function"||Array.isArray(v)||r.isKey(v)?v:{key:v,path:n.toPath(v)}));return a.map(v=>({original:v,criteria:f.map(h=>l(h,v))})).slice().sort((v,h)=>{for(let y=0;y<f.length;y++){const m=t.compareValues(v.criteria[y],h.criteria[y],u[y]);if(m!==0)return m}return 0}).map(v=>v.original)}e.orderBy=i})(Ld);var Kd={};(function(e){Object.defineProperty(e,Symbol.toStringTag,{value:"Module"});function t(r,n=1){const i=[],a=Math.floor(n),o=(u,c)=>{for(let s=0;s<u.length;s++){const l=u[s];Array.isArray(l)&&c<a?o(l,c+1):i.push(l)}};return o(r,0),i}e.flatten=t})(Kd);var vu={};(function(e){Object.defineProperty(e,Symbol.toStringTag,{value:"Module"});const t=su,r=Bi,n=au,i=Ki;function a(o,u,c){return n.isObject(c)&&(typeof u=="number"&&r.isArrayLike(c)&&t.isIndex(u)&&u<c.length||typeof u=="string"&&u in c)?i.eq(c[u],o):!1}e.isIterateeCall=a})(vu);(function(e){Object.defineProperty(e,Symbol.toStringTag,{value:"Module"});const t=Ld,r=Kd,n=vu;function i(a,...o){const u=o.length;return u>1&&n.isIterateeCall(a,o[0],o[1])?o=[]:u>2&&n.isIterateeCall(o[0],o[1],o[2])&&(o=[o[0]]),t.orderBy(a,r.flatten(o),["asc"])}e.sortBy=i})(Nd);var Jg=Nd.sortBy;const Ui=Et(Jg);var zd=e=>e.legend.settings,Qg=e=>e.legend.size,e0=e=>e.legend.payload,t0=S([e0,zd],(e,t)=>{var{itemSorter:r}=t,n=e.flat(1);return r?Ui(n,r):n});function r0(){return $(t0)}var kn=1;function Fd(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[],[t,r]=p.useState({height:0,left:0,top:0,width:0}),n=p.useCallback(i=>{if(i!=null){var a=i.getBoundingClientRect(),o={height:a.height,left:a.left,top:a.top,width:a.width};(Math.abs(o.height-t.height)>kn||Math.abs(o.left-t.left)>kn||Math.abs(o.top-t.top)>kn||Math.abs(o.width-t.width)>kn)&&r({height:o.height,left:o.left,top:o.top,width:o.width})}},[t.width,t.height,t.top,t.left,...e]);return[t,n]}function xe(e){return`Minified Redux error #${e}; visit https://redux.js.org/Errors?code=${e} for the full message or use the non-minified dev environment for full errors. `}var n0=(()=>typeof Symbol=="function"&&Symbol.observable||"@@observable")(),pc=n0,Ca=()=>Math.random().toString(36).substring(7).split("").join("."),i0={INIT:`@@redux/INIT${Ca()}`,REPLACE:`@@redux/REPLACE${Ca()}`,PROBE_UNKNOWN_ACTION:()=>`@@redux/PROBE_UNKNOWN_ACTION${Ca()}`},Vn=i0;function hu(e){if(typeof e!="object"||e===null)return!1;let t=e;for(;Object.getPrototypeOf(t)!==null;)t=Object.getPrototypeOf(t);return Object.getPrototypeOf(e)===t||Object.getPrototypeOf(e)===null}function Wd(e,t,r){if(typeof e!="function")throw new Error(xe(2));if(typeof t=="function"&&typeof r=="function"||typeof r=="function"&&typeof arguments[3]=="function")throw new Error(xe(0));if(typeof t=="function"&&typeof r>"u"&&(r=t,t=void 0),typeof r<"u"){if(typeof r!="function")throw new Error(xe(1));return r(Wd)(e,t)}let n=e,i=t,a=new Map,o=a,u=0,c=!1;function s(){o===a&&(o=new Map,a.forEach((m,g)=>{o.set(g,m)}))}function l(){if(c)throw new Error(xe(3));return i}function f(m){if(typeof m!="function")throw new Error(xe(4));if(c)throw new Error(xe(5));let g=!0;s();const b=u++;return o.set(b,m),function(){if(g){if(c)throw new Error(xe(6));g=!1,s(),o.delete(b),a=null}}}function d(m){if(!hu(m))throw new Error(xe(7));if(typeof m.type>"u")throw new Error(xe(8));if(typeof m.type!="string")throw new Error(xe(17));if(c)throw new Error(xe(9));try{c=!0,i=n(i,m)}finally{c=!1}return(a=o).forEach(b=>{b()}),m}function v(m){if(typeof m!="function")throw new Error(xe(10));n=m,d({type:Vn.REPLACE})}function h(){const m=f;return{subscribe(g){if(typeof g!="object"||g===null)throw new Error(xe(11));function b(){const P=g;P.next&&P.next(l())}return b(),{unsubscribe:m(b)}},[pc](){return this}}}return d({type:Vn.INIT}),{dispatch:d,subscribe:f,getState:l,replaceReducer:v,[pc]:h}}function a0(e){Object.keys(e).forEach(t=>{const r=e[t];if(typeof r(void 0,{type:Vn.INIT})>"u")throw new Error(xe(12));if(typeof r(void 0,{type:Vn.PROBE_UNKNOWN_ACTION()})>"u")throw new Error(xe(13))})}function Ud(e){const t=Object.keys(e),r={};for(let a=0;a<t.length;a++){const o=t[a];typeof e[o]=="function"&&(r[o]=e[o])}const n=Object.keys(r);let i;try{a0(r)}catch(a){i=a}return function(o={},u){if(i)throw i;let c=!1;const s={};for(let l=0;l<n.length;l++){const f=n[l],d=r[f],v=o[f],h=d(v,u);if(typeof h>"u")throw u&&u.type,new Error(xe(14));s[f]=h,c=c||h!==v}return c=c||n.length!==Object.keys(o).length,c?s:o}}function Xn(...e){return e.length===0?t=>t:e.length===1?e[0]:e.reduce((t,r)=>(...n)=>t(r(...n)))}function o0(...e){return t=>(r,n)=>{const i=t(r,n);let a=()=>{throw new Error(xe(15))};const o={getState:i.getState,dispatch:(c,...s)=>a(c,...s)},u=e.map(c=>c(o));return a=Xn(...u)(i.dispatch),{...i,dispatch:a}}}function qd(e){return hu(e)&&"type"in e&&typeof e.type=="string"}var Yd=Symbol.for("immer-nothing"),mc=Symbol.for("immer-draftable"),Ue=Symbol.for("immer-state");function lt(e,...t){throw new Error(`[Immer] minified error nr: ${e}. Full error at: https://bit.ly/3cXEKWf`)}var Er=Object.getPrototypeOf;function lr(e){return!!e&&!!e[Ue]}function Pt(e){var t;return e?Hd(e)||Array.isArray(e)||!!e[mc]||!!((t=e.constructor)!=null&&t[mc])||Yi(e)||Hi(e):!1}var u0=Object.prototype.constructor.toString();function Hd(e){if(!e||typeof e!="object")return!1;const t=Er(e);if(t===null)return!0;const r=Object.hasOwnProperty.call(t,"constructor")&&t.constructor;return r===Object?!0:typeof r=="function"&&Function.toString.call(r)===u0}function Zn(e,t){qi(e)===0?Reflect.ownKeys(e).forEach(r=>{t(r,e[r],e)}):e.forEach((r,n)=>t(n,r,e))}function qi(e){const t=e[Ue];return t?t.type_:Array.isArray(e)?1:Yi(e)?2:Hi(e)?3:0}function ro(e,t){return qi(e)===2?e.has(t):Object.prototype.hasOwnProperty.call(e,t)}function Gd(e,t,r){const n=qi(e);n===2?e.set(t,r):n===3?e.add(r):e[t]=r}function l0(e,t){return e===t?e!==0||1/e===1/t:e!==e&&t!==t}function Yi(e){return e instanceof Map}function Hi(e){return e instanceof Set}function Xt(e){return e.copy_||e.base_}function no(e,t){if(Yi(e))return new Map(e);if(Hi(e))return new Set(e);if(Array.isArray(e))return Array.prototype.slice.call(e);const r=Hd(e);if(t===!0||t==="class_only"&&!r){const n=Object.getOwnPropertyDescriptors(e);delete n[Ue];let i=Reflect.ownKeys(n);for(let a=0;a<i.length;a++){const o=i[a],u=n[o];u.writable===!1&&(u.writable=!0,u.configurable=!0),(u.get||u.set)&&(n[o]={configurable:!0,writable:!0,enumerable:u.enumerable,value:e[o]})}return Object.create(Er(e),n)}else{const n=Er(e);if(n!==null&&r)return{...e};const i=Object.create(n);return Object.assign(i,e)}}function pu(e,t=!1){return Gi(e)||lr(e)||!Pt(e)||(qi(e)>1&&(e.set=e.add=e.clear=e.delete=c0),Object.freeze(e),t&&Object.entries(e).forEach(([r,n])=>pu(n,!0))),e}function c0(){lt(2)}function Gi(e){return Object.isFrozen(e)}var s0={};function cr(e){const t=s0[e];return t||lt(0,e),t}var tn;function Vd(){return tn}function f0(e,t){return{drafts_:[],parent_:e,immer_:t,canAutoFreeze_:!0,unfinalizedDrafts_:0}}function yc(e,t){t&&(cr("Patches"),e.patches_=[],e.inversePatches_=[],e.patchListener_=t)}function io(e){ao(e),e.drafts_.forEach(d0),e.drafts_=null}function ao(e){e===tn&&(tn=e.parent_)}function gc(e){return tn=f0(tn,e)}function d0(e){const t=e[Ue];t.type_===0||t.type_===1?t.revoke_():t.revoked_=!0}function bc(e,t){t.unfinalizedDrafts_=t.drafts_.length;const r=t.drafts_[0];return e!==void 0&&e!==r?(r[Ue].modified_&&(io(t),lt(4)),Pt(e)&&(e=Jn(t,e),t.parent_||Qn(t,e)),t.patches_&&cr("Patches").generateReplacementPatches_(r[Ue].base_,e,t.patches_,t.inversePatches_)):e=Jn(t,r,[]),io(t),t.patches_&&t.patchListener_(t.patches_,t.inversePatches_),e!==Yd?e:void 0}function Jn(e,t,r){if(Gi(t))return t;const n=t[Ue];if(!n)return Zn(t,(i,a)=>wc(e,n,t,i,a,r)),t;if(n.scope_!==e)return t;if(!n.modified_)return Qn(e,n.base_,!0),n.base_;if(!n.finalized_){n.finalized_=!0,n.scope_.unfinalizedDrafts_--;const i=n.copy_;let a=i,o=!1;n.type_===3&&(a=new Set(i),i.clear(),o=!0),Zn(a,(u,c)=>wc(e,n,i,u,c,r,o)),Qn(e,i,!1),r&&e.patches_&&cr("Patches").generatePatches_(n,r,e.patches_,e.inversePatches_)}return n.copy_}function wc(e,t,r,n,i,a,o){if(lr(i)){const u=a&&t&&t.type_!==3&&!ro(t.assigned_,n)?a.concat(n):void 0,c=Jn(e,i,u);if(Gd(r,n,c),lr(c))e.canAutoFreeze_=!1;else return}else o&&r.add(i);if(Pt(i)&&!Gi(i)){if(!e.immer_.autoFreeze_&&e.unfinalizedDrafts_<1)return;Jn(e,i),(!t||!t.scope_.parent_)&&typeof n!="symbol"&&Object.prototype.propertyIsEnumerable.call(r,n)&&Qn(e,i)}}function Qn(e,t,r=!1){!e.parent_&&e.immer_.autoFreeze_&&e.canAutoFreeze_&&pu(t,r)}function v0(e,t){const r=Array.isArray(e),n={type_:r?1:0,scope_:t?t.scope_:Vd(),modified_:!1,finalized_:!1,assigned_:{},parent_:t,base_:e,draft_:null,copy_:null,revoke_:null,isManual_:!1};let i=n,a=mu;r&&(i=[n],a=rn);const{revoke:o,proxy:u}=Proxy.revocable(i,a);return n.draft_=u,n.revoke_=o,u}var mu={get(e,t){if(t===Ue)return e;const r=Xt(e);if(!ro(r,t))return h0(e,r,t);const n=r[t];return e.finalized_||!Pt(n)?n:n===ka(e.base_,t)?(Ma(e),e.copy_[t]=uo(n,e)):n},has(e,t){return t in Xt(e)},ownKeys(e){return Reflect.ownKeys(Xt(e))},set(e,t,r){const n=Xd(Xt(e),t);if(n!=null&&n.set)return n.set.call(e.draft_,r),!0;if(!e.modified_){const i=ka(Xt(e),t),a=i==null?void 0:i[Ue];if(a&&a.base_===r)return e.copy_[t]=r,e.assigned_[t]=!1,!0;if(l0(r,i)&&(r!==void 0||ro(e.base_,t)))return!0;Ma(e),oo(e)}return e.copy_[t]===r&&(r!==void 0||t in e.copy_)||Number.isNaN(r)&&Number.isNaN(e.copy_[t])||(e.copy_[t]=r,e.assigned_[t]=!0),!0},deleteProperty(e,t){return ka(e.base_,t)!==void 0||t in e.base_?(e.assigned_[t]=!1,Ma(e),oo(e)):delete e.assigned_[t],e.copy_&&delete e.copy_[t],!0},getOwnPropertyDescriptor(e,t){const r=Xt(e),n=Reflect.getOwnPropertyDescriptor(r,t);return n&&{writable:!0,configurable:e.type_!==1||t!=="length",enumerable:n.enumerable,value:r[t]}},defineProperty(){lt(11)},getPrototypeOf(e){return Er(e.base_)},setPrototypeOf(){lt(12)}},rn={};Zn(mu,(e,t)=>{rn[e]=function(){return arguments[0]=arguments[0][0],t.apply(this,arguments)}});rn.deleteProperty=function(e,t){return rn.set.call(this,e,t,void 0)};rn.set=function(e,t,r){return mu.set.call(this,e[0],t,r,e[0])};function ka(e,t){const r=e[Ue];return(r?Xt(r):e)[t]}function h0(e,t,r){var i;const n=Xd(t,r);return n?"value"in n?n.value:(i=n.get)==null?void 0:i.call(e.draft_):void 0}function Xd(e,t){if(!(t in e))return;let r=Er(e);for(;r;){const n=Object.getOwnPropertyDescriptor(r,t);if(n)return n;r=Er(r)}}function oo(e){e.modified_||(e.modified_=!0,e.parent_&&oo(e.parent_))}function Ma(e){e.copy_||(e.copy_=no(e.base_,e.scope_.immer_.useStrictShallowCopy_))}var p0=class{constructor(e){this.autoFreeze_=!0,this.useStrictShallowCopy_=!1,this.produce=(t,r,n)=>{if(typeof t=="function"&&typeof r!="function"){const a=r;r=t;const o=this;return function(c=a,...s){return o.produce(c,l=>r.call(this,l,...s))}}typeof r!="function"&&lt(6),n!==void 0&&typeof n!="function"&&lt(7);let i;if(Pt(t)){const a=gc(this),o=uo(t,void 0);let u=!0;try{i=r(o),u=!1}finally{u?io(a):ao(a)}return yc(a,n),bc(i,a)}else if(!t||typeof t!="object"){if(i=r(t),i===void 0&&(i=t),i===Yd&&(i=void 0),this.autoFreeze_&&pu(i,!0),n){const a=[],o=[];cr("Patches").generateReplacementPatches_(t,i,a,o),n(a,o)}return i}else lt(1,t)},this.produceWithPatches=(t,r)=>{if(typeof t=="function")return(o,...u)=>this.produceWithPatches(o,c=>t(c,...u));let n,i;return[this.produce(t,r,(o,u)=>{n=o,i=u}),n,i]},typeof(e==null?void 0:e.autoFreeze)=="boolean"&&this.setAutoFreeze(e.autoFreeze),typeof(e==null?void 0:e.useStrictShallowCopy)=="boolean"&&this.setUseStrictShallowCopy(e.useStrictShallowCopy)}createDraft(e){Pt(e)||lt(8),lr(e)&&(e=wt(e));const t=gc(this),r=uo(e,void 0);return r[Ue].isManual_=!0,ao(t),r}finishDraft(e,t){const r=e&&e[Ue];(!r||!r.isManual_)&&lt(9);const{scope_:n}=r;return yc(n,t),bc(void 0,n)}setAutoFreeze(e){this.autoFreeze_=e}setUseStrictShallowCopy(e){this.useStrictShallowCopy_=e}applyPatches(e,t){let r;for(r=t.length-1;r>=0;r--){const i=t[r];if(i.path.length===0&&i.op==="replace"){e=i.value;break}}r>-1&&(t=t.slice(r+1));const n=cr("Patches").applyPatches_;return lr(e)?n(e,t):this.produce(e,i=>n(i,t))}};function uo(e,t){const r=Yi(e)?cr("MapSet").proxyMap_(e,t):Hi(e)?cr("MapSet").proxySet_(e,t):v0(e,t);return(t?t.scope_:Vd()).drafts_.push(r),r}function wt(e){return lr(e)||lt(10,e),Zd(e)}function Zd(e){if(!Pt(e)||Gi(e))return e;const t=e[Ue];let r;if(t){if(!t.modified_)return t.base_;t.finalized_=!0,r=no(e,t.scope_.immer_.useStrictShallowCopy_)}else r=no(e,!0);return Zn(r,(n,i)=>{Gd(r,n,Zd(i))}),t&&(t.finalized_=!1),r}var qe=new p0,Jd=qe.produce;qe.produceWithPatches.bind(qe);qe.setAutoFreeze.bind(qe);qe.setUseStrictShallowCopy.bind(qe);qe.applyPatches.bind(qe);qe.createDraft.bind(qe);qe.finishDraft.bind(qe);function Qd(e){return({dispatch:r,getState:n})=>i=>a=>typeof a=="function"?a(r,n,e):i(a)}var m0=Qd(),y0=Qd,g0=typeof window<"u"&&window.__REDUX_DEVTOOLS_EXTENSION_COMPOSE__?window.__REDUX_DEVTOOLS_EXTENSION_COMPOSE__:function(){if(arguments.length!==0)return typeof arguments[0]=="object"?Xn:Xn.apply(null,arguments)};function tt(e,t){function r(...n){if(t){let i=t(...n);if(!i)throw new Error(Fe(0));return{type:e,payload:i.payload,..."meta"in i&&{meta:i.meta},..."error"in i&&{error:i.error}}}return{type:e,payload:n[0]}}return r.toString=()=>`${e}`,r.type=e,r.match=n=>qd(n)&&n.type===e,r}var ev=class Zr extends Array{constructor(...t){super(...t),Object.setPrototypeOf(this,Zr.prototype)}static get[Symbol.species](){return Zr}concat(...t){return super.concat.apply(this,t)}prepend(...t){return t.length===1&&Array.isArray(t[0])?new Zr(...t[0].concat(this)):new Zr(...t.concat(this))}};function xc(e){return Pt(e)?Jd(e,()=>{}):e}function Mn(e,t,r){return e.has(t)?e.get(t):e.set(t,r(t)).get(t)}function b0(e){return typeof e=="boolean"}var w0=()=>function(t){const{thunk:r=!0,immutableCheck:n=!0,serializableCheck:i=!0,actionCreatorCheck:a=!0}=t??{};let o=new ev;return r&&(b0(r)?o.push(m0):o.push(y0(r.extraArgument))),o},x0="RTK_autoBatch",Pc=e=>t=>{setTimeout(t,e)},P0=(e={type:"raf"})=>t=>(...r)=>{const n=t(...r);let i=!0,a=!1,o=!1;const u=new Set,c=e.type==="tick"?queueMicrotask:e.type==="raf"?typeof window<"u"&&window.requestAnimationFrame?window.requestAnimationFrame:Pc(10):e.type==="callback"?e.queueNotification:Pc(e.timeout),s=()=>{o=!1,a&&(a=!1,u.forEach(l=>l()))};return Object.assign({},n,{subscribe(l){const f=()=>i&&l(),d=n.subscribe(f);return u.add(l),()=>{d(),u.delete(l)}},dispatch(l){var f;try{return i=!((f=l==null?void 0:l.meta)!=null&&f[x0]),a=!i,a&&(o||(o=!0,c(s))),n.dispatch(l)}finally{i=!0}}})},O0=e=>function(r){const{autoBatch:n=!0}=r??{};let i=new ev(e);return n&&i.push(P0(typeof n=="object"?n:void 0)),i};function A0(e){const t=w0(),{reducer:r=void 0,middleware:n,devTools:i=!0,duplicateMiddlewareCheck:a=!0,preloadedState:o=void 0,enhancers:u=void 0}=e||{};let c;if(typeof r=="function")c=r;else if(hu(r))c=Ud(r);else throw new Error(Fe(1));let s;typeof n=="function"?s=n(t):s=t();let l=Xn;i&&(l=g0({trace:!1,...typeof i=="object"&&i}));const f=o0(...s),d=O0(f);let v=typeof u=="function"?u(d):d();const h=l(...v);return Wd(c,o,h)}function tv(e){const t={},r=[];let n;const i={addCase(a,o){const u=typeof a=="string"?a:a.type;if(!u)throw new Error(Fe(28));if(u in t)throw new Error(Fe(29));return t[u]=o,i},addMatcher(a,o){return r.push({matcher:a,reducer:o}),i},addDefaultCase(a){return n=a,i}};return e(i),[t,r,n]}function S0(e){return typeof e=="function"}function E0(e,t){let[r,n,i]=tv(t),a;if(S0(e))a=()=>xc(e());else{const u=xc(e);a=()=>u}function o(u=a(),c){let s=[r[c.type],...n.filter(({matcher:l})=>l(c)).map(({reducer:l})=>l)];return s.filter(l=>!!l).length===0&&(s=[i]),s.reduce((l,f)=>{if(f)if(lr(l)){const v=f(l,c);return v===void 0?l:v}else{if(Pt(l))return Jd(l,d=>f(d,c));{const d=f(l,c);if(d===void 0){if(l===null)return l;throw Error("A case reducer on a non-draftable value must not return undefined")}return d}}return l},u)}return o.getInitialState=a,o}var _0="ModuleSymbhasOwnPr-0123456789ABCDEFGHNRVfgctiUvz_KqYTJkLxpZXIjQW",T0=(e=21)=>{let t="",r=e;for(;r--;)t+=_0[Math.random()*64|0];return t},j0=Symbol.for("rtk-slice-createasyncthunk");function C0(e,t){return`${e}/${t}`}function k0({creators:e}={}){var r;const t=(r=e==null?void 0:e.asyncThunk)==null?void 0:r[j0];return function(i){const{name:a,reducerPath:o=a}=i;if(!a)throw new Error(Fe(11));typeof process<"u";const u=(typeof i.reducers=="function"?i.reducers(D0()):i.reducers)||{},c=Object.keys(u),s={sliceCaseReducersByName:{},sliceCaseReducersByType:{},actionCreators:{},sliceMatchers:[]},l={addCase(P,x){const O=typeof P=="string"?P:P.type;if(!O)throw new Error(Fe(12));if(O in s.sliceCaseReducersByType)throw new Error(Fe(13));return s.sliceCaseReducersByType[O]=x,l},addMatcher(P,x){return s.sliceMatchers.push({matcher:P,reducer:x}),l},exposeAction(P,x){return s.actionCreators[P]=x,l},exposeCaseReducer(P,x){return s.sliceCaseReducersByName[P]=x,l}};c.forEach(P=>{const x=u[P],O={reducerName:P,type:C0(a,P),createNotation:typeof i.reducers=="function"};$0(x)?L0(O,x,l,t):I0(O,x,l)});function f(){const[P={},x=[],O=void 0]=typeof i.extraReducers=="function"?tv(i.extraReducers):[i.extraReducers],A={...P,...s.sliceCaseReducersByType};return E0(i.initialState,E=>{for(let _ in A)E.addCase(_,A[_]);for(let _ of s.sliceMatchers)E.addMatcher(_.matcher,_.reducer);for(let _ of x)E.addMatcher(_.matcher,_.reducer);O&&E.addDefaultCase(O)})}const d=P=>P,v=new Map,h=new WeakMap;let y;function m(P,x){return y||(y=f()),y(P,x)}function g(){return y||(y=f()),y.getInitialState()}function b(P,x=!1){function O(E){let _=E[P];return typeof _>"u"&&x&&(_=Mn(h,O,g)),_}function A(E=d){const _=Mn(v,x,()=>new WeakMap);return Mn(_,E,()=>{const D={};for(const[j,C]of Object.entries(i.selectors??{}))D[j]=M0(C,E,()=>Mn(h,E,g),x);return D})}return{reducerPath:P,getSelectors:A,get selectors(){return A(O)},selectSlice:O}}const w={name:a,reducer:m,actions:s.actionCreators,caseReducers:s.sliceCaseReducersByName,getInitialState:g,...b(o),injectInto(P,{reducerPath:x,...O}={}){const A=x??o;return P.inject({reducerPath:A,reducer:m},O),{...w,...b(A,!0)}}};return w}}function M0(e,t,r,n){function i(a,...o){let u=t(a);return typeof u>"u"&&n&&(u=r()),e(u,...o)}return i.unwrapped=e,i}var it=k0();function D0(){function e(t,r){return{_reducerDefinitionType:"asyncThunk",payloadCreator:t,...r}}return e.withTypes=()=>e,{reducer(t){return Object.assign({[t.name](...r){return t(...r)}}[t.name],{_reducerDefinitionType:"reducer"})},preparedReducer(t,r){return{_reducerDefinitionType:"reducerWithPrepare",prepare:t,reducer:r}},asyncThunk:e}}function I0({type:e,reducerName:t,createNotation:r},n,i){let a,o;if("reducer"in n){if(r&&!N0(n))throw new Error(Fe(17));a=n.reducer,o=n.prepare}else a=n;i.addCase(e,a).exposeCaseReducer(t,a).exposeAction(t,o?tt(e,o):tt(e))}function $0(e){return e._reducerDefinitionType==="asyncThunk"}function N0(e){return e._reducerDefinitionType==="reducerWithPrepare"}function L0({type:e,reducerName:t},r,n,i){if(!i)throw new Error(Fe(18));const{payloadCreator:a,fulfilled:o,pending:u,rejected:c,settled:s,options:l}=r,f=i(e,a,l);n.exposeAction(t,f),o&&n.addCase(f.fulfilled,o),u&&n.addCase(f.pending,u),c&&n.addCase(f.rejected,c),s&&n.addMatcher(f.settled,s),n.exposeCaseReducer(t,{fulfilled:o||Dn,pending:u||Dn,rejected:c||Dn,settled:s||Dn})}function Dn(){}var R0="task",rv="listener",nv="completed",yu="cancelled",B0=`task-${yu}`,K0=`task-${nv}`,lo=`${rv}-${yu}`,z0=`${rv}-${nv}`,Vi=class{constructor(e){Pa(this,"name","TaskAbortError");Pa(this,"message");this.code=e,this.message=`${R0} ${yu} (reason: ${e})`}},gu=(e,t)=>{if(typeof e!="function")throw new TypeError(Fe(32))},ei=()=>{},iv=(e,t=ei)=>(e.catch(t),e),av=(e,t)=>(e.addEventListener("abort",t,{once:!0}),()=>e.removeEventListener("abort",t)),nr=(e,t)=>{const r=e.signal;r.aborted||("reason"in r||Object.defineProperty(r,"reason",{enumerable:!0,value:t,configurable:!0,writable:!0}),e.abort(t))},ir=e=>{if(e.aborted){const{reason:t}=e;throw new Vi(t)}};function ov(e,t){let r=ei;return new Promise((n,i)=>{const a=()=>i(new Vi(e.reason));if(e.aborted){a();return}r=av(e,a),t.finally(()=>r()).then(n,i)}).finally(()=>{r=ei})}var F0=async(e,t)=>{try{return await Promise.resolve(),{status:"ok",value:await e()}}catch(r){return{status:r instanceof Vi?"cancelled":"rejected",error:r}}finally{t==null||t()}},ti=e=>t=>iv(ov(e,t).then(r=>(ir(e),r))),uv=e=>{const t=ti(e);return r=>t(new Promise(n=>setTimeout(n,r)))},{assign:Pr}=Object,Oc={},Xi="listenerMiddleware",W0=(e,t)=>{const r=n=>av(e,()=>nr(n,e.reason));return(n,i)=>{gu(n);const a=new AbortController;r(a);const o=F0(async()=>{ir(e),ir(a.signal);const u=await n({pause:ti(a.signal),delay:uv(a.signal),signal:a.signal});return ir(a.signal),u},()=>nr(a,K0));return i!=null&&i.autoJoin&&t.push(o.catch(ei)),{result:ti(e)(o),cancel(){nr(a,B0)}}}},U0=(e,t)=>{const r=async(n,i)=>{ir(t);let a=()=>{};const u=[new Promise((c,s)=>{let l=e({predicate:n,effect:(f,d)=>{d.unsubscribe(),c([f,d.getState(),d.getOriginalState()])}});a=()=>{l(),s()}})];i!=null&&u.push(new Promise(c=>setTimeout(c,i,null)));try{const c=await ov(t,Promise.race(u));return ir(t),c}finally{a()}};return(n,i)=>iv(r(n,i))},lv=e=>{let{type:t,actionCreator:r,matcher:n,predicate:i,effect:a}=e;if(t)i=tt(t).match;else if(r)t=r.type,i=r.match;else if(n)i=n;else if(!i)throw new Error(Fe(21));return gu(a),{predicate:i,type:t,effect:a}},cv=Pr(e=>{const{type:t,predicate:r,effect:n}=lv(e);return{id:T0(),effect:n,type:t,predicate:r,pending:new Set,unsubscribe:()=>{throw new Error(Fe(22))}}},{withTypes:()=>cv}),Ac=(e,t)=>{const{type:r,effect:n,predicate:i}=lv(t);return Array.from(e.values()).find(a=>(typeof r=="string"?a.type===r:a.predicate===i)&&a.effect===n)},co=e=>{e.pending.forEach(t=>{nr(t,lo)})},q0=e=>()=>{e.forEach(co),e.clear()},Sc=(e,t,r)=>{try{e(t,r)}catch(n){setTimeout(()=>{throw n},0)}},sv=Pr(tt(`${Xi}/add`),{withTypes:()=>sv}),Y0=tt(`${Xi}/removeAll`),fv=Pr(tt(`${Xi}/remove`),{withTypes:()=>fv}),H0=(...e)=>{console.error(`${Xi}/error`,...e)},yn=(e={})=>{const t=new Map,{extra:r,onError:n=H0}=e;gu(n);const i=l=>(l.unsubscribe=()=>t.delete(l.id),t.set(l.id,l),f=>{l.unsubscribe(),f!=null&&f.cancelActive&&co(l)}),a=l=>{const f=Ac(t,l)??cv(l);return i(f)};Pr(a,{withTypes:()=>a});const o=l=>{const f=Ac(t,l);return f&&(f.unsubscribe(),l.cancelActive&&co(f)),!!f};Pr(o,{withTypes:()=>o});const u=async(l,f,d,v)=>{const h=new AbortController,y=U0(a,h.signal),m=[];try{l.pending.add(h),await Promise.resolve(l.effect(f,Pr({},d,{getOriginalState:v,condition:(g,b)=>y(g,b).then(Boolean),take:y,delay:uv(h.signal),pause:ti(h.signal),extra:r,signal:h.signal,fork:W0(h.signal,m),unsubscribe:l.unsubscribe,subscribe:()=>{t.set(l.id,l)},cancelActiveListeners:()=>{l.pending.forEach((g,b,w)=>{g!==h&&(nr(g,lo),w.delete(g))})},cancel:()=>{nr(h,lo),l.pending.delete(h)},throwIfCancelled:()=>{ir(h.signal)}})))}catch(g){g instanceof Vi||Sc(n,g,{raisedBy:"effect"})}finally{await Promise.all(m),nr(h,z0),l.pending.delete(h)}},c=q0(t);return{middleware:l=>f=>d=>{if(!qd(d))return f(d);if(sv.match(d))return a(d.payload);if(Y0.match(d)){c();return}if(fv.match(d))return o(d.payload);let v=l.getState();const h=()=>{if(v===Oc)throw new Error(Fe(23));return v};let y;try{if(y=f(d),t.size>0){const m=l.getState(),g=Array.from(t.values());for(const b of g){let w=!1;try{w=b.predicate(d,m,v)}catch(P){w=!1,Sc(n,P,{raisedBy:"predicate"})}w&&u(b,d,l,h)}}}finally{v=Oc}return y},startListening:a,stopListening:o,clearListeners:c}};function Fe(e){return`Minified Redux Toolkit error #${e}; visit https://redux-toolkit.js.org/Errors?code=${e} for the full message or use the non-minified dev environment for full errors. `}var G0={layoutType:"horizontal",width:0,height:0,margin:{top:5,right:5,bottom:5,left:5},scale:1},dv=it({name:"chartLayout",initialState:G0,reducers:{setLayout(e,t){e.layoutType=t.payload},setChartSize(e,t){e.width=t.payload.width,e.height=t.payload.height},setMargin(e,t){e.margin.top=t.payload.top,e.margin.right=t.payload.right,e.margin.bottom=t.payload.bottom,e.margin.left=t.payload.left},setScale(e,t){e.scale=t.payload}}}),{setMargin:V0,setLayout:X0,setChartSize:Z0,setScale:J0}=dv.actions,Q0=dv.reducer;function Ec(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function _c(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Ec(Object(r),!0).forEach(function(n){eb(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Ec(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function eb(e,t,r){return(t=tb(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function tb(e){var t=rb(e,"string");return typeof t=="symbol"?t:t+""}function rb(e,t){if(typeof e!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t||"default");if(typeof n!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var ri=Math.PI/180,nb=e=>e*180/Math.PI,ce=(e,t,r,n)=>({x:e+Math.cos(-ri*n)*r,y:t+Math.sin(-ri*n)*r}),vv=function(t,r){var n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{top:0,right:0,bottom:0,left:0,width:0,height:0,brushBottom:0};return Math.min(Math.abs(t-(n.left||0)-(n.right||0)),Math.abs(r-(n.top||0)-(n.bottom||0)))/2},ib=(e,t)=>{var{x:r,y:n}=e,{x:i,y:a}=t;return Math.sqrt((r-i)**2+(n-a)**2)},ab=(e,t)=>{var{x:r,y:n}=e,{cx:i,cy:a}=t,o=ib({x:r,y:n},{x:i,y:a});if(o<=0)return{radius:o,angle:0};var u=(r-i)/o,c=Math.acos(u);return n>a&&(c=2*Math.PI-c),{radius:o,angle:nb(c),angleInRadian:c}},ob=e=>{var{startAngle:t,endAngle:r}=e,n=Math.floor(t/360),i=Math.floor(r/360),a=Math.min(n,i);return{startAngle:t-a*360,endAngle:r-a*360}},ub=(e,t)=>{var{startAngle:r,endAngle:n}=t,i=Math.floor(r/360),a=Math.floor(n/360),o=Math.min(i,a);return e+o*360},lb=(e,t)=>{var{x:r,y:n}=e,{radius:i,angle:a}=ab({x:r,y:n},t),{innerRadius:o,outerRadius:u}=t;if(i<o||i>u||i===0)return null;var{startAngle:c,endAngle:s}=ob(t),l=a,f;if(c<=s){for(;l>s;)l-=360;for(;l<c;)l+=360;f=l>=c&&l<=s}else{for(;l>c;)l-=360;for(;l<s;)l+=360;f=l>=s&&l<=c}return f?_c(_c({},t),{},{radius:i,angle:ub(l,t)}):null};function Tc(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Qe(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Tc(Object(r),!0).forEach(function(n){cb(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Tc(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function cb(e,t,r){return(t=sb(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function sb(e){var t=fb(e,"string");return typeof t=="symbol"?t:t+""}function fb(e,t){if(typeof e!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t||"default");if(typeof n!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function te(e,t,r){return X(e)||X(t)?r:vt(t)?Rt(e,t,r):typeof t=="function"?t(e):r}var db=(e,t,r,n,i)=>{var a,o=-1,u=(a=t==null?void 0:t.length)!==null&&a!==void 0?a:0;if(u<=1||e==null)return 0;if(n==="angleAxis"&&i!=null&&Math.abs(Math.abs(i[1]-i[0])-360)<=1e-6)for(var c=0;c<u;c++){var s=c>0?r[c-1].coordinate:r[u-1].coordinate,l=r[c].coordinate,f=c>=u-1?r[0].coordinate:r[c+1].coordinate,d=void 0;if(me(l-s)!==me(f-l)){var v=[];if(me(f-l)===me(i[1]-i[0])){d=f;var h=l+i[1]-i[0];v[0]=Math.min(h,(h+s)/2),v[1]=Math.max(h,(h+s)/2)}else{d=s;var y=f+i[1]-i[0];v[0]=Math.min(l,(y+l)/2),v[1]=Math.max(l,(y+l)/2)}var m=[Math.min(l,(d+l)/2),Math.max(l,(d+l)/2)];if(e>m[0]&&e<=m[1]||e>=v[0]&&e<=v[1]){({index:o}=r[c]);break}}else{var g=Math.min(s,f),b=Math.max(s,f);if(e>(g+l)/2&&e<=(b+l)/2){({index:o}=r[c]);break}}}else if(t){for(var w=0;w<u;w++)if(w===0&&e<=(t[w].coordinate+t[w+1].coordinate)/2||w>0&&w<u-1&&e>(t[w].coordinate+t[w-1].coordinate)/2&&e<=(t[w].coordinate+t[w+1].coordinate)/2||w===u-1&&e>(t[w].coordinate+t[w-1].coordinate)/2){({index:o}=t[w]);break}}return o},vb=(e,t,r)=>{if(t&&r){var{width:n,height:i}=r,{align:a,verticalAlign:o,layout:u}=t;if((u==="vertical"||u==="horizontal"&&o==="middle")&&a!=="center"&&k(e[a]))return Qe(Qe({},e),{},{[a]:e[a]+(n||0)});if((u==="horizontal"||u==="vertical"&&a==="center")&&o!=="middle"&&k(e[o]))return Qe(Qe({},e),{},{[o]:e[o]+(i||0)})}return e},Ft=(e,t)=>e==="horizontal"&&t==="xAxis"||e==="vertical"&&t==="yAxis"||e==="centric"&&t==="angleAxis"||e==="radial"&&t==="radiusAxis",hv=(e,t,r,n)=>{if(n)return e.map(u=>u.coordinate);var i,a,o=e.map(u=>(u.coordinate===t&&(i=!0),u.coordinate===r&&(a=!0),u.coordinate));return i||o.push(t),a||o.push(r),o},pv=(e,t,r)=>{if(!e)return null;var{duplicateDomain:n,type:i,range:a,scale:o,realScaleType:u,isCategorical:c,categoricalDomain:s,tickCount:l,ticks:f,niceTicks:d,axisType:v}=e;if(!o)return null;var h=u==="scaleBand"&&o.bandwidth?o.bandwidth()/2:2,y=(t||r)&&i==="category"&&o.bandwidth?o.bandwidth()/h:0;if(y=v==="angleAxis"&&a&&a.length>=2?me(a[0]-a[1])*2*y:y,t&&(f||d)){var m=(f||d||[]).map((g,b)=>{var w=n?n.indexOf(g):g;return{coordinate:o(w)+y,value:g,offset:y,index:b}});return m.filter(g=>!We(g.coordinate))}return c&&s?s.map((g,b)=>({coordinate:o(g)+y,value:g,index:b,offset:y})):o.ticks&&!r&&l!=null?o.ticks(l).map((g,b)=>({coordinate:o(g)+y,value:g,offset:y,index:b})):o.domain().map((g,b)=>({coordinate:o(g)+y,value:n?n[g]:g,index:b,offset:y}))},jc=1e-4,hb=e=>{var t=e.domain();if(!(!t||t.length<=2)){var r=t.length,n=e.range(),i=Math.min(n[0],n[1])-jc,a=Math.max(n[0],n[1])+jc,o=e(t[0]),u=e(t[r-1]);(o<i||o>a||u<i||u>a)&&e.domain([t[0],t[r-1]])}},pb=(e,t)=>{if(!t||t.length!==2||!k(t[0])||!k(t[1]))return e;var r=Math.min(t[0],t[1]),n=Math.max(t[0],t[1]),i=[e[0],e[1]];return(!k(e[0])||e[0]<r)&&(i[0]=r),(!k(e[1])||e[1]>n)&&(i[1]=n),i[0]>n&&(i[0]=n),i[1]<r&&(i[1]=r),i},mb=e=>{var t=e.length;if(!(t<=0))for(var r=0,n=e[0].length;r<n;++r)for(var i=0,a=0,o=0;o<t;++o){var u=We(e[o][r][1])?e[o][r][0]:e[o][r][1];u>=0?(e[o][r][0]=i,e[o][r][1]=i+u,i=e[o][r][1]):(e[o][r][0]=a,e[o][r][1]=a+u,a=e[o][r][1])}},yb=e=>{var t=e.length;if(!(t<=0))for(var r=0,n=e[0].length;r<n;++r)for(var i=0,a=0;a<t;++a){var o=We(e[a][r][1])?e[a][r][0]:e[a][r][1];o>=0?(e[a][r][0]=i,e[a][r][1]=i+o,i=e[a][r][1]):(e[a][r][0]=0,e[a][r][1]=0)}},gb={sign:mb,expand:rg,none:Ar,silhouette:ng,wiggle:ig,positive:yb},bb=(e,t,r)=>{var n=gb[r],i=tg().keys(t).value((a,o)=>+te(a,o,0)).order(Qa).offset(n);return i(e)};function mv(e){return e==null?void 0:String(e)}function Cc(e){var{axis:t,ticks:r,bandSize:n,entry:i,index:a,dataKey:o}=e;if(t.type==="category"){if(!t.allowDuplicatedCategory&&t.dataKey&&!X(i[t.dataKey])){var u=Gf(r,"value",i[t.dataKey]);if(u)return u.coordinate+n/2}return r[a]?r[a].coordinate+n/2:null}var c=te(i,X(o)?t.dataKey:o);return X(c)?null:t.scale(c)}var kc=e=>{var{axis:t,ticks:r,offset:n,bandSize:i,entry:a,index:o}=e;if(t.type==="category")return r[o]?r[o].coordinate+n:null;var u=te(a,t.dataKey,t.scale.domain()[o]);return X(u)?null:t.scale(u)-i/2+n},wb=e=>{var{numericAxis:t}=e,r=t.scale.domain();if(t.type==="number"){var n=Math.min(r[0],r[1]),i=Math.max(r[0],r[1]);return n<=0&&i>=0?0:i<0?i:n}return r[0]},xb=e=>{var t=e.flat(2).filter(k);return[Math.min(...t),Math.max(...t)]},Pb=e=>[e[0]===1/0?0:e[0],e[1]===-1/0?0:e[1]],Ob=(e,t,r)=>{if(e!=null)return Pb(Object.keys(e).reduce((n,i)=>{var a=e[i],{stackedData:o}=a,u=o.reduce((c,s)=>{var l=xb(s.slice(t,r+1));return[Math.min(c[0],l[0]),Math.max(c[1],l[1])]},[1/0,-1/0]);return[Math.min(u[0],n[0]),Math.max(u[1],n[1])]},[1/0,-1/0]))},Mc=/^dataMin[\s]*-[\s]*([0-9]+([.]{1}[0-9]+){0,1})$/,Dc=/^dataMax[\s]*\+[\s]*([0-9]+([.]{1}[0-9]+){0,1})$/,nn=(e,t,r)=>{if(e&&e.scale&&e.scale.bandwidth){var n=e.scale.bandwidth();if(!r||n>0)return n}if(e&&t&&t.length>=2){for(var i=Ui(t,l=>l.coordinate),a=1/0,o=1,u=i.length;o<u;o++){var c=i[o],s=i[o-1];a=Math.min((c.coordinate||0)-(s.coordinate||0),a)}return a===1/0?0:a}return r?void 0:0};function Ic(e){var{tooltipEntrySettings:t,dataKey:r,payload:n,value:i,name:a}=e;return Qe(Qe({},t),{},{dataKey:r,payload:n,value:i,name:a})}function Ir(e,t){if(e)return String(e);if(typeof t=="string")return t}function Ab(e,t,r,n,i){if(r==="horizontal"||r==="vertical"){var a=e>=i.left&&e<=i.left+i.width&&t>=i.top&&t<=i.top+i.height;return a?{x:e,y:t}:null}return n?lb({x:e,y:t},n):null}var Sb=(e,t,r,n)=>{var i=t.find(s=>s&&s.index===r);if(i){if(e==="horizontal")return{x:i.coordinate,y:n.y};if(e==="vertical")return{x:n.x,y:i.coordinate};if(e==="centric"){var a=i.coordinate,{radius:o}=n;return Qe(Qe(Qe({},n),ce(n.cx,n.cy,o,a)),{},{angle:a,radius:o})}var u=i.coordinate,{angle:c}=n;return Qe(Qe(Qe({},n),ce(n.cx,n.cy,u,c)),{},{angle:c,radius:u})}return{x:0,y:0}},Eb=(e,t)=>t==="horizontal"?e.x:t==="vertical"?e.y:t==="centric"?e.angle:e.radius,_t=e=>e.layout.width,Tt=e=>e.layout.height,_b=e=>e.layout.scale,yv=e=>e.layout.margin,bu=S(e=>e.cartesianAxis.xAxis,e=>Object.values(e)),wu=S(e=>e.cartesianAxis.yAxis,e=>Object.values(e)),gv="data-recharts-item-index",bv="data-recharts-item-data-key",Zi=60;function $c(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Mt(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?$c(Object(r),!0).forEach(function(n){Tb(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):$c(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function Tb(e,t,r){return(t=jb(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function jb(e){var t=Cb(e,"string");return typeof t=="symbol"?t:t+""}function Cb(e,t){if(typeof e!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t||"default");if(typeof n!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var kb=e=>e.brush.height,ve=S([_t,Tt,yv,kb,bu,wu,zd,Qg],(e,t,r,n,i,a,o,u)=>{var c=a.reduce((h,y)=>{var{orientation:m}=y;if(!y.mirror&&!y.hide){var g=typeof y.width=="number"?y.width:Zi;return Mt(Mt({},h),{},{[m]:h[m]+g})}return h},{left:r.left||0,right:r.right||0}),s=i.reduce((h,y)=>{var{orientation:m}=y;return!y.mirror&&!y.hide?Mt(Mt({},h),{},{[m]:Rt(h,"".concat(m))+y.height}):h},{top:r.top||0,bottom:r.bottom||0}),l=Mt(Mt({},s),c),f=l.bottom;l.bottom+=n,l=vb(l,o,u);var d=e-l.left-l.right,v=t-l.top-l.bottom;return Mt(Mt({brushBottom:f},l),{},{width:Math.max(d,0),height:Math.max(v,0)})}),Mb=S(ve,e=>({x:e.left,y:e.top,width:e.width,height:e.height})),wv=S(_t,Tt,(e,t)=>({x:0,y:0,width:e,height:t})),Db=p.createContext(null),je=()=>p.useContext(Db)!=null,Ji=e=>e.brush,Qi=S([Ji,ve,yv],(e,t,r)=>({height:e.height,x:k(e.x)?e.x:t.left,y:k(e.y)?e.y:t.top+t.height+t.brushBottom-((r==null?void 0:r.bottom)||0),width:k(e.width)?e.width:t.width})),xu=()=>{var e,t=je(),r=$(Mb),n=$(Qi),i=(e=$(Ji))===null||e===void 0?void 0:e.padding;return!t||!n||!i?r:{width:n.width-i.left-i.right,height:n.height-i.top-i.bottom,x:i.left,y:i.top}},Ib={top:0,bottom:0,left:0,right:0,width:0,height:0,brushBottom:0},xv=()=>{var e;return(e=$(ve))!==null&&e!==void 0?e:Ib},Pu=()=>$(_t),Ou=()=>$(Tt),$b={top:0,right:0,bottom:0,left:0},Nb=()=>{var e;return(e=$(t=>t.layout.margin))!==null&&e!==void 0?e:$b},Y=e=>e.layout.layoutType,ea=()=>$(Y),Lb={settings:{layout:"horizontal",align:"center",verticalAlign:"middle",itemSorter:"value"},size:{width:0,height:0},payload:[]},Pv=it({name:"legend",initialState:Lb,reducers:{setLegendSize(e,t){e.size.width=t.payload.width,e.size.height=t.payload.height},setLegendSettings(e,t){e.settings.align=t.payload.align,e.settings.layout=t.payload.layout,e.settings.verticalAlign=t.payload.verticalAlign,e.settings.itemSorter=t.payload.itemSorter},addLegendPayload(e,t){e.payload.push(t.payload)},removeLegendPayload(e,t){var r=wt(e).payload.indexOf(t.payload);r>-1&&e.payload.splice(r,1)}}}),{setLegendSize:Nc,setLegendSettings:Rb,addLegendPayload:Ov,removeLegendPayload:Av}=Pv.actions,Bb=Pv.reducer,Kb=["contextPayload"];function so(){return so=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},so.apply(null,arguments)}function Lc(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function _r(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Lc(Object(r),!0).forEach(function(n){Au(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Lc(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function Au(e,t,r){return(t=zb(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function zb(e){var t=Fb(e,"string");return typeof t=="symbol"?t:t+""}function Fb(e,t){if(typeof e!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t||"default");if(typeof n!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function Wb(e,t){if(e==null)return{};var r,n,i=Ub(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],t.indexOf(r)===-1&&{}.propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}function Ub(e,t){if(e==null)return{};var r={};for(var n in e)if({}.hasOwnProperty.call(e,n)){if(t.indexOf(n)!==-1)continue;r[n]=e[n]}return r}function qb(e){return e.value}function Yb(e){var{contextPayload:t}=e,r=Wb(e,Kb),n=Cd(t,e.payloadUniqBy,qb),i=_r(_r({},r),{},{payload:n});return p.isValidElement(e.content)?p.cloneElement(e.content,i):typeof e.content=="function"?p.createElement(e.content,i):p.createElement(nu,i)}function Hb(e,t,r,n,i,a){var{layout:o,align:u,verticalAlign:c}=t,s,l;return(!e||(e.left===void 0||e.left===null)&&(e.right===void 0||e.right===null))&&(u==="center"&&o==="vertical"?s={left:((n||0)-a.width)/2}:s=u==="right"?{right:r&&r.right||0}:{left:r&&r.left||0}),(!e||(e.top===void 0||e.top===null)&&(e.bottom===void 0||e.bottom===null))&&(c==="middle"?l={top:((i||0)-a.height)/2}:l=c==="bottom"?{bottom:r&&r.bottom||0}:{top:r&&r.top||0}),_r(_r({},s),l)}function Gb(e){var t=ne();return p.useEffect(()=>{t(Rb(e))},[t,e]),null}function Vb(e){var t=ne();return p.useEffect(()=>(t(Nc(e)),()=>{t(Nc({width:0,height:0}))}),[t,e]),null}function Xb(e){var t=r0(),r=Sy(),n=Nb(),{width:i,height:a,wrapperStyle:o,portal:u}=e,[c,s]=Fd([t]),l=Pu(),f=Ou(),d=l-(n.left||0)-(n.right||0),v=Su.getWidthOrHeight(e.layout,a,i,d),h=u?o:_r(_r({position:"absolute",width:(v==null?void 0:v.width)||i||"auto",height:(v==null?void 0:v.height)||a||"auto"},Hb(o,e,n,l,f,c)),o),y=u??r;if(y==null)return null;var m=p.createElement("div",{className:"recharts-legend-wrapper",style:h,ref:s},p.createElement(Gb,{layout:e.layout,align:e.align,verticalAlign:e.verticalAlign,itemSorter:e.itemSorter}),p.createElement(Vb,{width:c.width,height:c.height}),p.createElement(Yb,so({},e,v,{margin:n,chartWidth:l,chartHeight:f,contextPayload:t})));return Wf.createPortal(m,y)}class Su extends p.PureComponent{static getWidthOrHeight(t,r,n,i){return t==="vertical"&&k(r)?{height:r}:t==="horizontal"?{width:n||i}:null}render(){return p.createElement(Xb,this.props)}}Au(Su,"displayName","Legend");Au(Su,"defaultProps",{align:"center",iconSize:14,itemSorter:"value",layout:"horizontal",verticalAlign:"bottom"});function fo(){return fo=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},fo.apply(null,arguments)}function Rc(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Da(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Rc(Object(r),!0).forEach(function(n){Zb(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Rc(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function Zb(e,t,r){return(t=Jb(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Jb(e){var t=Qb(e,"string");return typeof t=="symbol"?t:t+""}function Qb(e,t){if(typeof e!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t||"default");if(typeof n!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function ew(e){return Array.isArray(e)&&vt(e[0])&&vt(e[1])?e.join(" ~ "):e}var tw=e=>{var{separator:t=" : ",contentStyle:r={},itemStyle:n={},labelStyle:i={},payload:a,formatter:o,itemSorter:u,wrapperClassName:c,labelClassName:s,label:l,labelFormatter:f,accessibilityLayer:d=!1}=e,v=()=>{if(a&&a.length){var x={padding:0,margin:0},O=(u?Ui(a,u):a).map((A,E)=>{if(A.type==="none")return null;var _=A.formatter||o||ew,{value:D,name:j}=A,C=D,N=j;if(_){var R=_(D,j,A,E,a);if(Array.isArray(R))[C,N]=R;else if(R!=null)C=R;else return null}var F=Da({display:"block",paddingTop:4,paddingBottom:4,color:A.color||"#000"},n);return p.createElement("li",{className:"recharts-tooltip-item",key:"tooltip-item-".concat(E),style:F},vt(N)?p.createElement("span",{className:"recharts-tooltip-item-name"},N):null,vt(N)?p.createElement("span",{className:"recharts-tooltip-item-separator"},t):null,p.createElement("span",{className:"recharts-tooltip-item-value"},C),p.createElement("span",{className:"recharts-tooltip-item-unit"},A.unit||""))});return p.createElement("ul",{className:"recharts-tooltip-item-list",style:x},O)}return null},h=Da({margin:0,padding:10,backgroundColor:"#fff",border:"1px solid #ccc",whiteSpace:"nowrap"},r),y=Da({margin:0},i),m=!X(l),g=m?l:"",b=U("recharts-default-tooltip",c),w=U("recharts-tooltip-label",s);m&&f&&a!==void 0&&a!==null&&(g=f(l,a));var P=d?{role:"status","aria-live":"assertive"}:{};return p.createElement("div",fo({className:b,style:h},P),p.createElement("p",{className:w,style:y},p.isValidElement(g)?g:"".concat(g)),v())},Wr="recharts-tooltip-wrapper",rw={visibility:"hidden"};function nw(e){var{coordinate:t,translateX:r,translateY:n}=e;return U(Wr,{["".concat(Wr,"-right")]:k(r)&&t&&k(t.x)&&r>=t.x,["".concat(Wr,"-left")]:k(r)&&t&&k(t.x)&&r<t.x,["".concat(Wr,"-bottom")]:k(n)&&t&&k(t.y)&&n>=t.y,["".concat(Wr,"-top")]:k(n)&&t&&k(t.y)&&n<t.y})}function Bc(e){var{allowEscapeViewBox:t,coordinate:r,key:n,offsetTopLeft:i,position:a,reverseDirection:o,tooltipDimension:u,viewBox:c,viewBoxDimension:s}=e;if(a&&k(a[n]))return a[n];var l=r[n]-u-(i>0?i:0),f=r[n]+i;if(t[n])return o[n]?l:f;var d=c[n];if(d==null)return 0;if(o[n]){var v=l,h=d;return v<h?Math.max(f,d):Math.max(l,d)}if(s==null)return 0;var y=f+u,m=d+s;return y>m?Math.max(l,d):Math.max(f,d)}function iw(e){var{translateX:t,translateY:r,useTranslate3d:n}=e;return{transform:n?"translate3d(".concat(t,"px, ").concat(r,"px, 0)"):"translate(".concat(t,"px, ").concat(r,"px)")}}function aw(e){var{allowEscapeViewBox:t,coordinate:r,offsetTopLeft:n,position:i,reverseDirection:a,tooltipBox:o,useTranslate3d:u,viewBox:c}=e,s,l,f;return o.height>0&&o.width>0&&r?(l=Bc({allowEscapeViewBox:t,coordinate:r,key:"x",offsetTopLeft:n,position:i,reverseDirection:a,tooltipDimension:o.width,viewBox:c,viewBoxDimension:c.width}),f=Bc({allowEscapeViewBox:t,coordinate:r,key:"y",offsetTopLeft:n,position:i,reverseDirection:a,tooltipDimension:o.height,viewBox:c,viewBoxDimension:c.height}),s=iw({translateX:l,translateY:f,useTranslate3d:u})):s=rw,{cssProperties:s,cssClasses:nw({translateX:l,translateY:f,coordinate:r})}}function Kc(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function In(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Kc(Object(r),!0).forEach(function(n){vo(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Kc(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function vo(e,t,r){return(t=ow(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function ow(e){var t=uw(e,"string");return typeof t=="symbol"?t:t+""}function uw(e,t){if(typeof e!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t||"default");if(typeof n!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}class lw extends p.PureComponent{constructor(){super(...arguments),vo(this,"state",{dismissed:!1,dismissedAtCoordinate:{x:0,y:0}}),vo(this,"handleKeyDown",t=>{if(t.key==="Escape"){var r,n,i,a;this.setState({dismissed:!0,dismissedAtCoordinate:{x:(r=(n=this.props.coordinate)===null||n===void 0?void 0:n.x)!==null&&r!==void 0?r:0,y:(i=(a=this.props.coordinate)===null||a===void 0?void 0:a.y)!==null&&i!==void 0?i:0}})}})}componentDidMount(){document.addEventListener("keydown",this.handleKeyDown)}componentWillUnmount(){document.removeEventListener("keydown",this.handleKeyDown)}componentDidUpdate(){var t,r;this.state.dismissed&&(((t=this.props.coordinate)===null||t===void 0?void 0:t.x)!==this.state.dismissedAtCoordinate.x||((r=this.props.coordinate)===null||r===void 0?void 0:r.y)!==this.state.dismissedAtCoordinate.y)&&(this.state.dismissed=!1)}render(){var{active:t,allowEscapeViewBox:r,animationDuration:n,animationEasing:i,children:a,coordinate:o,hasPayload:u,isAnimationActive:c,offset:s,position:l,reverseDirection:f,useTranslate3d:d,viewBox:v,wrapperStyle:h,lastBoundingBox:y,innerRef:m,hasPortalFromProps:g}=this.props,{cssClasses:b,cssProperties:w}=aw({allowEscapeViewBox:r,coordinate:o,offsetTopLeft:s,position:l,reverseDirection:f,tooltipBox:{height:y.height,width:y.width},useTranslate3d:d,viewBox:v}),P=g?{}:In(In({transition:c&&t?"transform ".concat(n,"ms ").concat(i):void 0},w),{},{pointerEvents:"none",visibility:!this.state.dismissed&&t&&u?"visible":"hidden",position:"absolute",top:0,left:0}),x=In(In({},P),{},{visibility:!this.state.dismissed&&t&&u?"visible":"hidden"},h);return p.createElement("div",{xmlns:"http://www.w3.org/1999/xhtml",tabIndex:-1,className:b,style:x,ref:m},a)}}var cw=()=>!(typeof window<"u"&&window.document&&window.document.createElement&&window.setTimeout),dr={isSsr:cw()},Sv=()=>$(e=>e.rootProps.accessibilityLayer);function Ye(e){return Number.isFinite(e)}function Tr(e){return typeof e=="number"&&e>0&&Number.isFinite(e)}function ho(){return ho=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},ho.apply(null,arguments)}function zc(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Fc(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?zc(Object(r),!0).forEach(function(n){sw(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):zc(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function sw(e,t,r){return(t=fw(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function fw(e){var t=dw(e,"string");return typeof t=="symbol"?t:t+""}function dw(e,t){if(typeof e!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t||"default");if(typeof n!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var Wc={curveBasisClosed:Uy,curveBasisOpen:qy,curveBasis:Wy,curveBumpX:jy,curveBumpY:Cy,curveLinearClosed:Yy,curveLinear:Li,curveMonotoneX:Hy,curveMonotoneY:Gy,curveNatural:Vy,curveStep:Xy,curveStepAfter:Jy,curveStepBefore:Zy},$n=e=>Ye(e.x)&&Ye(e.y),Ur=e=>e.x,qr=e=>e.y,vw=(e,t)=>{if(typeof e=="function")return e;var r="curve".concat(hn(e));return(r==="curveMonotone"||r==="curveBump")&&t?Wc["".concat(r).concat(t==="vertical"?"Y":"X")]:Wc[r]||Li},hw=e=>{var{type:t="linear",points:r=[],baseLine:n,layout:i,connectNulls:a=!1}=e,o=vw(t,i),u=a?r.filter($n):r,c;if(Array.isArray(n)){var s=a?n.filter(f=>$n(f)):n,l=u.map((f,d)=>Fc(Fc({},f),{},{base:s[d]}));return i==="vertical"?c=jn().y(qr).x1(Ur).x0(f=>f.base.x):c=jn().x(Ur).y1(qr).y0(f=>f.base.y),c.defined($n).curve(o),c(l)}return i==="vertical"&&k(n)?c=jn().y(qr).x1(Ur).x0(n):k(n)?c=jn().x(Ur).y1(qr).y0(n):c=nd().x(Ur).y(qr),c.defined($n).curve(o),c(u)},Eu=e=>{var{className:t,points:r,path:n,pathRef:i}=e;if((!r||!r.length)&&!n)return null;var a=r&&r.length?hw(e):n;return p.createElement("path",ho({},z(e,!1),Xo(e),{className:U("recharts-curve",t),d:a===null?void 0:a,ref:i}))},pw=["x","y","top","left","width","height","className"];function po(){return po=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},po.apply(null,arguments)}function Uc(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function mw(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Uc(Object(r),!0).forEach(function(n){yw(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Uc(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function yw(e,t,r){return(t=gw(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function gw(e){var t=bw(e,"string");return typeof t=="symbol"?t:t+""}function bw(e,t){if(typeof e!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t||"default");if(typeof n!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function ww(e,t){if(e==null)return{};var r,n,i=xw(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],t.indexOf(r)===-1&&{}.propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}function xw(e,t){if(e==null)return{};var r={};for(var n in e)if({}.hasOwnProperty.call(e,n)){if(t.indexOf(n)!==-1)continue;r[n]=e[n]}return r}var Pw=(e,t,r,n,i,a)=>"M".concat(e,",").concat(i,"v").concat(n,"M").concat(a,",").concat(t,"h").concat(r),Ow=e=>{var{x:t=0,y:r=0,top:n=0,left:i=0,width:a=0,height:o=0,className:u}=e,c=ww(e,pw),s=mw({x:t,y:r,top:n,left:i,width:a,height:o},c);return!k(t)||!k(r)||!k(a)||!k(o)||!k(n)||!k(i)?null:p.createElement("path",po({},z(s,!0),{className:U("recharts-cross",u),d:Pw(t,r,a,o,n,i)}))};function Aw(e,t,r,n){var i=n/2;return{stroke:"none",fill:"#ccc",x:e==="horizontal"?t.x-i:r.left+.5,y:e==="horizontal"?r.top+.5:t.y-i,width:e==="horizontal"?n:r.width-1,height:e==="horizontal"?r.height-1:n}}function qc(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Sw(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?qc(Object(r),!0).forEach(function(n){Ew(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):qc(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function Ew(e,t,r){return(t=_w(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function _w(e){var t=Tw(e,"string");return typeof t=="symbol"?t:t+""}function Tw(e,t){if(typeof e!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t||"default");if(typeof n!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function at(e,t){var r=Sw({},e),n=t,i=Object.keys(t),a=i.reduce((o,u)=>(o[u]===void 0&&n[u]!==void 0&&(o[u]=n[u]),o),r);return a}var Ev={},_v={},Tv={};(function(e){Object.defineProperty(e,Symbol.toStringTag,{value:"Module"});function t(r){if(!r||typeof r!="object")return!1;const n=Object.getPrototypeOf(r);return n===null||n===Object.prototype||Object.getPrototypeOf(n)===null?Object.prototype.toString.call(r)==="[object Object]":!1}e.isPlainObject=t})(Tv);(function(e){Object.defineProperty(e,Symbol.toStringTag,{value:"Module"});const t=Tv,r=cu,n=zi,i=Fi,a=Ki;function o(s,l,f){return u(s,l,void 0,void 0,void 0,void 0,f)}function u(s,l,f,d,v,h,y){const m=y(s,l,f,d,v,h);if(m!==void 0)return m;if(typeof s==typeof l)switch(typeof s){case"bigint":case"string":case"boolean":case"symbol":case"undefined":return s===l;case"number":return s===l||Object.is(s,l);case"function":return s===l;case"object":return c(s,l,h,y)}return c(s,l,h,y)}function c(s,l,f,d){if(Object.is(s,l))return!0;let v=n.getTag(s),h=n.getTag(l);if(v===i.argumentsTag&&(v=i.objectTag),h===i.argumentsTag&&(h=i.objectTag),v!==h)return!1;switch(v){case i.stringTag:return s.toString()===l.toString();case i.numberTag:{const g=s.valueOf(),b=l.valueOf();return a.eq(g,b)}case i.booleanTag:case i.dateTag:case i.symbolTag:return Object.is(s.valueOf(),l.valueOf());case i.regexpTag:return s.source===l.source&&s.flags===l.flags;case i.functionTag:return s===l}f=f??new Map;const y=f.get(s),m=f.get(l);if(y!=null&&m!=null)return y===l;f.set(s,l),f.set(l,s);try{switch(v){case i.mapTag:{if(s.size!==l.size)return!1;for(const[g,b]of s.entries())if(!l.has(g)||!u(b,l.get(g),g,s,l,f,d))return!1;return!0}case i.setTag:{if(s.size!==l.size)return!1;const g=Array.from(s.values()),b=Array.from(l.values());for(let w=0;w<g.length;w++){const P=g[w],x=b.findIndex(O=>u(P,O,void 0,s,l,f,d));if(x===-1)return!1;b.splice(x,1)}return!0}case i.arrayTag:case i.uint8ArrayTag:case i.uint8ClampedArrayTag:case i.uint16ArrayTag:case i.uint32ArrayTag:case i.bigUint64ArrayTag:case i.int8ArrayTag:case i.int16ArrayTag:case i.int32ArrayTag:case i.bigInt64ArrayTag:case i.float32ArrayTag:case i.float64ArrayTag:{if(typeof Buffer<"u"&&Buffer.isBuffer(s)!==Buffer.isBuffer(l)||s.length!==l.length)return!1;for(let g=0;g<s.length;g++)if(!u(s[g],l[g],g,s,l,f,d))return!1;return!0}case i.arrayBufferTag:return s.byteLength!==l.byteLength?!1:c(new Uint8Array(s),new Uint8Array(l),f,d);case i.dataViewTag:return s.byteLength!==l.byteLength||s.byteOffset!==l.byteOffset?!1:c(new Uint8Array(s),new Uint8Array(l),f,d);case i.errorTag:return s.name===l.name&&s.message===l.message;case i.objectTag:{if(!(c(s.constructor,l.constructor,f,d)||t.isPlainObject(s)&&t.isPlainObject(l)))return!1;const b=[...Object.keys(s),...r.getSymbols(s)],w=[...Object.keys(l),...r.getSymbols(l)];if(b.length!==w.length)return!1;for(let P=0;P<b.length;P++){const x=b[P],O=s[x];if(!Object.hasOwn(l,x))return!1;const A=l[x];if(!u(O,A,x,s,l,f,d))return!1}return!0}default:return!1}}finally{f.delete(s),f.delete(l)}}e.isEqualWith=o})(_v);var jv={};(function(e){Object.defineProperty(e,Symbol.toStringTag,{value:"Module"});function t(){}e.noop=t})(jv);(function(e){Object.defineProperty(e,Symbol.toStringTag,{value:"Module"});const t=_v,r=jv;function n(i,a){return t.isEqualWith(i,a,r.noop)}e.isEqual=n})(Ev);var jw=Ev.isEqual;const Cw=Et(jw);function kw(e){var t={},r=()=>null,n=!1,i=null,a=o=>{if(!n){if(Array.isArray(o)){if(!o.length)return;var u=o,[c,...s]=u;if(typeof c=="number"){i=e.setTimeout(a.bind(null,s),c);return}a(c),i=e.setTimeout(a.bind(null,s));return}typeof o=="object"&&(t=o,r(t)),typeof o=="function"&&o()}};return{stop:()=>{n=!0},start:o=>{n=!1,i&&(i(),i=null),a(o)},subscribe:o=>(r=o,()=>{r=()=>null}),getTimeoutController:()=>e}}var ni=1e-4,Cv=(e,t)=>[0,3*e,3*t-6*e,3*e-3*t+1],kv=(e,t)=>e.map((r,n)=>r*t**n).reduce((r,n)=>r+n),Yc=(e,t)=>r=>{var n=Cv(e,t);return kv(n,r)},Mw=(e,t)=>r=>{var n=Cv(e,t),i=[...n.map((a,o)=>a*o).slice(1),0];return kv(i,r)},Hc=function(){for(var t,r,n,i,a=arguments.length,o=new Array(a),u=0;u<a;u++)o[u]=arguments[u];if(o.length===1)switch(o[0]){case"linear":[t,n,r,i]=[0,0,1,1];break;case"ease":[t,n,r,i]=[.25,.1,.25,1];break;case"ease-in":[t,n,r,i]=[.42,0,1,1];break;case"ease-out":[t,n,r,i]=[.42,0,.58,1];break;case"ease-in-out":[t,n,r,i]=[0,0,.58,1];break;default:{var c=o[0].split("(");c[0]==="cubic-bezier"&&c[1].split(")")[0].split(",").length===4&&([t,n,r,i]=c[1].split(")")[0].split(",").map(h=>parseFloat(h)))}}else o.length===4&&([t,n,r,i]=o);var s=Yc(t,r),l=Yc(n,i),f=Mw(t,r),d=h=>h>1?1:h<0?0:h,v=h=>{for(var y=h>1?1:h,m=y,g=0;g<8;++g){var b=s(m)-y,w=f(m);if(Math.abs(b-y)<ni||w<ni)return l(m);m=d(m-b/w)}return l(m)};return v.isStepper=!1,v},Dw=function(){var t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},{stiff:r=100,damping:n=8,dt:i=17}=t,a=(o,u,c)=>{var s=-(o-u)*r,l=c*n,f=c+(s-l)*i/1e3,d=c*i/1e3+o;return Math.abs(d-u)<ni&&Math.abs(f)<ni?[u,0]:[d,f]};return a.isStepper=!0,a.dt=i,a},Iw=e=>{if(typeof e=="string")switch(e){case"ease":case"ease-in-out":case"ease-out":case"ease-in":case"linear":return Hc(e);case"spring":return Dw();default:if(e.split("(")[0]==="cubic-bezier")return Hc(e)}return typeof e=="function"?e:null};function Gc(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Vc(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Gc(Object(r),!0).forEach(function(n){$w(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Gc(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function $w(e,t,r){return(t=Nw(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Nw(e){var t=Lw(e,"string");return typeof t=="symbol"?t:t+""}function Lw(e,t){if(typeof e!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t||"default");if(typeof n!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var Rw=e=>e.replace(/([A-Z])/g,t=>"-".concat(t.toLowerCase())),Bw=(e,t,r)=>e.map(n=>"".concat(Rw(n)," ").concat(t,"ms ").concat(r)).join(","),Kw=(e,t)=>[Object.keys(e),Object.keys(t)].reduce((r,n)=>r.filter(i=>n.includes(i))),an=(e,t)=>Object.keys(t).reduce((r,n)=>Vc(Vc({},r),{},{[n]:e(n,t[n])}),{});function Xc(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Pe(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Xc(Object(r),!0).forEach(function(n){zw(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Xc(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function zw(e,t,r){return(t=Fw(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Fw(e){var t=Ww(e,"string");return typeof t=="symbol"?t:t+""}function Ww(e,t){if(typeof e!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t||"default");if(typeof n!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var ii=(e,t,r)=>e+(t-e)*r,mo=e=>{var{from:t,to:r}=e;return t!==r},Mv=(e,t,r)=>{var n=an((i,a)=>{if(mo(a)){var[o,u]=e(a.from,a.to,a.velocity);return Pe(Pe({},a),{},{from:o,velocity:u})}return a},t);return r<1?an((i,a)=>mo(a)?Pe(Pe({},a),{},{velocity:ii(a.velocity,n[i].velocity,r),from:ii(a.from,n[i].from,r)}):a,t):Mv(e,n,r-1)};function Uw(e,t,r,n,i,a){var o,u=n.reduce((d,v)=>Pe(Pe({},d),{},{[v]:{from:e[v],velocity:0,to:t[v]}}),{}),c=()=>an((d,v)=>v.from,u),s=()=>!Object.values(u).filter(mo).length,l=null,f=d=>{o||(o=d);var v=d-o,h=v/r.dt;u=Mv(r,u,h),i(Pe(Pe(Pe({},e),t),c())),o=d,s()||(l=a.setTimeout(f))};return()=>(l=a.setTimeout(f),()=>{l()})}function qw(e,t,r,n,i,a,o){var u=null,c=i.reduce((f,d)=>Pe(Pe({},f),{},{[d]:[e[d],t[d]]}),{}),s,l=f=>{s||(s=f);var d=(f-s)/n,v=an((y,m)=>ii(...m,r(d)),c);if(a(Pe(Pe(Pe({},e),t),v)),d<1)u=o.setTimeout(l);else{var h=an((y,m)=>ii(...m,r(1)),c);a(Pe(Pe(Pe({},e),t),h))}};return()=>(u=o.setTimeout(l),()=>{u()})}const Yw=(e,t,r,n,i,a)=>{var o=Kw(e,t);return r.isStepper===!0?Uw(e,t,r,o,i,a):qw(e,t,r,n,o,i,a)};class Hw{setTimeout(t){var r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0,n=performance.now(),i=null,a=o=>{o-n>=r?t(o):typeof requestAnimationFrame=="function"&&(i=requestAnimationFrame(a))};return i=requestAnimationFrame(a),()=>{cancelAnimationFrame(i)}}}var Gw=["children","begin","duration","attributeName","easing","isActive","from","to","canBegin","onAnimationEnd","shouldReAnimate","onAnimationReStart","animationManager"];function yo(){return yo=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},yo.apply(null,arguments)}function Vw(e,t){if(e==null)return{};var r,n,i=Xw(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],t.indexOf(r)===-1&&{}.propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}function Xw(e,t){if(e==null)return{};var r={};for(var n in e)if({}.hasOwnProperty.call(e,n)){if(t.indexOf(n)!==-1)continue;r[n]=e[n]}return r}function Zc(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Dt(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Zc(Object(r),!0).forEach(function(n){Qt(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Zc(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function Qt(e,t,r){return(t=Zw(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Zw(e){var t=Jw(e,"string");return typeof t=="symbol"?t:t+""}function Jw(e,t){if(typeof e!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t||"default");if(typeof n!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function Qw(){return kw(new Hw)}class _u extends p.PureComponent{constructor(t,r){super(t,r),Qt(this,"mounted",!1),Qt(this,"manager",null),Qt(this,"stopJSAnimation",null),Qt(this,"unSubscribe",null);var{isActive:n,attributeName:i,from:a,to:o,children:u,duration:c,animationManager:s}=this.props;if(this.manager=s,this.handleStyleChange=this.handleStyleChange.bind(this),this.changeStyle=this.changeStyle.bind(this),!n||c<=0){this.state={style:{}},typeof u=="function"&&(this.state={style:o});return}if(a){if(typeof u=="function"){this.state={style:a};return}this.state={style:i?{[i]:a}:a}}else this.state={style:{}}}componentDidMount(){var{isActive:t,canBegin:r}=this.props;this.mounted=!0,!(!t||!r)&&this.runAnimation(this.props)}componentDidUpdate(t){var{isActive:r,canBegin:n,attributeName:i,shouldReAnimate:a,to:o,from:u}=this.props,{style:c}=this.state;if(n){if(!r){var s={style:i?{[i]:o}:o};this.state&&c&&(i&&c[i]!==o||!i&&c!==o)&&this.setState(s);return}if(!(Cw(t.to,o)&&t.canBegin&&t.isActive)){var l=!t.canBegin||!t.isActive;this.manager.stop(),this.stopJSAnimation&&this.stopJSAnimation();var f=l||a?u:t.to;if(this.state&&c){var d={style:i?{[i]:f}:f};(i&&c[i]!==f||!i&&c!==f)&&this.setState(d)}this.runAnimation(Dt(Dt({},this.props),{},{from:f,begin:0}))}}}componentWillUnmount(){this.mounted=!1;var{onAnimationEnd:t}=this.props;this.unSubscribe&&this.unSubscribe(),this.manager.stop(),this.stopJSAnimation&&this.stopJSAnimation(),t&&t()}handleStyleChange(t){this.changeStyle(t)}changeStyle(t){this.mounted&&this.setState({style:t})}runJSAnimation(t){var{from:r,to:n,duration:i,easing:a,begin:o,onAnimationEnd:u,onAnimationStart:c}=t,s=Yw(r,n,Iw(a),i,this.changeStyle,this.manager.getTimeoutController()),l=()=>{this.stopJSAnimation=s()};this.manager.start([c,o,l,i,u])}runAnimation(t){var{begin:r,duration:n,attributeName:i,to:a,easing:o,onAnimationStart:u,onAnimationEnd:c,children:s}=t;if(this.unSubscribe=this.manager.subscribe(this.handleStyleChange),typeof o=="function"||typeof s=="function"||o==="spring"){this.runJSAnimation(t);return}var l=i?{[i]:a}:a,f=Bw(Object.keys(l),n,o);this.manager.start([u,r,Dt(Dt({},l),{},{transition:f}),n,c])}render(){var t=this.props,{children:r,begin:n,duration:i,attributeName:a,easing:o,isActive:u,from:c,to:s,canBegin:l,onAnimationEnd:f,shouldReAnimate:d,onAnimationReStart:v,animationManager:h}=t,y=Vw(t,Gw),m=p.Children.count(r),g=this.state.style;if(typeof r=="function")return r(g);if(!u||m===0||i<=0)return r;var b=w=>{var{style:P={},className:x}=w.props,O=p.cloneElement(w,Dt(Dt({},y),{},{style:Dt(Dt({},P),g),className:x}));return O};return m===1?b(p.Children.only(r)):p.createElement("div",null,p.Children.map(r,w=>b(w)))}}Qt(_u,"displayName","Animate");Qt(_u,"defaultProps",{begin:0,duration:1e3,attributeName:"",easing:"ease",isActive:!0,canBegin:!0,onAnimationEnd:()=>{},onAnimationStart:()=>{}});var ex=p.createContext(null);function Bt(e){var t,r,n=p.useContext(ex);return p.createElement(_u,yo({},e,{animationManager:(t=(r=e.animationManager)!==null&&r!==void 0?r:n)!==null&&t!==void 0?t:Qw()}))}function ai(){return ai=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},ai.apply(null,arguments)}var Jc=(e,t,r,n,i)=>{var a=Math.min(Math.abs(r)/2,Math.abs(n)/2),o=n>=0?1:-1,u=r>=0?1:-1,c=n>=0&&r>=0||n<0&&r<0?1:0,s;if(a>0&&i instanceof Array){for(var l=[0,0,0,0],f=0,d=4;f<d;f++)l[f]=i[f]>a?a:i[f];s="M".concat(e,",").concat(t+o*l[0]),l[0]>0&&(s+="A ".concat(l[0],",").concat(l[0],",0,0,").concat(c,",").concat(e+u*l[0],",").concat(t)),s+="L ".concat(e+r-u*l[1],",").concat(t),l[1]>0&&(s+="A ".concat(l[1],",").concat(l[1],",0,0,").concat(c,`,
        `).concat(e+r,",").concat(t+o*l[1])),s+="L ".concat(e+r,",").concat(t+n-o*l[2]),l[2]>0&&(s+="A ".concat(l[2],",").concat(l[2],",0,0,").concat(c,`,
        `).concat(e+r-u*l[2],",").concat(t+n)),s+="L ".concat(e+u*l[3],",").concat(t+n),l[3]>0&&(s+="A ".concat(l[3],",").concat(l[3],",0,0,").concat(c,`,
        `).concat(e,",").concat(t+n-o*l[3])),s+="Z"}else if(a>0&&i===+i&&i>0){var v=Math.min(a,i);s="M ".concat(e,",").concat(t+o*v,`
            A `).concat(v,",").concat(v,",0,0,").concat(c,",").concat(e+u*v,",").concat(t,`
            L `).concat(e+r-u*v,",").concat(t,`
            A `).concat(v,",").concat(v,",0,0,").concat(c,",").concat(e+r,",").concat(t+o*v,`
            L `).concat(e+r,",").concat(t+n-o*v,`
            A `).concat(v,",").concat(v,",0,0,").concat(c,",").concat(e+r-u*v,",").concat(t+n,`
            L `).concat(e+u*v,",").concat(t+n,`
            A `).concat(v,",").concat(v,",0,0,").concat(c,",").concat(e,",").concat(t+n-o*v," Z")}else s="M ".concat(e,",").concat(t," h ").concat(r," v ").concat(n," h ").concat(-r," Z");return s},tx={x:0,y:0,width:0,height:0,radius:0,isAnimationActive:!1,isUpdateAnimationActive:!1,animationBegin:0,animationDuration:1500,animationEasing:"ease"},Dv=e=>{var t=at(e,tx),r=p.useRef(null),[n,i]=p.useState(-1);p.useEffect(()=>{if(r.current&&r.current.getTotalLength)try{var g=r.current.getTotalLength();g&&i(g)}catch{}},[]);var{x:a,y:o,width:u,height:c,radius:s,className:l}=t,{animationEasing:f,animationDuration:d,animationBegin:v,isAnimationActive:h,isUpdateAnimationActive:y}=t;if(a!==+a||o!==+o||u!==+u||c!==+c||u===0||c===0)return null;var m=U("recharts-rectangle",l);return y?p.createElement(Bt,{canBegin:n>0,from:{width:u,height:c,x:a,y:o},to:{width:u,height:c,x:a,y:o},duration:d,animationEasing:f,isActive:y},g=>{var{width:b,height:w,x:P,y:x}=g;return p.createElement(Bt,{canBegin:n>0,from:"0px ".concat(n===-1?1:n,"px"),to:"".concat(n,"px 0px"),attributeName:"strokeDasharray",begin:v,duration:d,isActive:h,easing:f},p.createElement("path",ai({},z(t,!0),{className:m,d:Jc(P,x,b,w,s),ref:r})))}):p.createElement("path",ai({},z(t,!0),{className:m,d:Jc(a,o,u,c,s)}))};function Iv(e){var{cx:t,cy:r,radius:n,startAngle:i,endAngle:a}=e,o=ce(t,r,n,i),u=ce(t,r,n,a);return{points:[o,u],cx:t,cy:r,radius:n,startAngle:i,endAngle:a}}function go(){return go=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},go.apply(null,arguments)}var rx=(e,t)=>{var r=me(t-e),n=Math.min(Math.abs(t-e),359.999);return r*n},Nn=e=>{var{cx:t,cy:r,radius:n,angle:i,sign:a,isExternal:o,cornerRadius:u,cornerIsExternal:c}=e,s=u*(o?1:-1)+n,l=Math.asin(u/s)/ri,f=c?i:i+a*l,d=ce(t,r,s,f),v=ce(t,r,n,f),h=c?i-a*l:i,y=ce(t,r,s*Math.cos(l*ri),h);return{center:d,circleTangency:v,lineTangency:y,theta:l}},$v=e=>{var{cx:t,cy:r,innerRadius:n,outerRadius:i,startAngle:a,endAngle:o}=e,u=rx(a,o),c=a+u,s=ce(t,r,i,a),l=ce(t,r,i,c),f="M ".concat(s.x,",").concat(s.y,`
    A `).concat(i,",").concat(i,`,0,
    `).concat(+(Math.abs(u)>180),",").concat(+(a>c),`,
    `).concat(l.x,",").concat(l.y,`
  `);if(n>0){var d=ce(t,r,n,a),v=ce(t,r,n,c);f+="L ".concat(v.x,",").concat(v.y,`
            A `).concat(n,",").concat(n,`,0,
            `).concat(+(Math.abs(u)>180),",").concat(+(a<=c),`,
            `).concat(d.x,",").concat(d.y," Z")}else f+="L ".concat(t,",").concat(r," Z");return f},nx=e=>{var{cx:t,cy:r,innerRadius:n,outerRadius:i,cornerRadius:a,forceCornerRadius:o,cornerIsExternal:u,startAngle:c,endAngle:s}=e,l=me(s-c),{circleTangency:f,lineTangency:d,theta:v}=Nn({cx:t,cy:r,radius:i,angle:c,sign:l,cornerRadius:a,cornerIsExternal:u}),{circleTangency:h,lineTangency:y,theta:m}=Nn({cx:t,cy:r,radius:i,angle:s,sign:-l,cornerRadius:a,cornerIsExternal:u}),g=u?Math.abs(c-s):Math.abs(c-s)-v-m;if(g<0)return o?"M ".concat(d.x,",").concat(d.y,`
        a`).concat(a,",").concat(a,",0,0,1,").concat(a*2,`,0
        a`).concat(a,",").concat(a,",0,0,1,").concat(-a*2,`,0
      `):$v({cx:t,cy:r,innerRadius:n,outerRadius:i,startAngle:c,endAngle:s});var b="M ".concat(d.x,",").concat(d.y,`
    A`).concat(a,",").concat(a,",0,0,").concat(+(l<0),",").concat(f.x,",").concat(f.y,`
    A`).concat(i,",").concat(i,",0,").concat(+(g>180),",").concat(+(l<0),",").concat(h.x,",").concat(h.y,`
    A`).concat(a,",").concat(a,",0,0,").concat(+(l<0),",").concat(y.x,",").concat(y.y,`
  `);if(n>0){var{circleTangency:w,lineTangency:P,theta:x}=Nn({cx:t,cy:r,radius:n,angle:c,sign:l,isExternal:!0,cornerRadius:a,cornerIsExternal:u}),{circleTangency:O,lineTangency:A,theta:E}=Nn({cx:t,cy:r,radius:n,angle:s,sign:-l,isExternal:!0,cornerRadius:a,cornerIsExternal:u}),_=u?Math.abs(c-s):Math.abs(c-s)-x-E;if(_<0&&a===0)return"".concat(b,"L").concat(t,",").concat(r,"Z");b+="L".concat(A.x,",").concat(A.y,`
      A`).concat(a,",").concat(a,",0,0,").concat(+(l<0),",").concat(O.x,",").concat(O.y,`
      A`).concat(n,",").concat(n,",0,").concat(+(_>180),",").concat(+(l>0),",").concat(w.x,",").concat(w.y,`
      A`).concat(a,",").concat(a,",0,0,").concat(+(l<0),",").concat(P.x,",").concat(P.y,"Z")}else b+="L".concat(t,",").concat(r,"Z");return b},ix={cx:0,cy:0,innerRadius:0,outerRadius:0,startAngle:0,endAngle:0,cornerRadius:0,forceCornerRadius:!1,cornerIsExternal:!1},Nv=e=>{var t=at(e,ix),{cx:r,cy:n,innerRadius:i,outerRadius:a,cornerRadius:o,forceCornerRadius:u,cornerIsExternal:c,startAngle:s,endAngle:l,className:f}=t;if(a<i||s===l)return null;var d=U("recharts-sector",f),v=a-i,h=Te(o,v,0,!0),y;return h>0&&Math.abs(s-l)<360?y=nx({cx:r,cy:n,innerRadius:i,outerRadius:a,cornerRadius:Math.min(h,v/2),forceCornerRadius:u,cornerIsExternal:c,startAngle:s,endAngle:l}):y=$v({cx:r,cy:n,innerRadius:i,outerRadius:a,startAngle:s,endAngle:l}),p.createElement("path",go({},z(t,!0),{className:d,d:y}))};function ax(e,t,r){var n,i,a,o;if(e==="horizontal")n=t.x,a=n,i=r.top,o=r.top+r.height;else if(e==="vertical")i=t.y,o=i,n=r.left,a=r.left+r.width;else if(t.cx!=null&&t.cy!=null)if(e==="centric"){var{cx:u,cy:c,innerRadius:s,outerRadius:l,angle:f}=t,d=ce(u,c,s,f),v=ce(u,c,l,f);n=d.x,i=d.y,a=v.x,o=v.y}else return Iv(t);return[{x:n,y:i},{x:a,y:o}]}var Lv={},Rv={},Bv={};(function(e){Object.defineProperty(e,Symbol.toStringTag,{value:"Module"});const t=du;function r(n){return t.isSymbol(n)?NaN:Number(n)}e.toNumber=r})(Bv);(function(e){Object.defineProperty(e,Symbol.toStringTag,{value:"Module"});const t=Bv;function r(n){return n?(n=t.toNumber(n),n===1/0||n===-1/0?(n<0?-1:1)*Number.MAX_VALUE:n===n?n:0):n===0?n:0}e.toFinite=r})(Rv);(function(e){Object.defineProperty(e,Symbol.toStringTag,{value:"Module"});const t=vu,r=Rv;function n(i,a,o){o&&typeof o!="number"&&t.isIterateeCall(i,a,o)&&(a=o=void 0),i=r.toFinite(i),a===void 0?(a=i,i=0):a=r.toFinite(a),o=o===void 0?i<a?1:-1:r.toFinite(o);const u=Math.max(Math.ceil((a-i)/(o||1)),0),c=new Array(u);for(let s=0;s<u;s++)c[s]=i,i+=o;return c}e.range=n})(Lv);var ox=Lv.range;const Kv=Et(ox);function Nt(e,t){return e==null||t==null?NaN:e<t?-1:e>t?1:e>=t?0:NaN}function ux(e,t){return e==null||t==null?NaN:t<e?-1:t>e?1:t>=e?0:NaN}function Tu(e){let t,r,n;e.length!==2?(t=Nt,r=(u,c)=>Nt(e(u),c),n=(u,c)=>e(u)-c):(t=e===Nt||e===ux?e:lx,r=e,n=e);function i(u,c,s=0,l=u.length){if(s<l){if(t(c,c)!==0)return l;do{const f=s+l>>>1;r(u[f],c)<0?s=f+1:l=f}while(s<l)}return s}function a(u,c,s=0,l=u.length){if(s<l){if(t(c,c)!==0)return l;do{const f=s+l>>>1;r(u[f],c)<=0?s=f+1:l=f}while(s<l)}return s}function o(u,c,s=0,l=u.length){const f=i(u,c,s,l-1);return f>s&&n(u[f-1],c)>-n(u[f],c)?f-1:f}return{left:i,center:o,right:a}}function lx(){return 0}function zv(e){return e===null?NaN:+e}function*cx(e,t){if(t===void 0)for(let r of e)r!=null&&(r=+r)>=r&&(yield r);else{let r=-1;for(let n of e)(n=t(n,++r,e))!=null&&(n=+n)>=n&&(yield n)}}const sx=Tu(Nt),fx=sx.right;Tu(zv).center;const gn=fx;class Qc extends Map{constructor(t,r=hx){if(super(),Object.defineProperties(this,{_intern:{value:new Map},_key:{value:r}}),t!=null)for(const[n,i]of t)this.set(n,i)}get(t){return super.get(es(this,t))}has(t){return super.has(es(this,t))}set(t,r){return super.set(dx(this,t),r)}delete(t){return super.delete(vx(this,t))}}function es({_intern:e,_key:t},r){const n=t(r);return e.has(n)?e.get(n):r}function dx({_intern:e,_key:t},r){const n=t(r);return e.has(n)?e.get(n):(e.set(n,r),r)}function vx({_intern:e,_key:t},r){const n=t(r);return e.has(n)&&(r=e.get(n),e.delete(n)),r}function hx(e){return e!==null&&typeof e=="object"?e.valueOf():e}function px(e=Nt){if(e===Nt)return Fv;if(typeof e!="function")throw new TypeError("compare is not a function");return(t,r)=>{const n=e(t,r);return n||n===0?n:(e(r,r)===0)-(e(t,t)===0)}}function Fv(e,t){return(e==null||!(e>=e))-(t==null||!(t>=t))||(e<t?-1:e>t?1:0)}const mx=Math.sqrt(50),yx=Math.sqrt(10),gx=Math.sqrt(2);function oi(e,t,r){const n=(t-e)/Math.max(0,r),i=Math.floor(Math.log10(n)),a=n/Math.pow(10,i),o=a>=mx?10:a>=yx?5:a>=gx?2:1;let u,c,s;return i<0?(s=Math.pow(10,-i)/o,u=Math.round(e*s),c=Math.round(t*s),u/s<e&&++u,c/s>t&&--c,s=-s):(s=Math.pow(10,i)*o,u=Math.round(e/s),c=Math.round(t/s),u*s<e&&++u,c*s>t&&--c),c<u&&.5<=r&&r<2?oi(e,t,r*2):[u,c,s]}function bo(e,t,r){if(t=+t,e=+e,r=+r,!(r>0))return[];if(e===t)return[e];const n=t<e,[i,a,o]=n?oi(t,e,r):oi(e,t,r);if(!(a>=i))return[];const u=a-i+1,c=new Array(u);if(n)if(o<0)for(let s=0;s<u;++s)c[s]=(a-s)/-o;else for(let s=0;s<u;++s)c[s]=(a-s)*o;else if(o<0)for(let s=0;s<u;++s)c[s]=(i+s)/-o;else for(let s=0;s<u;++s)c[s]=(i+s)*o;return c}function wo(e,t,r){return t=+t,e=+e,r=+r,oi(e,t,r)[2]}function xo(e,t,r){t=+t,e=+e,r=+r;const n=t<e,i=n?wo(t,e,r):wo(e,t,r);return(n?-1:1)*(i<0?1/-i:i)}function ts(e,t){let r;if(t===void 0)for(const n of e)n!=null&&(r<n||r===void 0&&n>=n)&&(r=n);else{let n=-1;for(let i of e)(i=t(i,++n,e))!=null&&(r<i||r===void 0&&i>=i)&&(r=i)}return r}function rs(e,t){let r;if(t===void 0)for(const n of e)n!=null&&(r>n||r===void 0&&n>=n)&&(r=n);else{let n=-1;for(let i of e)(i=t(i,++n,e))!=null&&(r>i||r===void 0&&i>=i)&&(r=i)}return r}function Wv(e,t,r=0,n=1/0,i){if(t=Math.floor(t),r=Math.floor(Math.max(0,r)),n=Math.floor(Math.min(e.length-1,n)),!(r<=t&&t<=n))return e;for(i=i===void 0?Fv:px(i);n>r;){if(n-r>600){const c=n-r+1,s=t-r+1,l=Math.log(c),f=.5*Math.exp(2*l/3),d=.5*Math.sqrt(l*f*(c-f)/c)*(s-c/2<0?-1:1),v=Math.max(r,Math.floor(t-s*f/c+d)),h=Math.min(n,Math.floor(t+(c-s)*f/c+d));Wv(e,t,v,h,i)}const a=e[t];let o=r,u=n;for(Yr(e,r,t),i(e[n],a)>0&&Yr(e,r,n);o<u;){for(Yr(e,o,u),++o,--u;i(e[o],a)<0;)++o;for(;i(e[u],a)>0;)--u}i(e[r],a)===0?Yr(e,r,u):(++u,Yr(e,u,n)),u<=t&&(r=u+1),t<=u&&(n=u-1)}return e}function Yr(e,t,r){const n=e[t];e[t]=e[r],e[r]=n}function bx(e,t,r){if(e=Float64Array.from(cx(e,r)),!(!(n=e.length)||isNaN(t=+t))){if(t<=0||n<2)return rs(e);if(t>=1)return ts(e);var n,i=(n-1)*t,a=Math.floor(i),o=ts(Wv(e,a).subarray(0,a+1)),u=rs(e.subarray(a+1));return o+(u-o)*(i-a)}}function wx(e,t,r=zv){if(!(!(n=e.length)||isNaN(t=+t))){if(t<=0||n<2)return+r(e[0],0,e);if(t>=1)return+r(e[n-1],n-1,e);var n,i=(n-1)*t,a=Math.floor(i),o=+r(e[a],a,e),u=+r(e[a+1],a+1,e);return o+(u-o)*(i-a)}}function xx(e,t,r){e=+e,t=+t,r=(i=arguments.length)<2?(t=e,e=0,1):i<3?1:+r;for(var n=-1,i=Math.max(0,Math.ceil((t-e)/r))|0,a=new Array(i);++n<i;)a[n]=e+n*r;return a}function ot(e,t){switch(arguments.length){case 0:break;case 1:this.range(e);break;default:this.range(t).domain(e);break}return this}function jt(e,t){switch(arguments.length){case 0:break;case 1:{typeof e=="function"?this.interpolator(e):this.range(e);break}default:{this.domain(e),typeof t=="function"?this.interpolator(t):this.range(t);break}}return this}const Po=Symbol("implicit");function ju(){var e=new Qc,t=[],r=[],n=Po;function i(a){let o=e.get(a);if(o===void 0){if(n!==Po)return n;e.set(a,o=t.push(a)-1)}return r[o%r.length]}return i.domain=function(a){if(!arguments.length)return t.slice();t=[],e=new Qc;for(const o of a)e.has(o)||e.set(o,t.push(o)-1);return i},i.range=function(a){return arguments.length?(r=Array.from(a),i):r.slice()},i.unknown=function(a){return arguments.length?(n=a,i):n},i.copy=function(){return ju(t,r).unknown(n)},ot.apply(i,arguments),i}function Cu(){var e=ju().unknown(void 0),t=e.domain,r=e.range,n=0,i=1,a,o,u=!1,c=0,s=0,l=.5;delete e.unknown;function f(){var d=t().length,v=i<n,h=v?i:n,y=v?n:i;a=(y-h)/Math.max(1,d-c+s*2),u&&(a=Math.floor(a)),h+=(y-h-a*(d-c))*l,o=a*(1-c),u&&(h=Math.round(h),o=Math.round(o));var m=xx(d).map(function(g){return h+a*g});return r(v?m.reverse():m)}return e.domain=function(d){return arguments.length?(t(d),f()):t()},e.range=function(d){return arguments.length?([n,i]=d,n=+n,i=+i,f()):[n,i]},e.rangeRound=function(d){return[n,i]=d,n=+n,i=+i,u=!0,f()},e.bandwidth=function(){return o},e.step=function(){return a},e.round=function(d){return arguments.length?(u=!!d,f()):u},e.padding=function(d){return arguments.length?(c=Math.min(1,s=+d),f()):c},e.paddingInner=function(d){return arguments.length?(c=Math.min(1,d),f()):c},e.paddingOuter=function(d){return arguments.length?(s=+d,f()):s},e.align=function(d){return arguments.length?(l=Math.max(0,Math.min(1,d)),f()):l},e.copy=function(){return Cu(t(),[n,i]).round(u).paddingInner(c).paddingOuter(s).align(l)},ot.apply(f(),arguments)}function Uv(e){var t=e.copy;return e.padding=e.paddingOuter,delete e.paddingInner,delete e.paddingOuter,e.copy=function(){return Uv(t())},e}function Px(){return Uv(Cu.apply(null,arguments).paddingInner(1))}function ku(e,t,r){e.prototype=t.prototype=r,r.constructor=e}function qv(e,t){var r=Object.create(e.prototype);for(var n in t)r[n]=t[n];return r}function bn(){}var on=.7,ui=1/on,Or="\\s*([+-]?\\d+)\\s*",un="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)\\s*",dt="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)%\\s*",Ox=/^#([0-9a-f]{3,8})$/,Ax=new RegExp(`^rgb\\(${Or},${Or},${Or}\\)$`),Sx=new RegExp(`^rgb\\(${dt},${dt},${dt}\\)$`),Ex=new RegExp(`^rgba\\(${Or},${Or},${Or},${un}\\)$`),_x=new RegExp(`^rgba\\(${dt},${dt},${dt},${un}\\)$`),Tx=new RegExp(`^hsl\\(${un},${dt},${dt}\\)$`),jx=new RegExp(`^hsla\\(${un},${dt},${dt},${un}\\)$`),ns={aliceblue:15792383,antiquewhite:16444375,aqua:65535,aquamarine:8388564,azure:15794175,beige:16119260,bisque:16770244,black:0,blanchedalmond:16772045,blue:255,blueviolet:9055202,brown:10824234,burlywood:14596231,cadetblue:6266528,chartreuse:8388352,chocolate:13789470,coral:16744272,cornflowerblue:6591981,cornsilk:16775388,crimson:14423100,cyan:65535,darkblue:139,darkcyan:35723,darkgoldenrod:12092939,darkgray:11119017,darkgreen:25600,darkgrey:11119017,darkkhaki:12433259,darkmagenta:9109643,darkolivegreen:5597999,darkorange:16747520,darkorchid:10040012,darkred:9109504,darksalmon:15308410,darkseagreen:9419919,darkslateblue:4734347,darkslategray:3100495,darkslategrey:3100495,darkturquoise:52945,darkviolet:9699539,deeppink:16716947,deepskyblue:49151,dimgray:6908265,dimgrey:6908265,dodgerblue:2003199,firebrick:11674146,floralwhite:16775920,forestgreen:2263842,fuchsia:16711935,gainsboro:14474460,ghostwhite:16316671,gold:16766720,goldenrod:14329120,gray:8421504,green:32768,greenyellow:11403055,grey:8421504,honeydew:15794160,hotpink:16738740,indianred:13458524,indigo:4915330,ivory:16777200,khaki:15787660,lavender:15132410,lavenderblush:16773365,lawngreen:8190976,lemonchiffon:16775885,lightblue:11393254,lightcoral:15761536,lightcyan:14745599,lightgoldenrodyellow:16448210,lightgray:13882323,lightgreen:9498256,lightgrey:13882323,lightpink:16758465,lightsalmon:16752762,lightseagreen:2142890,lightskyblue:8900346,lightslategray:7833753,lightslategrey:7833753,lightsteelblue:11584734,lightyellow:16777184,lime:65280,limegreen:3329330,linen:16445670,magenta:16711935,maroon:8388608,mediumaquamarine:6737322,mediumblue:205,mediumorchid:12211667,mediumpurple:9662683,mediumseagreen:3978097,mediumslateblue:8087790,mediumspringgreen:64154,mediumturquoise:4772300,mediumvioletred:13047173,midnightblue:1644912,mintcream:16121850,mistyrose:16770273,moccasin:16770229,navajowhite:16768685,navy:128,oldlace:16643558,olive:8421376,olivedrab:7048739,orange:16753920,orangered:16729344,orchid:14315734,palegoldenrod:15657130,palegreen:10025880,paleturquoise:11529966,palevioletred:14381203,papayawhip:16773077,peachpuff:16767673,peru:13468991,pink:16761035,plum:14524637,powderblue:11591910,purple:8388736,rebeccapurple:6697881,red:16711680,rosybrown:12357519,royalblue:4286945,saddlebrown:9127187,salmon:16416882,sandybrown:16032864,seagreen:3050327,seashell:16774638,sienna:10506797,silver:12632256,skyblue:8900331,slateblue:6970061,slategray:7372944,slategrey:7372944,snow:16775930,springgreen:65407,steelblue:4620980,tan:13808780,teal:32896,thistle:14204888,tomato:16737095,turquoise:4251856,violet:15631086,wheat:16113331,white:16777215,whitesmoke:16119285,yellow:16776960,yellowgreen:10145074};ku(bn,ln,{copy(e){return Object.assign(new this.constructor,this,e)},displayable(){return this.rgb().displayable()},hex:is,formatHex:is,formatHex8:Cx,formatHsl:kx,formatRgb:as,toString:as});function is(){return this.rgb().formatHex()}function Cx(){return this.rgb().formatHex8()}function kx(){return Yv(this).formatHsl()}function as(){return this.rgb().formatRgb()}function ln(e){var t,r;return e=(e+"").trim().toLowerCase(),(t=Ox.exec(e))?(r=t[1].length,t=parseInt(t[1],16),r===6?os(t):r===3?new Me(t>>8&15|t>>4&240,t>>4&15|t&240,(t&15)<<4|t&15,1):r===8?Ln(t>>24&255,t>>16&255,t>>8&255,(t&255)/255):r===4?Ln(t>>12&15|t>>8&240,t>>8&15|t>>4&240,t>>4&15|t&240,((t&15)<<4|t&15)/255):null):(t=Ax.exec(e))?new Me(t[1],t[2],t[3],1):(t=Sx.exec(e))?new Me(t[1]*255/100,t[2]*255/100,t[3]*255/100,1):(t=Ex.exec(e))?Ln(t[1],t[2],t[3],t[4]):(t=_x.exec(e))?Ln(t[1]*255/100,t[2]*255/100,t[3]*255/100,t[4]):(t=Tx.exec(e))?cs(t[1],t[2]/100,t[3]/100,1):(t=jx.exec(e))?cs(t[1],t[2]/100,t[3]/100,t[4]):ns.hasOwnProperty(e)?os(ns[e]):e==="transparent"?new Me(NaN,NaN,NaN,0):null}function os(e){return new Me(e>>16&255,e>>8&255,e&255,1)}function Ln(e,t,r,n){return n<=0&&(e=t=r=NaN),new Me(e,t,r,n)}function Mx(e){return e instanceof bn||(e=ln(e)),e?(e=e.rgb(),new Me(e.r,e.g,e.b,e.opacity)):new Me}function Oo(e,t,r,n){return arguments.length===1?Mx(e):new Me(e,t,r,n??1)}function Me(e,t,r,n){this.r=+e,this.g=+t,this.b=+r,this.opacity=+n}ku(Me,Oo,qv(bn,{brighter(e){return e=e==null?ui:Math.pow(ui,e),new Me(this.r*e,this.g*e,this.b*e,this.opacity)},darker(e){return e=e==null?on:Math.pow(on,e),new Me(this.r*e,this.g*e,this.b*e,this.opacity)},rgb(){return this},clamp(){return new Me(ar(this.r),ar(this.g),ar(this.b),li(this.opacity))},displayable(){return-.5<=this.r&&this.r<255.5&&-.5<=this.g&&this.g<255.5&&-.5<=this.b&&this.b<255.5&&0<=this.opacity&&this.opacity<=1},hex:us,formatHex:us,formatHex8:Dx,formatRgb:ls,toString:ls}));function us(){return`#${er(this.r)}${er(this.g)}${er(this.b)}`}function Dx(){return`#${er(this.r)}${er(this.g)}${er(this.b)}${er((isNaN(this.opacity)?1:this.opacity)*255)}`}function ls(){const e=li(this.opacity);return`${e===1?"rgb(":"rgba("}${ar(this.r)}, ${ar(this.g)}, ${ar(this.b)}${e===1?")":`, ${e})`}`}function li(e){return isNaN(e)?1:Math.max(0,Math.min(1,e))}function ar(e){return Math.max(0,Math.min(255,Math.round(e)||0))}function er(e){return e=ar(e),(e<16?"0":"")+e.toString(16)}function cs(e,t,r,n){return n<=0?e=t=r=NaN:r<=0||r>=1?e=t=NaN:t<=0&&(e=NaN),new ct(e,t,r,n)}function Yv(e){if(e instanceof ct)return new ct(e.h,e.s,e.l,e.opacity);if(e instanceof bn||(e=ln(e)),!e)return new ct;if(e instanceof ct)return e;e=e.rgb();var t=e.r/255,r=e.g/255,n=e.b/255,i=Math.min(t,r,n),a=Math.max(t,r,n),o=NaN,u=a-i,c=(a+i)/2;return u?(t===a?o=(r-n)/u+(r<n)*6:r===a?o=(n-t)/u+2:o=(t-r)/u+4,u/=c<.5?a+i:2-a-i,o*=60):u=c>0&&c<1?0:o,new ct(o,u,c,e.opacity)}function Ix(e,t,r,n){return arguments.length===1?Yv(e):new ct(e,t,r,n??1)}function ct(e,t,r,n){this.h=+e,this.s=+t,this.l=+r,this.opacity=+n}ku(ct,Ix,qv(bn,{brighter(e){return e=e==null?ui:Math.pow(ui,e),new ct(this.h,this.s,this.l*e,this.opacity)},darker(e){return e=e==null?on:Math.pow(on,e),new ct(this.h,this.s,this.l*e,this.opacity)},rgb(){var e=this.h%360+(this.h<0)*360,t=isNaN(e)||isNaN(this.s)?0:this.s,r=this.l,n=r+(r<.5?r:1-r)*t,i=2*r-n;return new Me(Ia(e>=240?e-240:e+120,i,n),Ia(e,i,n),Ia(e<120?e+240:e-120,i,n),this.opacity)},clamp(){return new ct(ss(this.h),Rn(this.s),Rn(this.l),li(this.opacity))},displayable(){return(0<=this.s&&this.s<=1||isNaN(this.s))&&0<=this.l&&this.l<=1&&0<=this.opacity&&this.opacity<=1},formatHsl(){const e=li(this.opacity);return`${e===1?"hsl(":"hsla("}${ss(this.h)}, ${Rn(this.s)*100}%, ${Rn(this.l)*100}%${e===1?")":`, ${e})`}`}}));function ss(e){return e=(e||0)%360,e<0?e+360:e}function Rn(e){return Math.max(0,Math.min(1,e||0))}function Ia(e,t,r){return(e<60?t+(r-t)*e/60:e<180?r:e<240?t+(r-t)*(240-e)/60:t)*255}const Mu=e=>()=>e;function $x(e,t){return function(r){return e+r*t}}function Nx(e,t,r){return e=Math.pow(e,r),t=Math.pow(t,r)-e,r=1/r,function(n){return Math.pow(e+n*t,r)}}function Lx(e){return(e=+e)==1?Hv:function(t,r){return r-t?Nx(t,r,e):Mu(isNaN(t)?r:t)}}function Hv(e,t){var r=t-e;return r?$x(e,r):Mu(isNaN(e)?t:e)}const fs=function e(t){var r=Lx(t);function n(i,a){var o=r((i=Oo(i)).r,(a=Oo(a)).r),u=r(i.g,a.g),c=r(i.b,a.b),s=Hv(i.opacity,a.opacity);return function(l){return i.r=o(l),i.g=u(l),i.b=c(l),i.opacity=s(l),i+""}}return n.gamma=e,n}(1);function Rx(e,t){t||(t=[]);var r=e?Math.min(t.length,e.length):0,n=t.slice(),i;return function(a){for(i=0;i<r;++i)n[i]=e[i]*(1-a)+t[i]*a;return n}}function Bx(e){return ArrayBuffer.isView(e)&&!(e instanceof DataView)}function Kx(e,t){var r=t?t.length:0,n=e?Math.min(r,e.length):0,i=new Array(n),a=new Array(r),o;for(o=0;o<n;++o)i[o]=$r(e[o],t[o]);for(;o<r;++o)a[o]=t[o];return function(u){for(o=0;o<n;++o)a[o]=i[o](u);return a}}function zx(e,t){var r=new Date;return e=+e,t=+t,function(n){return r.setTime(e*(1-n)+t*n),r}}function ci(e,t){return e=+e,t=+t,function(r){return e*(1-r)+t*r}}function Fx(e,t){var r={},n={},i;(e===null||typeof e!="object")&&(e={}),(t===null||typeof t!="object")&&(t={});for(i in t)i in e?r[i]=$r(e[i],t[i]):n[i]=t[i];return function(a){for(i in r)n[i]=r[i](a);return n}}var Ao=/[-+]?(?:\d+\.?\d*|\.?\d+)(?:[eE][-+]?\d+)?/g,$a=new RegExp(Ao.source,"g");function Wx(e){return function(){return e}}function Ux(e){return function(t){return e(t)+""}}function qx(e,t){var r=Ao.lastIndex=$a.lastIndex=0,n,i,a,o=-1,u=[],c=[];for(e=e+"",t=t+"";(n=Ao.exec(e))&&(i=$a.exec(t));)(a=i.index)>r&&(a=t.slice(r,a),u[o]?u[o]+=a:u[++o]=a),(n=n[0])===(i=i[0])?u[o]?u[o]+=i:u[++o]=i:(u[++o]=null,c.push({i:o,x:ci(n,i)})),r=$a.lastIndex;return r<t.length&&(a=t.slice(r),u[o]?u[o]+=a:u[++o]=a),u.length<2?c[0]?Ux(c[0].x):Wx(t):(t=c.length,function(s){for(var l=0,f;l<t;++l)u[(f=c[l]).i]=f.x(s);return u.join("")})}function $r(e,t){var r=typeof t,n;return t==null||r==="boolean"?Mu(t):(r==="number"?ci:r==="string"?(n=ln(t))?(t=n,fs):qx:t instanceof ln?fs:t instanceof Date?zx:Bx(t)?Rx:Array.isArray(t)?Kx:typeof t.valueOf!="function"&&typeof t.toString!="function"||isNaN(t)?Fx:ci)(e,t)}function Du(e,t){return e=+e,t=+t,function(r){return Math.round(e*(1-r)+t*r)}}function Yx(e,t){t===void 0&&(t=e,e=$r);for(var r=0,n=t.length-1,i=t[0],a=new Array(n<0?0:n);r<n;)a[r]=e(i,i=t[++r]);return function(o){var u=Math.max(0,Math.min(n-1,Math.floor(o*=n)));return a[u](o-u)}}function Hx(e){return function(){return e}}function si(e){return+e}var ds=[0,1];function _e(e){return e}function So(e,t){return(t-=e=+e)?function(r){return(r-e)/t}:Hx(isNaN(t)?NaN:.5)}function Gx(e,t){var r;return e>t&&(r=e,e=t,t=r),function(n){return Math.max(e,Math.min(t,n))}}function Vx(e,t,r){var n=e[0],i=e[1],a=t[0],o=t[1];return i<n?(n=So(i,n),a=r(o,a)):(n=So(n,i),a=r(a,o)),function(u){return a(n(u))}}function Xx(e,t,r){var n=Math.min(e.length,t.length)-1,i=new Array(n),a=new Array(n),o=-1;for(e[n]<e[0]&&(e=e.slice().reverse(),t=t.slice().reverse());++o<n;)i[o]=So(e[o],e[o+1]),a[o]=r(t[o],t[o+1]);return function(u){var c=gn(e,u,1,n)-1;return a[c](i[c](u))}}function wn(e,t){return t.domain(e.domain()).range(e.range()).interpolate(e.interpolate()).clamp(e.clamp()).unknown(e.unknown())}function ta(){var e=ds,t=ds,r=$r,n,i,a,o=_e,u,c,s;function l(){var d=Math.min(e.length,t.length);return o!==_e&&(o=Gx(e[0],e[d-1])),u=d>2?Xx:Vx,c=s=null,f}function f(d){return d==null||isNaN(d=+d)?a:(c||(c=u(e.map(n),t,r)))(n(o(d)))}return f.invert=function(d){return o(i((s||(s=u(t,e.map(n),ci)))(d)))},f.domain=function(d){return arguments.length?(e=Array.from(d,si),l()):e.slice()},f.range=function(d){return arguments.length?(t=Array.from(d),l()):t.slice()},f.rangeRound=function(d){return t=Array.from(d),r=Du,l()},f.clamp=function(d){return arguments.length?(o=d?!0:_e,l()):o!==_e},f.interpolate=function(d){return arguments.length?(r=d,l()):r},f.unknown=function(d){return arguments.length?(a=d,f):a},function(d,v){return n=d,i=v,l()}}function Iu(){return ta()(_e,_e)}function Zx(e){return Math.abs(e=Math.round(e))>=1e21?e.toLocaleString("en").replace(/,/g,""):e.toString(10)}function fi(e,t){if((r=(e=t?e.toExponential(t-1):e.toExponential()).indexOf("e"))<0)return null;var r,n=e.slice(0,r);return[n.length>1?n[0]+n.slice(2):n,+e.slice(r+1)]}function jr(e){return e=fi(Math.abs(e)),e?e[1]:NaN}function Jx(e,t){return function(r,n){for(var i=r.length,a=[],o=0,u=e[0],c=0;i>0&&u>0&&(c+u+1>n&&(u=Math.max(1,n-c)),a.push(r.substring(i-=u,i+u)),!((c+=u+1)>n));)u=e[o=(o+1)%e.length];return a.reverse().join(t)}}function Qx(e){return function(t){return t.replace(/[0-9]/g,function(r){return e[+r]})}}var eP=/^(?:(.)?([<>=^]))?([+\-( ])?([$#])?(0)?(\d+)?(,)?(\.\d+)?(~)?([a-z%])?$/i;function cn(e){if(!(t=eP.exec(e)))throw new Error("invalid format: "+e);var t;return new $u({fill:t[1],align:t[2],sign:t[3],symbol:t[4],zero:t[5],width:t[6],comma:t[7],precision:t[8]&&t[8].slice(1),trim:t[9],type:t[10]})}cn.prototype=$u.prototype;function $u(e){this.fill=e.fill===void 0?" ":e.fill+"",this.align=e.align===void 0?">":e.align+"",this.sign=e.sign===void 0?"-":e.sign+"",this.symbol=e.symbol===void 0?"":e.symbol+"",this.zero=!!e.zero,this.width=e.width===void 0?void 0:+e.width,this.comma=!!e.comma,this.precision=e.precision===void 0?void 0:+e.precision,this.trim=!!e.trim,this.type=e.type===void 0?"":e.type+""}$u.prototype.toString=function(){return this.fill+this.align+this.sign+this.symbol+(this.zero?"0":"")+(this.width===void 0?"":Math.max(1,this.width|0))+(this.comma?",":"")+(this.precision===void 0?"":"."+Math.max(0,this.precision|0))+(this.trim?"~":"")+this.type};function tP(e){e:for(var t=e.length,r=1,n=-1,i;r<t;++r)switch(e[r]){case".":n=i=r;break;case"0":n===0&&(n=r),i=r;break;default:if(!+e[r])break e;n>0&&(n=0);break}return n>0?e.slice(0,n)+e.slice(i+1):e}var Gv;function rP(e,t){var r=fi(e,t);if(!r)return e+"";var n=r[0],i=r[1],a=i-(Gv=Math.max(-8,Math.min(8,Math.floor(i/3)))*3)+1,o=n.length;return a===o?n:a>o?n+new Array(a-o+1).join("0"):a>0?n.slice(0,a)+"."+n.slice(a):"0."+new Array(1-a).join("0")+fi(e,Math.max(0,t+a-1))[0]}function vs(e,t){var r=fi(e,t);if(!r)return e+"";var n=r[0],i=r[1];return i<0?"0."+new Array(-i).join("0")+n:n.length>i+1?n.slice(0,i+1)+"."+n.slice(i+1):n+new Array(i-n.length+2).join("0")}const hs={"%":(e,t)=>(e*100).toFixed(t),b:e=>Math.round(e).toString(2),c:e=>e+"",d:Zx,e:(e,t)=>e.toExponential(t),f:(e,t)=>e.toFixed(t),g:(e,t)=>e.toPrecision(t),o:e=>Math.round(e).toString(8),p:(e,t)=>vs(e*100,t),r:vs,s:rP,X:e=>Math.round(e).toString(16).toUpperCase(),x:e=>Math.round(e).toString(16)};function ps(e){return e}var ms=Array.prototype.map,ys=["y","z","a","f","p","n","µ","m","","k","M","G","T","P","E","Z","Y"];function nP(e){var t=e.grouping===void 0||e.thousands===void 0?ps:Jx(ms.call(e.grouping,Number),e.thousands+""),r=e.currency===void 0?"":e.currency[0]+"",n=e.currency===void 0?"":e.currency[1]+"",i=e.decimal===void 0?".":e.decimal+"",a=e.numerals===void 0?ps:Qx(ms.call(e.numerals,String)),o=e.percent===void 0?"%":e.percent+"",u=e.minus===void 0?"−":e.minus+"",c=e.nan===void 0?"NaN":e.nan+"";function s(f){f=cn(f);var d=f.fill,v=f.align,h=f.sign,y=f.symbol,m=f.zero,g=f.width,b=f.comma,w=f.precision,P=f.trim,x=f.type;x==="n"?(b=!0,x="g"):hs[x]||(w===void 0&&(w=12),P=!0,x="g"),(m||d==="0"&&v==="=")&&(m=!0,d="0",v="=");var O=y==="$"?r:y==="#"&&/[boxX]/.test(x)?"0"+x.toLowerCase():"",A=y==="$"?n:/[%p]/.test(x)?o:"",E=hs[x],_=/[defgprs%]/.test(x);w=w===void 0?6:/[gprs]/.test(x)?Math.max(1,Math.min(21,w)):Math.max(0,Math.min(20,w));function D(j){var C=O,N=A,R,F,G;if(x==="c")N=E(j)+N,j="";else{j=+j;var B=j<0||1/j<0;if(j=isNaN(j)?c:E(Math.abs(j),w),P&&(j=tP(j)),B&&+j==0&&h!=="+"&&(B=!1),C=(B?h==="("?h:u:h==="-"||h==="("?"":h)+C,N=(x==="s"?ys[8+Gv/3]:"")+N+(B&&h==="("?")":""),_){for(R=-1,F=j.length;++R<F;)if(G=j.charCodeAt(R),48>G||G>57){N=(G===46?i+j.slice(R+1):j.slice(R))+N,j=j.slice(0,R);break}}}b&&!m&&(j=t(j,1/0));var fe=C.length+j.length+N.length,ae=fe<g?new Array(g-fe+1).join(d):"";switch(b&&m&&(j=t(ae+j,ae.length?g-N.length:1/0),ae=""),v){case"<":j=C+j+N+ae;break;case"=":j=C+ae+j+N;break;case"^":j=ae.slice(0,fe=ae.length>>1)+C+j+N+ae.slice(fe);break;default:j=ae+C+j+N;break}return a(j)}return D.toString=function(){return f+""},D}function l(f,d){var v=s((f=cn(f),f.type="f",f)),h=Math.max(-8,Math.min(8,Math.floor(jr(d)/3)))*3,y=Math.pow(10,-h),m=ys[8+h/3];return function(g){return v(y*g)+m}}return{format:s,formatPrefix:l}}var Bn,Nu,Vv;iP({thousands:",",grouping:[3],currency:["$",""]});function iP(e){return Bn=nP(e),Nu=Bn.format,Vv=Bn.formatPrefix,Bn}function aP(e){return Math.max(0,-jr(Math.abs(e)))}function oP(e,t){return Math.max(0,Math.max(-8,Math.min(8,Math.floor(jr(t)/3)))*3-jr(Math.abs(e)))}function uP(e,t){return e=Math.abs(e),t=Math.abs(t)-e,Math.max(0,jr(t)-jr(e))+1}function Xv(e,t,r,n){var i=xo(e,t,r),a;switch(n=cn(n??",f"),n.type){case"s":{var o=Math.max(Math.abs(e),Math.abs(t));return n.precision==null&&!isNaN(a=oP(i,o))&&(n.precision=a),Vv(n,o)}case"":case"e":case"g":case"p":case"r":{n.precision==null&&!isNaN(a=uP(i,Math.max(Math.abs(e),Math.abs(t))))&&(n.precision=a-(n.type==="e"));break}case"f":case"%":{n.precision==null&&!isNaN(a=aP(i))&&(n.precision=a-(n.type==="%")*2);break}}return Nu(n)}function Wt(e){var t=e.domain;return e.ticks=function(r){var n=t();return bo(n[0],n[n.length-1],r??10)},e.tickFormat=function(r,n){var i=t();return Xv(i[0],i[i.length-1],r??10,n)},e.nice=function(r){r==null&&(r=10);var n=t(),i=0,a=n.length-1,o=n[i],u=n[a],c,s,l=10;for(u<o&&(s=o,o=u,u=s,s=i,i=a,a=s);l-- >0;){if(s=wo(o,u,r),s===c)return n[i]=o,n[a]=u,t(n);if(s>0)o=Math.floor(o/s)*s,u=Math.ceil(u/s)*s;else if(s<0)o=Math.ceil(o*s)/s,u=Math.floor(u*s)/s;else break;c=s}return e},e}function Zv(){var e=Iu();return e.copy=function(){return wn(e,Zv())},ot.apply(e,arguments),Wt(e)}function Jv(e){var t;function r(n){return n==null||isNaN(n=+n)?t:n}return r.invert=r,r.domain=r.range=function(n){return arguments.length?(e=Array.from(n,si),r):e.slice()},r.unknown=function(n){return arguments.length?(t=n,r):t},r.copy=function(){return Jv(e).unknown(t)},e=arguments.length?Array.from(e,si):[0,1],Wt(r)}function Qv(e,t){e=e.slice();var r=0,n=e.length-1,i=e[r],a=e[n],o;return a<i&&(o=r,r=n,n=o,o=i,i=a,a=o),e[r]=t.floor(i),e[n]=t.ceil(a),e}function gs(e){return Math.log(e)}function bs(e){return Math.exp(e)}function lP(e){return-Math.log(-e)}function cP(e){return-Math.exp(-e)}function sP(e){return isFinite(e)?+("1e"+e):e<0?0:e}function fP(e){return e===10?sP:e===Math.E?Math.exp:t=>Math.pow(e,t)}function dP(e){return e===Math.E?Math.log:e===10&&Math.log10||e===2&&Math.log2||(e=Math.log(e),t=>Math.log(t)/e)}function ws(e){return(t,r)=>-e(-t,r)}function Lu(e){const t=e(gs,bs),r=t.domain;let n=10,i,a;function o(){return i=dP(n),a=fP(n),r()[0]<0?(i=ws(i),a=ws(a),e(lP,cP)):e(gs,bs),t}return t.base=function(u){return arguments.length?(n=+u,o()):n},t.domain=function(u){return arguments.length?(r(u),o()):r()},t.ticks=u=>{const c=r();let s=c[0],l=c[c.length-1];const f=l<s;f&&([s,l]=[l,s]);let d=i(s),v=i(l),h,y;const m=u==null?10:+u;let g=[];if(!(n%1)&&v-d<m){if(d=Math.floor(d),v=Math.ceil(v),s>0){for(;d<=v;++d)for(h=1;h<n;++h)if(y=d<0?h/a(-d):h*a(d),!(y<s)){if(y>l)break;g.push(y)}}else for(;d<=v;++d)for(h=n-1;h>=1;--h)if(y=d>0?h/a(-d):h*a(d),!(y<s)){if(y>l)break;g.push(y)}g.length*2<m&&(g=bo(s,l,m))}else g=bo(d,v,Math.min(v-d,m)).map(a);return f?g.reverse():g},t.tickFormat=(u,c)=>{if(u==null&&(u=10),c==null&&(c=n===10?"s":","),typeof c!="function"&&(!(n%1)&&(c=cn(c)).precision==null&&(c.trim=!0),c=Nu(c)),u===1/0)return c;const s=Math.max(1,n*u/t.ticks().length);return l=>{let f=l/a(Math.round(i(l)));return f*n<n-.5&&(f*=n),f<=s?c(l):""}},t.nice=()=>r(Qv(r(),{floor:u=>a(Math.floor(i(u))),ceil:u=>a(Math.ceil(i(u)))})),t}function eh(){const e=Lu(ta()).domain([1,10]);return e.copy=()=>wn(e,eh()).base(e.base()),ot.apply(e,arguments),e}function xs(e){return function(t){return Math.sign(t)*Math.log1p(Math.abs(t/e))}}function Ps(e){return function(t){return Math.sign(t)*Math.expm1(Math.abs(t))*e}}function Ru(e){var t=1,r=e(xs(t),Ps(t));return r.constant=function(n){return arguments.length?e(xs(t=+n),Ps(t)):t},Wt(r)}function th(){var e=Ru(ta());return e.copy=function(){return wn(e,th()).constant(e.constant())},ot.apply(e,arguments)}function Os(e){return function(t){return t<0?-Math.pow(-t,e):Math.pow(t,e)}}function vP(e){return e<0?-Math.sqrt(-e):Math.sqrt(e)}function hP(e){return e<0?-e*e:e*e}function Bu(e){var t=e(_e,_e),r=1;function n(){return r===1?e(_e,_e):r===.5?e(vP,hP):e(Os(r),Os(1/r))}return t.exponent=function(i){return arguments.length?(r=+i,n()):r},Wt(t)}function Ku(){var e=Bu(ta());return e.copy=function(){return wn(e,Ku()).exponent(e.exponent())},ot.apply(e,arguments),e}function pP(){return Ku.apply(null,arguments).exponent(.5)}function As(e){return Math.sign(e)*e*e}function mP(e){return Math.sign(e)*Math.sqrt(Math.abs(e))}function rh(){var e=Iu(),t=[0,1],r=!1,n;function i(a){var o=mP(e(a));return isNaN(o)?n:r?Math.round(o):o}return i.invert=function(a){return e.invert(As(a))},i.domain=function(a){return arguments.length?(e.domain(a),i):e.domain()},i.range=function(a){return arguments.length?(e.range((t=Array.from(a,si)).map(As)),i):t.slice()},i.rangeRound=function(a){return i.range(a).round(!0)},i.round=function(a){return arguments.length?(r=!!a,i):r},i.clamp=function(a){return arguments.length?(e.clamp(a),i):e.clamp()},i.unknown=function(a){return arguments.length?(n=a,i):n},i.copy=function(){return rh(e.domain(),t).round(r).clamp(e.clamp()).unknown(n)},ot.apply(i,arguments),Wt(i)}function nh(){var e=[],t=[],r=[],n;function i(){var o=0,u=Math.max(1,t.length);for(r=new Array(u-1);++o<u;)r[o-1]=wx(e,o/u);return a}function a(o){return o==null||isNaN(o=+o)?n:t[gn(r,o)]}return a.invertExtent=function(o){var u=t.indexOf(o);return u<0?[NaN,NaN]:[u>0?r[u-1]:e[0],u<r.length?r[u]:e[e.length-1]]},a.domain=function(o){if(!arguments.length)return e.slice();e=[];for(let u of o)u!=null&&!isNaN(u=+u)&&e.push(u);return e.sort(Nt),i()},a.range=function(o){return arguments.length?(t=Array.from(o),i()):t.slice()},a.unknown=function(o){return arguments.length?(n=o,a):n},a.quantiles=function(){return r.slice()},a.copy=function(){return nh().domain(e).range(t).unknown(n)},ot.apply(a,arguments)}function ih(){var e=0,t=1,r=1,n=[.5],i=[0,1],a;function o(c){return c!=null&&c<=c?i[gn(n,c,0,r)]:a}function u(){var c=-1;for(n=new Array(r);++c<r;)n[c]=((c+1)*t-(c-r)*e)/(r+1);return o}return o.domain=function(c){return arguments.length?([e,t]=c,e=+e,t=+t,u()):[e,t]},o.range=function(c){return arguments.length?(r=(i=Array.from(c)).length-1,u()):i.slice()},o.invertExtent=function(c){var s=i.indexOf(c);return s<0?[NaN,NaN]:s<1?[e,n[0]]:s>=r?[n[r-1],t]:[n[s-1],n[s]]},o.unknown=function(c){return arguments.length&&(a=c),o},o.thresholds=function(){return n.slice()},o.copy=function(){return ih().domain([e,t]).range(i).unknown(a)},ot.apply(Wt(o),arguments)}function ah(){var e=[.5],t=[0,1],r,n=1;function i(a){return a!=null&&a<=a?t[gn(e,a,0,n)]:r}return i.domain=function(a){return arguments.length?(e=Array.from(a),n=Math.min(e.length,t.length-1),i):e.slice()},i.range=function(a){return arguments.length?(t=Array.from(a),n=Math.min(e.length,t.length-1),i):t.slice()},i.invertExtent=function(a){var o=t.indexOf(a);return[e[o-1],e[o]]},i.unknown=function(a){return arguments.length?(r=a,i):r},i.copy=function(){return ah().domain(e).range(t).unknown(r)},ot.apply(i,arguments)}const Na=new Date,La=new Date;function he(e,t,r,n){function i(a){return e(a=arguments.length===0?new Date:new Date(+a)),a}return i.floor=a=>(e(a=new Date(+a)),a),i.ceil=a=>(e(a=new Date(a-1)),t(a,1),e(a),a),i.round=a=>{const o=i(a),u=i.ceil(a);return a-o<u-a?o:u},i.offset=(a,o)=>(t(a=new Date(+a),o==null?1:Math.floor(o)),a),i.range=(a,o,u)=>{const c=[];if(a=i.ceil(a),u=u==null?1:Math.floor(u),!(a<o)||!(u>0))return c;let s;do c.push(s=new Date(+a)),t(a,u),e(a);while(s<a&&a<o);return c},i.filter=a=>he(o=>{if(o>=o)for(;e(o),!a(o);)o.setTime(o-1)},(o,u)=>{if(o>=o)if(u<0)for(;++u<=0;)for(;t(o,-1),!a(o););else for(;--u>=0;)for(;t(o,1),!a(o););}),r&&(i.count=(a,o)=>(Na.setTime(+a),La.setTime(+o),e(Na),e(La),Math.floor(r(Na,La))),i.every=a=>(a=Math.floor(a),!isFinite(a)||!(a>0)?null:a>1?i.filter(n?o=>n(o)%a===0:o=>i.count(0,o)%a===0):i)),i}const di=he(()=>{},(e,t)=>{e.setTime(+e+t)},(e,t)=>t-e);di.every=e=>(e=Math.floor(e),!isFinite(e)||!(e>0)?null:e>1?he(t=>{t.setTime(Math.floor(t/e)*e)},(t,r)=>{t.setTime(+t+r*e)},(t,r)=>(r-t)/e):di);di.range;const gt=1e3,et=gt*60,bt=et*60,Ot=bt*24,zu=Ot*7,Ss=Ot*30,Ra=Ot*365,tr=he(e=>{e.setTime(e-e.getMilliseconds())},(e,t)=>{e.setTime(+e+t*gt)},(e,t)=>(t-e)/gt,e=>e.getUTCSeconds());tr.range;const Fu=he(e=>{e.setTime(e-e.getMilliseconds()-e.getSeconds()*gt)},(e,t)=>{e.setTime(+e+t*et)},(e,t)=>(t-e)/et,e=>e.getMinutes());Fu.range;const Wu=he(e=>{e.setUTCSeconds(0,0)},(e,t)=>{e.setTime(+e+t*et)},(e,t)=>(t-e)/et,e=>e.getUTCMinutes());Wu.range;const Uu=he(e=>{e.setTime(e-e.getMilliseconds()-e.getSeconds()*gt-e.getMinutes()*et)},(e,t)=>{e.setTime(+e+t*bt)},(e,t)=>(t-e)/bt,e=>e.getHours());Uu.range;const qu=he(e=>{e.setUTCMinutes(0,0,0)},(e,t)=>{e.setTime(+e+t*bt)},(e,t)=>(t-e)/bt,e=>e.getUTCHours());qu.range;const xn=he(e=>e.setHours(0,0,0,0),(e,t)=>e.setDate(e.getDate()+t),(e,t)=>(t-e-(t.getTimezoneOffset()-e.getTimezoneOffset())*et)/Ot,e=>e.getDate()-1);xn.range;const ra=he(e=>{e.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCDate(e.getUTCDate()+t)},(e,t)=>(t-e)/Ot,e=>e.getUTCDate()-1);ra.range;const oh=he(e=>{e.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCDate(e.getUTCDate()+t)},(e,t)=>(t-e)/Ot,e=>Math.floor(e/Ot));oh.range;function vr(e){return he(t=>{t.setDate(t.getDate()-(t.getDay()+7-e)%7),t.setHours(0,0,0,0)},(t,r)=>{t.setDate(t.getDate()+r*7)},(t,r)=>(r-t-(r.getTimezoneOffset()-t.getTimezoneOffset())*et)/zu)}const na=vr(0),vi=vr(1),yP=vr(2),gP=vr(3),Cr=vr(4),bP=vr(5),wP=vr(6);na.range;vi.range;yP.range;gP.range;Cr.range;bP.range;wP.range;function hr(e){return he(t=>{t.setUTCDate(t.getUTCDate()-(t.getUTCDay()+7-e)%7),t.setUTCHours(0,0,0,0)},(t,r)=>{t.setUTCDate(t.getUTCDate()+r*7)},(t,r)=>(r-t)/zu)}const ia=hr(0),hi=hr(1),xP=hr(2),PP=hr(3),kr=hr(4),OP=hr(5),AP=hr(6);ia.range;hi.range;xP.range;PP.range;kr.range;OP.range;AP.range;const Yu=he(e=>{e.setDate(1),e.setHours(0,0,0,0)},(e,t)=>{e.setMonth(e.getMonth()+t)},(e,t)=>t.getMonth()-e.getMonth()+(t.getFullYear()-e.getFullYear())*12,e=>e.getMonth());Yu.range;const Hu=he(e=>{e.setUTCDate(1),e.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCMonth(e.getUTCMonth()+t)},(e,t)=>t.getUTCMonth()-e.getUTCMonth()+(t.getUTCFullYear()-e.getUTCFullYear())*12,e=>e.getUTCMonth());Hu.range;const At=he(e=>{e.setMonth(0,1),e.setHours(0,0,0,0)},(e,t)=>{e.setFullYear(e.getFullYear()+t)},(e,t)=>t.getFullYear()-e.getFullYear(),e=>e.getFullYear());At.every=e=>!isFinite(e=Math.floor(e))||!(e>0)?null:he(t=>{t.setFullYear(Math.floor(t.getFullYear()/e)*e),t.setMonth(0,1),t.setHours(0,0,0,0)},(t,r)=>{t.setFullYear(t.getFullYear()+r*e)});At.range;const St=he(e=>{e.setUTCMonth(0,1),e.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCFullYear(e.getUTCFullYear()+t)},(e,t)=>t.getUTCFullYear()-e.getUTCFullYear(),e=>e.getUTCFullYear());St.every=e=>!isFinite(e=Math.floor(e))||!(e>0)?null:he(t=>{t.setUTCFullYear(Math.floor(t.getUTCFullYear()/e)*e),t.setUTCMonth(0,1),t.setUTCHours(0,0,0,0)},(t,r)=>{t.setUTCFullYear(t.getUTCFullYear()+r*e)});St.range;function uh(e,t,r,n,i,a){const o=[[tr,1,gt],[tr,5,5*gt],[tr,15,15*gt],[tr,30,30*gt],[a,1,et],[a,5,5*et],[a,15,15*et],[a,30,30*et],[i,1,bt],[i,3,3*bt],[i,6,6*bt],[i,12,12*bt],[n,1,Ot],[n,2,2*Ot],[r,1,zu],[t,1,Ss],[t,3,3*Ss],[e,1,Ra]];function u(s,l,f){const d=l<s;d&&([s,l]=[l,s]);const v=f&&typeof f.range=="function"?f:c(s,l,f),h=v?v.range(s,+l+1):[];return d?h.reverse():h}function c(s,l,f){const d=Math.abs(l-s)/f,v=Tu(([,,m])=>m).right(o,d);if(v===o.length)return e.every(xo(s/Ra,l/Ra,f));if(v===0)return di.every(Math.max(xo(s,l,f),1));const[h,y]=o[d/o[v-1][2]<o[v][2]/d?v-1:v];return h.every(y)}return[u,c]}const[SP,EP]=uh(St,Hu,ia,oh,qu,Wu),[_P,TP]=uh(At,Yu,na,xn,Uu,Fu);function Ba(e){if(0<=e.y&&e.y<100){var t=new Date(-1,e.m,e.d,e.H,e.M,e.S,e.L);return t.setFullYear(e.y),t}return new Date(e.y,e.m,e.d,e.H,e.M,e.S,e.L)}function Ka(e){if(0<=e.y&&e.y<100){var t=new Date(Date.UTC(-1,e.m,e.d,e.H,e.M,e.S,e.L));return t.setUTCFullYear(e.y),t}return new Date(Date.UTC(e.y,e.m,e.d,e.H,e.M,e.S,e.L))}function Hr(e,t,r){return{y:e,m:t,d:r,H:0,M:0,S:0,L:0}}function jP(e){var t=e.dateTime,r=e.date,n=e.time,i=e.periods,a=e.days,o=e.shortDays,u=e.months,c=e.shortMonths,s=Gr(i),l=Vr(i),f=Gr(a),d=Vr(a),v=Gr(o),h=Vr(o),y=Gr(u),m=Vr(u),g=Gr(c),b=Vr(c),w={a:B,A:fe,b:ae,B:De,c:null,d:ks,e:ks,f:JP,g:l1,G:s1,H:VP,I:XP,j:ZP,L:lh,m:QP,M:e1,p:He,q:L,Q:Is,s:$s,S:t1,u:r1,U:n1,V:i1,w:a1,W:o1,x:null,X:null,y:u1,Y:c1,Z:f1,"%":Ds},P={a:we,A:Ht,b:Ge,B:ay,c:null,d:Ms,e:Ms,f:p1,g:S1,G:_1,H:d1,I:v1,j:h1,L:sh,m:m1,M:y1,p:oy,q:uy,Q:Is,s:$s,S:g1,u:b1,U:w1,V:x1,w:P1,W:O1,x:null,X:null,y:A1,Y:E1,Z:T1,"%":Ds},x={a:D,A:j,b:C,B:N,c:R,d:js,e:js,f:qP,g:Ts,G:_s,H:Cs,I:Cs,j:zP,L:UP,m:KP,M:FP,p:_,q:BP,Q:HP,s:GP,S:WP,u:IP,U:$P,V:NP,w:DP,W:LP,x:F,X:G,y:Ts,Y:_s,Z:RP,"%":YP};w.x=O(r,w),w.X=O(n,w),w.c=O(t,w),P.x=O(r,P),P.X=O(n,P),P.c=O(t,P);function O(I,K){return function(W){var T=[],Ce=-1,J=0,Ie=I.length,$e,Gt,Ql;for(W instanceof Date||(W=new Date(+W));++Ce<Ie;)I.charCodeAt(Ce)===37&&(T.push(I.slice(J,Ce)),(Gt=Es[$e=I.charAt(++Ce)])!=null?$e=I.charAt(++Ce):Gt=$e==="e"?" ":"0",(Ql=K[$e])&&($e=Ql(W,Gt)),T.push($e),J=Ce+1);return T.push(I.slice(J,Ce)),T.join("")}}function A(I,K){return function(W){var T=Hr(1900,void 0,1),Ce=E(T,I,W+="",0),J,Ie;if(Ce!=W.length)return null;if("Q"in T)return new Date(T.Q);if("s"in T)return new Date(T.s*1e3+("L"in T?T.L:0));if(K&&!("Z"in T)&&(T.Z=0),"p"in T&&(T.H=T.H%12+T.p*12),T.m===void 0&&(T.m="q"in T?T.q:0),"V"in T){if(T.V<1||T.V>53)return null;"w"in T||(T.w=1),"Z"in T?(J=Ka(Hr(T.y,0,1)),Ie=J.getUTCDay(),J=Ie>4||Ie===0?hi.ceil(J):hi(J),J=ra.offset(J,(T.V-1)*7),T.y=J.getUTCFullYear(),T.m=J.getUTCMonth(),T.d=J.getUTCDate()+(T.w+6)%7):(J=Ba(Hr(T.y,0,1)),Ie=J.getDay(),J=Ie>4||Ie===0?vi.ceil(J):vi(J),J=xn.offset(J,(T.V-1)*7),T.y=J.getFullYear(),T.m=J.getMonth(),T.d=J.getDate()+(T.w+6)%7)}else("W"in T||"U"in T)&&("w"in T||(T.w="u"in T?T.u%7:"W"in T?1:0),Ie="Z"in T?Ka(Hr(T.y,0,1)).getUTCDay():Ba(Hr(T.y,0,1)).getDay(),T.m=0,T.d="W"in T?(T.w+6)%7+T.W*7-(Ie+5)%7:T.w+T.U*7-(Ie+6)%7);return"Z"in T?(T.H+=T.Z/100|0,T.M+=T.Z%100,Ka(T)):Ba(T)}}function E(I,K,W,T){for(var Ce=0,J=K.length,Ie=W.length,$e,Gt;Ce<J;){if(T>=Ie)return-1;if($e=K.charCodeAt(Ce++),$e===37){if($e=K.charAt(Ce++),Gt=x[$e in Es?K.charAt(Ce++):$e],!Gt||(T=Gt(I,W,T))<0)return-1}else if($e!=W.charCodeAt(T++))return-1}return T}function _(I,K,W){var T=s.exec(K.slice(W));return T?(I.p=l.get(T[0].toLowerCase()),W+T[0].length):-1}function D(I,K,W){var T=v.exec(K.slice(W));return T?(I.w=h.get(T[0].toLowerCase()),W+T[0].length):-1}function j(I,K,W){var T=f.exec(K.slice(W));return T?(I.w=d.get(T[0].toLowerCase()),W+T[0].length):-1}function C(I,K,W){var T=g.exec(K.slice(W));return T?(I.m=b.get(T[0].toLowerCase()),W+T[0].length):-1}function N(I,K,W){var T=y.exec(K.slice(W));return T?(I.m=m.get(T[0].toLowerCase()),W+T[0].length):-1}function R(I,K,W){return E(I,t,K,W)}function F(I,K,W){return E(I,r,K,W)}function G(I,K,W){return E(I,n,K,W)}function B(I){return o[I.getDay()]}function fe(I){return a[I.getDay()]}function ae(I){return c[I.getMonth()]}function De(I){return u[I.getMonth()]}function He(I){return i[+(I.getHours()>=12)]}function L(I){return 1+~~(I.getMonth()/3)}function we(I){return o[I.getUTCDay()]}function Ht(I){return a[I.getUTCDay()]}function Ge(I){return c[I.getUTCMonth()]}function ay(I){return u[I.getUTCMonth()]}function oy(I){return i[+(I.getUTCHours()>=12)]}function uy(I){return 1+~~(I.getUTCMonth()/3)}return{format:function(I){var K=O(I+="",w);return K.toString=function(){return I},K},parse:function(I){var K=A(I+="",!1);return K.toString=function(){return I},K},utcFormat:function(I){var K=O(I+="",P);return K.toString=function(){return I},K},utcParse:function(I){var K=A(I+="",!0);return K.toString=function(){return I},K}}}var Es={"-":"",_:" ",0:"0"},ge=/^\s*\d+/,CP=/^%/,kP=/[\\^$*+?|[\]().{}]/g;function q(e,t,r){var n=e<0?"-":"",i=(n?-e:e)+"",a=i.length;return n+(a<r?new Array(r-a+1).join(t)+i:i)}function MP(e){return e.replace(kP,"\\$&")}function Gr(e){return new RegExp("^(?:"+e.map(MP).join("|")+")","i")}function Vr(e){return new Map(e.map((t,r)=>[t.toLowerCase(),r]))}function DP(e,t,r){var n=ge.exec(t.slice(r,r+1));return n?(e.w=+n[0],r+n[0].length):-1}function IP(e,t,r){var n=ge.exec(t.slice(r,r+1));return n?(e.u=+n[0],r+n[0].length):-1}function $P(e,t,r){var n=ge.exec(t.slice(r,r+2));return n?(e.U=+n[0],r+n[0].length):-1}function NP(e,t,r){var n=ge.exec(t.slice(r,r+2));return n?(e.V=+n[0],r+n[0].length):-1}function LP(e,t,r){var n=ge.exec(t.slice(r,r+2));return n?(e.W=+n[0],r+n[0].length):-1}function _s(e,t,r){var n=ge.exec(t.slice(r,r+4));return n?(e.y=+n[0],r+n[0].length):-1}function Ts(e,t,r){var n=ge.exec(t.slice(r,r+2));return n?(e.y=+n[0]+(+n[0]>68?1900:2e3),r+n[0].length):-1}function RP(e,t,r){var n=/^(Z)|([+-]\d\d)(?::?(\d\d))?/.exec(t.slice(r,r+6));return n?(e.Z=n[1]?0:-(n[2]+(n[3]||"00")),r+n[0].length):-1}function BP(e,t,r){var n=ge.exec(t.slice(r,r+1));return n?(e.q=n[0]*3-3,r+n[0].length):-1}function KP(e,t,r){var n=ge.exec(t.slice(r,r+2));return n?(e.m=n[0]-1,r+n[0].length):-1}function js(e,t,r){var n=ge.exec(t.slice(r,r+2));return n?(e.d=+n[0],r+n[0].length):-1}function zP(e,t,r){var n=ge.exec(t.slice(r,r+3));return n?(e.m=0,e.d=+n[0],r+n[0].length):-1}function Cs(e,t,r){var n=ge.exec(t.slice(r,r+2));return n?(e.H=+n[0],r+n[0].length):-1}function FP(e,t,r){var n=ge.exec(t.slice(r,r+2));return n?(e.M=+n[0],r+n[0].length):-1}function WP(e,t,r){var n=ge.exec(t.slice(r,r+2));return n?(e.S=+n[0],r+n[0].length):-1}function UP(e,t,r){var n=ge.exec(t.slice(r,r+3));return n?(e.L=+n[0],r+n[0].length):-1}function qP(e,t,r){var n=ge.exec(t.slice(r,r+6));return n?(e.L=Math.floor(n[0]/1e3),r+n[0].length):-1}function YP(e,t,r){var n=CP.exec(t.slice(r,r+1));return n?r+n[0].length:-1}function HP(e,t,r){var n=ge.exec(t.slice(r));return n?(e.Q=+n[0],r+n[0].length):-1}function GP(e,t,r){var n=ge.exec(t.slice(r));return n?(e.s=+n[0],r+n[0].length):-1}function ks(e,t){return q(e.getDate(),t,2)}function VP(e,t){return q(e.getHours(),t,2)}function XP(e,t){return q(e.getHours()%12||12,t,2)}function ZP(e,t){return q(1+xn.count(At(e),e),t,3)}function lh(e,t){return q(e.getMilliseconds(),t,3)}function JP(e,t){return lh(e,t)+"000"}function QP(e,t){return q(e.getMonth()+1,t,2)}function e1(e,t){return q(e.getMinutes(),t,2)}function t1(e,t){return q(e.getSeconds(),t,2)}function r1(e){var t=e.getDay();return t===0?7:t}function n1(e,t){return q(na.count(At(e)-1,e),t,2)}function ch(e){var t=e.getDay();return t>=4||t===0?Cr(e):Cr.ceil(e)}function i1(e,t){return e=ch(e),q(Cr.count(At(e),e)+(At(e).getDay()===4),t,2)}function a1(e){return e.getDay()}function o1(e,t){return q(vi.count(At(e)-1,e),t,2)}function u1(e,t){return q(e.getFullYear()%100,t,2)}function l1(e,t){return e=ch(e),q(e.getFullYear()%100,t,2)}function c1(e,t){return q(e.getFullYear()%1e4,t,4)}function s1(e,t){var r=e.getDay();return e=r>=4||r===0?Cr(e):Cr.ceil(e),q(e.getFullYear()%1e4,t,4)}function f1(e){var t=e.getTimezoneOffset();return(t>0?"-":(t*=-1,"+"))+q(t/60|0,"0",2)+q(t%60,"0",2)}function Ms(e,t){return q(e.getUTCDate(),t,2)}function d1(e,t){return q(e.getUTCHours(),t,2)}function v1(e,t){return q(e.getUTCHours()%12||12,t,2)}function h1(e,t){return q(1+ra.count(St(e),e),t,3)}function sh(e,t){return q(e.getUTCMilliseconds(),t,3)}function p1(e,t){return sh(e,t)+"000"}function m1(e,t){return q(e.getUTCMonth()+1,t,2)}function y1(e,t){return q(e.getUTCMinutes(),t,2)}function g1(e,t){return q(e.getUTCSeconds(),t,2)}function b1(e){var t=e.getUTCDay();return t===0?7:t}function w1(e,t){return q(ia.count(St(e)-1,e),t,2)}function fh(e){var t=e.getUTCDay();return t>=4||t===0?kr(e):kr.ceil(e)}function x1(e,t){return e=fh(e),q(kr.count(St(e),e)+(St(e).getUTCDay()===4),t,2)}function P1(e){return e.getUTCDay()}function O1(e,t){return q(hi.count(St(e)-1,e),t,2)}function A1(e,t){return q(e.getUTCFullYear()%100,t,2)}function S1(e,t){return e=fh(e),q(e.getUTCFullYear()%100,t,2)}function E1(e,t){return q(e.getUTCFullYear()%1e4,t,4)}function _1(e,t){var r=e.getUTCDay();return e=r>=4||r===0?kr(e):kr.ceil(e),q(e.getUTCFullYear()%1e4,t,4)}function T1(){return"+0000"}function Ds(){return"%"}function Is(e){return+e}function $s(e){return Math.floor(+e/1e3)}var gr,dh,vh;j1({dateTime:"%x, %X",date:"%-m/%-d/%Y",time:"%-I:%M:%S %p",periods:["AM","PM"],days:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],shortDays:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],months:["January","February","March","April","May","June","July","August","September","October","November","December"],shortMonths:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"]});function j1(e){return gr=jP(e),dh=gr.format,gr.parse,vh=gr.utcFormat,gr.utcParse,gr}function C1(e){return new Date(e)}function k1(e){return e instanceof Date?+e:+new Date(+e)}function Gu(e,t,r,n,i,a,o,u,c,s){var l=Iu(),f=l.invert,d=l.domain,v=s(".%L"),h=s(":%S"),y=s("%I:%M"),m=s("%I %p"),g=s("%a %d"),b=s("%b %d"),w=s("%B"),P=s("%Y");function x(O){return(c(O)<O?v:u(O)<O?h:o(O)<O?y:a(O)<O?m:n(O)<O?i(O)<O?g:b:r(O)<O?w:P)(O)}return l.invert=function(O){return new Date(f(O))},l.domain=function(O){return arguments.length?d(Array.from(O,k1)):d().map(C1)},l.ticks=function(O){var A=d();return e(A[0],A[A.length-1],O??10)},l.tickFormat=function(O,A){return A==null?x:s(A)},l.nice=function(O){var A=d();return(!O||typeof O.range!="function")&&(O=t(A[0],A[A.length-1],O??10)),O?d(Qv(A,O)):l},l.copy=function(){return wn(l,Gu(e,t,r,n,i,a,o,u,c,s))},l}function M1(){return ot.apply(Gu(_P,TP,At,Yu,na,xn,Uu,Fu,tr,dh).domain([new Date(2e3,0,1),new Date(2e3,0,2)]),arguments)}function D1(){return ot.apply(Gu(SP,EP,St,Hu,ia,ra,qu,Wu,tr,vh).domain([Date.UTC(2e3,0,1),Date.UTC(2e3,0,2)]),arguments)}function aa(){var e=0,t=1,r,n,i,a,o=_e,u=!1,c;function s(f){return f==null||isNaN(f=+f)?c:o(i===0?.5:(f=(a(f)-r)*i,u?Math.max(0,Math.min(1,f)):f))}s.domain=function(f){return arguments.length?([e,t]=f,r=a(e=+e),n=a(t=+t),i=r===n?0:1/(n-r),s):[e,t]},s.clamp=function(f){return arguments.length?(u=!!f,s):u},s.interpolator=function(f){return arguments.length?(o=f,s):o};function l(f){return function(d){var v,h;return arguments.length?([v,h]=d,o=f(v,h),s):[o(0),o(1)]}}return s.range=l($r),s.rangeRound=l(Du),s.unknown=function(f){return arguments.length?(c=f,s):c},function(f){return a=f,r=f(e),n=f(t),i=r===n?0:1/(n-r),s}}function Ut(e,t){return t.domain(e.domain()).interpolator(e.interpolator()).clamp(e.clamp()).unknown(e.unknown())}function hh(){var e=Wt(aa()(_e));return e.copy=function(){return Ut(e,hh())},jt.apply(e,arguments)}function ph(){var e=Lu(aa()).domain([1,10]);return e.copy=function(){return Ut(e,ph()).base(e.base())},jt.apply(e,arguments)}function mh(){var e=Ru(aa());return e.copy=function(){return Ut(e,mh()).constant(e.constant())},jt.apply(e,arguments)}function Vu(){var e=Bu(aa());return e.copy=function(){return Ut(e,Vu()).exponent(e.exponent())},jt.apply(e,arguments)}function I1(){return Vu.apply(null,arguments).exponent(.5)}function yh(){var e=[],t=_e;function r(n){if(n!=null&&!isNaN(n=+n))return t((gn(e,n,1)-1)/(e.length-1))}return r.domain=function(n){if(!arguments.length)return e.slice();e=[];for(let i of n)i!=null&&!isNaN(i=+i)&&e.push(i);return e.sort(Nt),r},r.interpolator=function(n){return arguments.length?(t=n,r):t},r.range=function(){return e.map((n,i)=>t(i/(e.length-1)))},r.quantiles=function(n){return Array.from({length:n+1},(i,a)=>bx(e,a/n))},r.copy=function(){return yh(t).domain(e)},jt.apply(r,arguments)}function oa(){var e=0,t=.5,r=1,n=1,i,a,o,u,c,s=_e,l,f=!1,d;function v(y){return isNaN(y=+y)?d:(y=.5+((y=+l(y))-a)*(n*y<n*a?u:c),s(f?Math.max(0,Math.min(1,y)):y))}v.domain=function(y){return arguments.length?([e,t,r]=y,i=l(e=+e),a=l(t=+t),o=l(r=+r),u=i===a?0:.5/(a-i),c=a===o?0:.5/(o-a),n=a<i?-1:1,v):[e,t,r]},v.clamp=function(y){return arguments.length?(f=!!y,v):f},v.interpolator=function(y){return arguments.length?(s=y,v):s};function h(y){return function(m){var g,b,w;return arguments.length?([g,b,w]=m,s=Yx(y,[g,b,w]),v):[s(0),s(.5),s(1)]}}return v.range=h($r),v.rangeRound=h(Du),v.unknown=function(y){return arguments.length?(d=y,v):d},function(y){return l=y,i=y(e),a=y(t),o=y(r),u=i===a?0:.5/(a-i),c=a===o?0:.5/(o-a),n=a<i?-1:1,v}}function gh(){var e=Wt(oa()(_e));return e.copy=function(){return Ut(e,gh())},jt.apply(e,arguments)}function bh(){var e=Lu(oa()).domain([.1,1,10]);return e.copy=function(){return Ut(e,bh()).base(e.base())},jt.apply(e,arguments)}function wh(){var e=Ru(oa());return e.copy=function(){return Ut(e,wh()).constant(e.constant())},jt.apply(e,arguments)}function Xu(){var e=Bu(oa());return e.copy=function(){return Ut(e,Xu()).exponent(e.exponent())},jt.apply(e,arguments)}function $1(){return Xu.apply(null,arguments).exponent(.5)}const Jr=Object.freeze(Object.defineProperty({__proto__:null,scaleBand:Cu,scaleDiverging:gh,scaleDivergingLog:bh,scaleDivergingPow:Xu,scaleDivergingSqrt:$1,scaleDivergingSymlog:wh,scaleIdentity:Jv,scaleImplicit:Po,scaleLinear:Zv,scaleLog:eh,scaleOrdinal:ju,scalePoint:Px,scalePow:Ku,scaleQuantile:nh,scaleQuantize:ih,scaleRadial:rh,scaleSequential:hh,scaleSequentialLog:ph,scaleSequentialPow:Vu,scaleSequentialQuantile:yh,scaleSequentialSqrt:I1,scaleSequentialSymlog:mh,scaleSqrt:pP,scaleSymlog:th,scaleThreshold:ah,scaleTime:M1,scaleUtc:D1,tickFormat:Xv},Symbol.toStringTag,{value:"Module"}));var pr=e=>e.chartData,Zu=S([pr],e=>{var t=e.chartData!=null?e.chartData.length-1:0;return{chartData:e.chartData,computedData:e.computedData,dataEndIndex:t,dataStartIndex:0}}),Ju=(e,t,r,n)=>n?Zu(e):pr(e);function Mr(e){if(Array.isArray(e)&&e.length===2){var[t,r]=e;if(Ye(t)&&Ye(r))return!0}return!1}function Ns(e,t,r){return r?e:[Math.min(e[0],t[0]),Math.max(e[1],t[1])]}function N1(e,t){if(t&&typeof e!="function"&&Array.isArray(e)&&e.length===2){var[r,n]=e,i,a;if(Ye(r))i=r;else if(typeof r=="function")return;if(Ye(n))a=n;else if(typeof n=="function")return;var o=[i,a];if(Mr(o))return o}}function L1(e,t,r){if(!(!r&&t==null)){if(typeof e=="function"&&t!=null)try{var n=e(t,r);if(Mr(n))return Ns(n,t,r)}catch{}if(Array.isArray(e)&&e.length===2){var[i,a]=e,o,u;if(i==="auto")t!=null&&(o=Math.min(...t));else if(k(i))o=i;else if(typeof i=="function")try{t!=null&&(o=i(t==null?void 0:t[0]))}catch{}else if(typeof i=="string"&&Mc.test(i)){var c=Mc.exec(i);if(c==null||t==null)o=void 0;else{var s=+c[1];o=t[0]-s}}else o=t==null?void 0:t[0];if(a==="auto")t!=null&&(u=Math.max(...t));else if(k(a))u=a;else if(typeof a=="function")try{t!=null&&(u=a(t==null?void 0:t[1]))}catch{}else if(typeof a=="string"&&Dc.test(a)){var l=Dc.exec(a);if(l==null||t==null)u=void 0;else{var f=+l[1];u=t[1]+f}}else u=t==null?void 0:t[1];var d=[o,u];if(Mr(d))return t==null?d:Ns(d,t,r)}}}var Nr=1e9,R1={precision:20,rounding:4,toExpNeg:-7,toExpPos:21,LN10:"2.302585092994045684017991454684364207601101488628772976033327900967572609677352480235997205089598298341967784042286"},el,re=!0,rt="[DecimalError] ",or=rt+"Invalid argument: ",Qu=rt+"Exponent out of range: ",Lr=Math.floor,Zt=Math.pow,B1=/^(\d+(\.\d*)?|\.\d+)(e[+-]?\d+)?$/i,ze,pe=1e7,ee=7,xh=9007199254740991,pi=Lr(xh/ee),M={};M.absoluteValue=M.abs=function(){var e=new this.constructor(this);return e.s&&(e.s=1),e};M.comparedTo=M.cmp=function(e){var t,r,n,i,a=this;if(e=new a.constructor(e),a.s!==e.s)return a.s||-e.s;if(a.e!==e.e)return a.e>e.e^a.s<0?1:-1;for(n=a.d.length,i=e.d.length,t=0,r=n<i?n:i;t<r;++t)if(a.d[t]!==e.d[t])return a.d[t]>e.d[t]^a.s<0?1:-1;return n===i?0:n>i^a.s<0?1:-1};M.decimalPlaces=M.dp=function(){var e=this,t=e.d.length-1,r=(t-e.e)*ee;if(t=e.d[t],t)for(;t%10==0;t/=10)r--;return r<0?0:r};M.dividedBy=M.div=function(e){return xt(this,new this.constructor(e))};M.dividedToIntegerBy=M.idiv=function(e){var t=this,r=t.constructor;return V(xt(t,new r(e),0,1),r.precision)};M.equals=M.eq=function(e){return!this.cmp(e)};M.exponent=function(){return se(this)};M.greaterThan=M.gt=function(e){return this.cmp(e)>0};M.greaterThanOrEqualTo=M.gte=function(e){return this.cmp(e)>=0};M.isInteger=M.isint=function(){return this.e>this.d.length-2};M.isNegative=M.isneg=function(){return this.s<0};M.isPositive=M.ispos=function(){return this.s>0};M.isZero=function(){return this.s===0};M.lessThan=M.lt=function(e){return this.cmp(e)<0};M.lessThanOrEqualTo=M.lte=function(e){return this.cmp(e)<1};M.logarithm=M.log=function(e){var t,r=this,n=r.constructor,i=n.precision,a=i+5;if(e===void 0)e=new n(10);else if(e=new n(e),e.s<1||e.eq(ze))throw Error(rt+"NaN");if(r.s<1)throw Error(rt+(r.s?"NaN":"-Infinity"));return r.eq(ze)?new n(0):(re=!1,t=xt(sn(r,a),sn(e,a),a),re=!0,V(t,i))};M.minus=M.sub=function(e){var t=this;return e=new t.constructor(e),t.s==e.s?Ah(t,e):Ph(t,(e.s=-e.s,e))};M.modulo=M.mod=function(e){var t,r=this,n=r.constructor,i=n.precision;if(e=new n(e),!e.s)throw Error(rt+"NaN");return r.s?(re=!1,t=xt(r,e,0,1).times(e),re=!0,r.minus(t)):V(new n(r),i)};M.naturalExponential=M.exp=function(){return Oh(this)};M.naturalLogarithm=M.ln=function(){return sn(this)};M.negated=M.neg=function(){var e=new this.constructor(this);return e.s=-e.s||0,e};M.plus=M.add=function(e){var t=this;return e=new t.constructor(e),t.s==e.s?Ph(t,e):Ah(t,(e.s=-e.s,e))};M.precision=M.sd=function(e){var t,r,n,i=this;if(e!==void 0&&e!==!!e&&e!==1&&e!==0)throw Error(or+e);if(t=se(i)+1,n=i.d.length-1,r=n*ee+1,n=i.d[n],n){for(;n%10==0;n/=10)r--;for(n=i.d[0];n>=10;n/=10)r++}return e&&t>r?t:r};M.squareRoot=M.sqrt=function(){var e,t,r,n,i,a,o,u=this,c=u.constructor;if(u.s<1){if(!u.s)return new c(0);throw Error(rt+"NaN")}for(e=se(u),re=!1,i=Math.sqrt(+u),i==0||i==1/0?(t=ft(u.d),(t.length+e)%2==0&&(t+="0"),i=Math.sqrt(t),e=Lr((e+1)/2)-(e<0||e%2),i==1/0?t="5e"+e:(t=i.toExponential(),t=t.slice(0,t.indexOf("e")+1)+e),n=new c(t)):n=new c(i.toString()),r=c.precision,i=o=r+3;;)if(a=n,n=a.plus(xt(u,a,o+2)).times(.5),ft(a.d).slice(0,o)===(t=ft(n.d)).slice(0,o)){if(t=t.slice(o-3,o+1),i==o&&t=="4999"){if(V(a,r+1,0),a.times(a).eq(u)){n=a;break}}else if(t!="9999")break;o+=4}return re=!0,V(n,r)};M.times=M.mul=function(e){var t,r,n,i,a,o,u,c,s,l=this,f=l.constructor,d=l.d,v=(e=new f(e)).d;if(!l.s||!e.s)return new f(0);for(e.s*=l.s,r=l.e+e.e,c=d.length,s=v.length,c<s&&(a=d,d=v,v=a,o=c,c=s,s=o),a=[],o=c+s,n=o;n--;)a.push(0);for(n=s;--n>=0;){for(t=0,i=c+n;i>n;)u=a[i]+v[n]*d[i-n-1]+t,a[i--]=u%pe|0,t=u/pe|0;a[i]=(a[i]+t)%pe|0}for(;!a[--o];)a.pop();return t?++r:a.shift(),e.d=a,e.e=r,re?V(e,f.precision):e};M.toDecimalPlaces=M.todp=function(e,t){var r=this,n=r.constructor;return r=new n(r),e===void 0?r:(ht(e,0,Nr),t===void 0?t=n.rounding:ht(t,0,8),V(r,e+se(r)+1,t))};M.toExponential=function(e,t){var r,n=this,i=n.constructor;return e===void 0?r=sr(n,!0):(ht(e,0,Nr),t===void 0?t=i.rounding:ht(t,0,8),n=V(new i(n),e+1,t),r=sr(n,!0,e+1)),r};M.toFixed=function(e,t){var r,n,i=this,a=i.constructor;return e===void 0?sr(i):(ht(e,0,Nr),t===void 0?t=a.rounding:ht(t,0,8),n=V(new a(i),e+se(i)+1,t),r=sr(n.abs(),!1,e+se(n)+1),i.isneg()&&!i.isZero()?"-"+r:r)};M.toInteger=M.toint=function(){var e=this,t=e.constructor;return V(new t(e),se(e)+1,t.rounding)};M.toNumber=function(){return+this};M.toPower=M.pow=function(e){var t,r,n,i,a,o,u=this,c=u.constructor,s=12,l=+(e=new c(e));if(!e.s)return new c(ze);if(u=new c(u),!u.s){if(e.s<1)throw Error(rt+"Infinity");return u}if(u.eq(ze))return u;if(n=c.precision,e.eq(ze))return V(u,n);if(t=e.e,r=e.d.length-1,o=t>=r,a=u.s,o){if((r=l<0?-l:l)<=xh){for(i=new c(ze),t=Math.ceil(n/ee+4),re=!1;r%2&&(i=i.times(u),Rs(i.d,t)),r=Lr(r/2),r!==0;)u=u.times(u),Rs(u.d,t);return re=!0,e.s<0?new c(ze).div(i):V(i,n)}}else if(a<0)throw Error(rt+"NaN");return a=a<0&&e.d[Math.max(t,r)]&1?-1:1,u.s=1,re=!1,i=e.times(sn(u,n+s)),re=!0,i=Oh(i),i.s=a,i};M.toPrecision=function(e,t){var r,n,i=this,a=i.constructor;return e===void 0?(r=se(i),n=sr(i,r<=a.toExpNeg||r>=a.toExpPos)):(ht(e,1,Nr),t===void 0?t=a.rounding:ht(t,0,8),i=V(new a(i),e,t),r=se(i),n=sr(i,e<=r||r<=a.toExpNeg,e)),n};M.toSignificantDigits=M.tosd=function(e,t){var r=this,n=r.constructor;return e===void 0?(e=n.precision,t=n.rounding):(ht(e,1,Nr),t===void 0?t=n.rounding:ht(t,0,8)),V(new n(r),e,t)};M.toString=M.valueOf=M.val=M.toJSON=M[Symbol.for("nodejs.util.inspect.custom")]=function(){var e=this,t=se(e),r=e.constructor;return sr(e,t<=r.toExpNeg||t>=r.toExpPos)};function Ph(e,t){var r,n,i,a,o,u,c,s,l=e.constructor,f=l.precision;if(!e.s||!t.s)return t.s||(t=new l(e)),re?V(t,f):t;if(c=e.d,s=t.d,o=e.e,i=t.e,c=c.slice(),a=o-i,a){for(a<0?(n=c,a=-a,u=s.length):(n=s,i=o,u=c.length),o=Math.ceil(f/ee),u=o>u?o+1:u+1,a>u&&(a=u,n.length=1),n.reverse();a--;)n.push(0);n.reverse()}for(u=c.length,a=s.length,u-a<0&&(a=u,n=s,s=c,c=n),r=0;a;)r=(c[--a]=c[a]+s[a]+r)/pe|0,c[a]%=pe;for(r&&(c.unshift(r),++i),u=c.length;c[--u]==0;)c.pop();return t.d=c,t.e=i,re?V(t,f):t}function ht(e,t,r){if(e!==~~e||e<t||e>r)throw Error(or+e)}function ft(e){var t,r,n,i=e.length-1,a="",o=e[0];if(i>0){for(a+=o,t=1;t<i;t++)n=e[t]+"",r=ee-n.length,r&&(a+=It(r)),a+=n;o=e[t],n=o+"",r=ee-n.length,r&&(a+=It(r))}else if(o===0)return"0";for(;o%10===0;)o/=10;return a+o}var xt=function(){function e(n,i){var a,o=0,u=n.length;for(n=n.slice();u--;)a=n[u]*i+o,n[u]=a%pe|0,o=a/pe|0;return o&&n.unshift(o),n}function t(n,i,a,o){var u,c;if(a!=o)c=a>o?1:-1;else for(u=c=0;u<a;u++)if(n[u]!=i[u]){c=n[u]>i[u]?1:-1;break}return c}function r(n,i,a){for(var o=0;a--;)n[a]-=o,o=n[a]<i[a]?1:0,n[a]=o*pe+n[a]-i[a];for(;!n[0]&&n.length>1;)n.shift()}return function(n,i,a,o){var u,c,s,l,f,d,v,h,y,m,g,b,w,P,x,O,A,E,_=n.constructor,D=n.s==i.s?1:-1,j=n.d,C=i.d;if(!n.s)return new _(n);if(!i.s)throw Error(rt+"Division by zero");for(c=n.e-i.e,A=C.length,x=j.length,v=new _(D),h=v.d=[],s=0;C[s]==(j[s]||0);)++s;if(C[s]>(j[s]||0)&&--c,a==null?b=a=_.precision:o?b=a+(se(n)-se(i))+1:b=a,b<0)return new _(0);if(b=b/ee+2|0,s=0,A==1)for(l=0,C=C[0],b++;(s<x||l)&&b--;s++)w=l*pe+(j[s]||0),h[s]=w/C|0,l=w%C|0;else{for(l=pe/(C[0]+1)|0,l>1&&(C=e(C,l),j=e(j,l),A=C.length,x=j.length),P=A,y=j.slice(0,A),m=y.length;m<A;)y[m++]=0;E=C.slice(),E.unshift(0),O=C[0],C[1]>=pe/2&&++O;do l=0,u=t(C,y,A,m),u<0?(g=y[0],A!=m&&(g=g*pe+(y[1]||0)),l=g/O|0,l>1?(l>=pe&&(l=pe-1),f=e(C,l),d=f.length,m=y.length,u=t(f,y,d,m),u==1&&(l--,r(f,A<d?E:C,d))):(l==0&&(u=l=1),f=C.slice()),d=f.length,d<m&&f.unshift(0),r(y,f,m),u==-1&&(m=y.length,u=t(C,y,A,m),u<1&&(l++,r(y,A<m?E:C,m))),m=y.length):u===0&&(l++,y=[0]),h[s++]=l,u&&y[0]?y[m++]=j[P]||0:(y=[j[P]],m=1);while((P++<x||y[0]!==void 0)&&b--)}return h[0]||h.shift(),v.e=c,V(v,o?a+se(v)+1:a)}}();function Oh(e,t){var r,n,i,a,o,u,c=0,s=0,l=e.constructor,f=l.precision;if(se(e)>16)throw Error(Qu+se(e));if(!e.s)return new l(ze);for(t==null?(re=!1,u=f):u=t,o=new l(.03125);e.abs().gte(.1);)e=e.times(o),s+=5;for(n=Math.log(Zt(2,s))/Math.LN10*2+5|0,u+=n,r=i=a=new l(ze),l.precision=u;;){if(i=V(i.times(e),u),r=r.times(++c),o=a.plus(xt(i,r,u)),ft(o.d).slice(0,u)===ft(a.d).slice(0,u)){for(;s--;)a=V(a.times(a),u);return l.precision=f,t==null?(re=!0,V(a,f)):a}a=o}}function se(e){for(var t=e.e*ee,r=e.d[0];r>=10;r/=10)t++;return t}function za(e,t,r){if(t>e.LN10.sd())throw re=!0,r&&(e.precision=r),Error(rt+"LN10 precision limit exceeded");return V(new e(e.LN10),t)}function It(e){for(var t="";e--;)t+="0";return t}function sn(e,t){var r,n,i,a,o,u,c,s,l,f=1,d=10,v=e,h=v.d,y=v.constructor,m=y.precision;if(v.s<1)throw Error(rt+(v.s?"NaN":"-Infinity"));if(v.eq(ze))return new y(0);if(t==null?(re=!1,s=m):s=t,v.eq(10))return t==null&&(re=!0),za(y,s);if(s+=d,y.precision=s,r=ft(h),n=r.charAt(0),a=se(v),Math.abs(a)<15e14){for(;n<7&&n!=1||n==1&&r.charAt(1)>3;)v=v.times(e),r=ft(v.d),n=r.charAt(0),f++;a=se(v),n>1?(v=new y("0."+r),a++):v=new y(n+"."+r.slice(1))}else return c=za(y,s+2,m).times(a+""),v=sn(new y(n+"."+r.slice(1)),s-d).plus(c),y.precision=m,t==null?(re=!0,V(v,m)):v;for(u=o=v=xt(v.minus(ze),v.plus(ze),s),l=V(v.times(v),s),i=3;;){if(o=V(o.times(l),s),c=u.plus(xt(o,new y(i),s)),ft(c.d).slice(0,s)===ft(u.d).slice(0,s))return u=u.times(2),a!==0&&(u=u.plus(za(y,s+2,m).times(a+""))),u=xt(u,new y(f),s),y.precision=m,t==null?(re=!0,V(u,m)):u;u=c,i+=2}}function Ls(e,t){var r,n,i;for((r=t.indexOf("."))>-1&&(t=t.replace(".","")),(n=t.search(/e/i))>0?(r<0&&(r=n),r+=+t.slice(n+1),t=t.substring(0,n)):r<0&&(r=t.length),n=0;t.charCodeAt(n)===48;)++n;for(i=t.length;t.charCodeAt(i-1)===48;)--i;if(t=t.slice(n,i),t){if(i-=n,r=r-n-1,e.e=Lr(r/ee),e.d=[],n=(r+1)%ee,r<0&&(n+=ee),n<i){for(n&&e.d.push(+t.slice(0,n)),i-=ee;n<i;)e.d.push(+t.slice(n,n+=ee));t=t.slice(n),n=ee-t.length}else n-=i;for(;n--;)t+="0";if(e.d.push(+t),re&&(e.e>pi||e.e<-pi))throw Error(Qu+r)}else e.s=0,e.e=0,e.d=[0];return e}function V(e,t,r){var n,i,a,o,u,c,s,l,f=e.d;for(o=1,a=f[0];a>=10;a/=10)o++;if(n=t-o,n<0)n+=ee,i=t,s=f[l=0];else{if(l=Math.ceil((n+1)/ee),a=f.length,l>=a)return e;for(s=a=f[l],o=1;a>=10;a/=10)o++;n%=ee,i=n-ee+o}if(r!==void 0&&(a=Zt(10,o-i-1),u=s/a%10|0,c=t<0||f[l+1]!==void 0||s%a,c=r<4?(u||c)&&(r==0||r==(e.s<0?3:2)):u>5||u==5&&(r==4||c||r==6&&(n>0?i>0?s/Zt(10,o-i):0:f[l-1])%10&1||r==(e.s<0?8:7))),t<1||!f[0])return c?(a=se(e),f.length=1,t=t-a-1,f[0]=Zt(10,(ee-t%ee)%ee),e.e=Lr(-t/ee)||0):(f.length=1,f[0]=e.e=e.s=0),e;if(n==0?(f.length=l,a=1,l--):(f.length=l+1,a=Zt(10,ee-n),f[l]=i>0?(s/Zt(10,o-i)%Zt(10,i)|0)*a:0),c)for(;;)if(l==0){(f[0]+=a)==pe&&(f[0]=1,++e.e);break}else{if(f[l]+=a,f[l]!=pe)break;f[l--]=0,a=1}for(n=f.length;f[--n]===0;)f.pop();if(re&&(e.e>pi||e.e<-pi))throw Error(Qu+se(e));return e}function Ah(e,t){var r,n,i,a,o,u,c,s,l,f,d=e.constructor,v=d.precision;if(!e.s||!t.s)return t.s?t.s=-t.s:t=new d(e),re?V(t,v):t;if(c=e.d,f=t.d,n=t.e,s=e.e,c=c.slice(),o=s-n,o){for(l=o<0,l?(r=c,o=-o,u=f.length):(r=f,n=s,u=c.length),i=Math.max(Math.ceil(v/ee),u)+2,o>i&&(o=i,r.length=1),r.reverse(),i=o;i--;)r.push(0);r.reverse()}else{for(i=c.length,u=f.length,l=i<u,l&&(u=i),i=0;i<u;i++)if(c[i]!=f[i]){l=c[i]<f[i];break}o=0}for(l&&(r=c,c=f,f=r,t.s=-t.s),u=c.length,i=f.length-u;i>0;--i)c[u++]=0;for(i=f.length;i>o;){if(c[--i]<f[i]){for(a=i;a&&c[--a]===0;)c[a]=pe-1;--c[a],c[i]+=pe}c[i]-=f[i]}for(;c[--u]===0;)c.pop();for(;c[0]===0;c.shift())--n;return c[0]?(t.d=c,t.e=n,re?V(t,v):t):new d(0)}function sr(e,t,r){var n,i=se(e),a=ft(e.d),o=a.length;return t?(r&&(n=r-o)>0?a=a.charAt(0)+"."+a.slice(1)+It(n):o>1&&(a=a.charAt(0)+"."+a.slice(1)),a=a+(i<0?"e":"e+")+i):i<0?(a="0."+It(-i-1)+a,r&&(n=r-o)>0&&(a+=It(n))):i>=o?(a+=It(i+1-o),r&&(n=r-i-1)>0&&(a=a+"."+It(n))):((n=i+1)<o&&(a=a.slice(0,n)+"."+a.slice(n)),r&&(n=r-o)>0&&(i+1===o&&(a+="."),a+=It(n))),e.s<0?"-"+a:a}function Rs(e,t){if(e.length>t)return e.length=t,!0}function Sh(e){var t,r,n;function i(a){var o=this;if(!(o instanceof i))return new i(a);if(o.constructor=i,a instanceof i){o.s=a.s,o.e=a.e,o.d=(a=a.d)?a.slice():a;return}if(typeof a=="number"){if(a*0!==0)throw Error(or+a);if(a>0)o.s=1;else if(a<0)a=-a,o.s=-1;else{o.s=0,o.e=0,o.d=[0];return}if(a===~~a&&a<1e7){o.e=0,o.d=[a];return}return Ls(o,a.toString())}else if(typeof a!="string")throw Error(or+a);if(a.charCodeAt(0)===45?(a=a.slice(1),o.s=-1):o.s=1,B1.test(a))Ls(o,a);else throw Error(or+a)}if(i.prototype=M,i.ROUND_UP=0,i.ROUND_DOWN=1,i.ROUND_CEIL=2,i.ROUND_FLOOR=3,i.ROUND_HALF_UP=4,i.ROUND_HALF_DOWN=5,i.ROUND_HALF_EVEN=6,i.ROUND_HALF_CEIL=7,i.ROUND_HALF_FLOOR=8,i.clone=Sh,i.config=i.set=K1,e===void 0&&(e={}),e)for(n=["precision","rounding","toExpNeg","toExpPos","LN10"],t=0;t<n.length;)e.hasOwnProperty(r=n[t++])||(e[r]=this[r]);return i.config(e),i}function K1(e){if(!e||typeof e!="object")throw Error(rt+"Object expected");var t,r,n,i=["precision",1,Nr,"rounding",0,8,"toExpNeg",-1/0,0,"toExpPos",0,1/0];for(t=0;t<i.length;t+=3)if((n=e[r=i[t]])!==void 0)if(Lr(n)===n&&n>=i[t+1]&&n<=i[t+2])this[r]=n;else throw Error(or+r+": "+n);if((n=e[r="LN10"])!==void 0)if(n==Math.LN10)this[r]=new this(n);else throw Error(or+r+": "+n);return this}var el=Sh(R1);ze=new el(1);const H=el;var z1=e=>e,Eh={"@@functional/placeholder":!0},_h=e=>e===Eh,Bs=e=>function t(){return arguments.length===0||arguments.length===1&&_h(arguments.length<=0?void 0:arguments[0])?t:e(...arguments)},Th=(e,t)=>e===1?t:Bs(function(){for(var r=arguments.length,n=new Array(r),i=0;i<r;i++)n[i]=arguments[i];var a=n.filter(o=>o!==Eh).length;return a>=e?t(...n):Th(e-a,Bs(function(){for(var o=arguments.length,u=new Array(o),c=0;c<o;c++)u[c]=arguments[c];var s=n.map(l=>_h(l)?u.shift():l);return t(...s,...u)}))}),ua=e=>Th(e.length,e),Eo=(e,t)=>{for(var r=[],n=e;n<t;++n)r[n-e]=n;return r},F1=ua((e,t)=>Array.isArray(t)?t.map(e):Object.keys(t).map(r=>t[r]).map(e)),W1=function(){for(var t=arguments.length,r=new Array(t),n=0;n<t;n++)r[n]=arguments[n];if(!r.length)return z1;var i=r.reverse(),a=i[0],o=i.slice(1);return function(){return o.reduce((u,c)=>c(u),a(...arguments))}},_o=e=>Array.isArray(e)?e.reverse():e.split("").reverse().join(""),jh=e=>{var t=null,r=null;return function(){for(var n=arguments.length,i=new Array(n),a=0;a<n;a++)i[a]=arguments[a];return t&&i.every((o,u)=>{var c;return o===((c=t)===null||c===void 0?void 0:c[u])})||(t=i,r=e(...i)),r}};function Ch(e){var t;return e===0?t=1:t=Math.floor(new H(e).abs().log(10).toNumber())+1,t}function kh(e,t,r){for(var n=new H(e),i=0,a=[];n.lt(t)&&i<1e5;)a.push(n.toNumber()),n=n.add(r),i++;return a}ua((e,t,r)=>{var n=+e,i=+t;return n+r*(i-n)});ua((e,t,r)=>{var n=t-+e;return n=n||1/0,(r-e)/n});ua((e,t,r)=>{var n=t-+e;return n=n||1/0,Math.max(0,Math.min(1,(r-e)/n))});var Mh=e=>{var[t,r]=e,[n,i]=[t,r];return t>r&&([n,i]=[r,t]),[n,i]},Dh=(e,t,r)=>{if(e.lte(0))return new H(0);var n=Ch(e.toNumber()),i=new H(10).pow(n),a=e.div(i),o=n!==1?.05:.1,u=new H(Math.ceil(a.div(o).toNumber())).add(r).mul(o),c=u.mul(i);return t?new H(c.toNumber()):new H(Math.ceil(c.toNumber()))},U1=(e,t,r)=>{var n=new H(1),i=new H(e);if(!i.isint()&&r){var a=Math.abs(e);a<1?(n=new H(10).pow(Ch(e)-1),i=new H(Math.floor(i.div(n).toNumber())).mul(n)):a>1&&(i=new H(Math.floor(e)))}else e===0?i=new H(Math.floor((t-1)/2)):r||(i=new H(Math.floor(e)));var o=Math.floor((t-1)/2),u=W1(F1(c=>i.add(new H(c-o).mul(n)).toNumber()),Eo);return u(0,t)},Ih=function(t,r,n,i){var a=arguments.length>4&&arguments[4]!==void 0?arguments[4]:0;if(!Number.isFinite((r-t)/(n-1)))return{step:new H(0),tickMin:new H(0),tickMax:new H(0)};var o=Dh(new H(r).sub(t).div(n-1),i,a),u;t<=0&&r>=0?u=new H(0):(u=new H(t).add(r).div(2),u=u.sub(new H(u).mod(o)));var c=Math.ceil(u.sub(t).div(o).toNumber()),s=Math.ceil(new H(r).sub(u).div(o).toNumber()),l=c+s+1;return l>n?Ih(t,r,n,i,a+1):(l<n&&(s=r>0?s+(n-l):s,c=r>0?c:c+(n-l)),{step:o,tickMin:u.sub(new H(c).mul(o)),tickMax:u.add(new H(s).mul(o))})};function q1(e){var[t,r]=e,n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:6,i=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!0,a=Math.max(n,2),[o,u]=Mh([t,r]);if(o===-1/0||u===1/0){var c=u===1/0?[o,...Eo(0,n-1).map(()=>1/0)]:[...Eo(0,n-1).map(()=>-1/0),u];return t>r?_o(c):c}if(o===u)return U1(o,n,i);var{step:s,tickMin:l,tickMax:f}=Ih(o,u,a,i,0),d=kh(l,f.add(new H(.1).mul(s)),s);return t>r?_o(d):d}function Y1(e,t){var[r,n]=e,i=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!0,[a,o]=Mh([r,n]);if(a===-1/0||o===1/0)return[r,n];if(a===o)return[a];var u=Math.max(t,2),c=Dh(new H(o).sub(a).div(u-1),i,0),s=[...kh(new H(a),new H(o),c),o];return i===!1&&(s=s.map(l=>Math.round(l))),r>n?_o(s):s}var H1=jh(q1),G1=jh(Y1),$h=e=>e.rootProps.maxBarSize,V1=e=>e.rootProps.barGap,Nh=e=>e.rootProps.barCategoryGap,X1=e=>e.rootProps.barSize,Pn=e=>e.rootProps.stackOffset,tl=e=>e.options.chartName,rl=e=>e.rootProps.syncId,Lh=e=>e.rootProps.syncMethod,nl=e=>e.options.eventEmitter,mt={allowDuplicatedCategory:!0,angleAxisId:0,axisLine:!0,cx:0,cy:0,orientation:"outer",reversed:!1,scale:"auto",tick:!0,tickLine:!0,tickSize:8,type:"category"},Re={allowDataOverflow:!1,allowDuplicatedCategory:!0,angle:0,axisLine:!0,cx:0,cy:0,orientation:"right",radiusAxisId:0,scale:"auto",stroke:"#ccc",tick:!0,tickCount:5,type:"number"},la=(e,t)=>{if(!(!e||!t))return e!=null&&e.reversed?[t[1],t[0]]:t},Z1={allowDataOverflow:!1,allowDecimals:!1,allowDuplicatedCategory:!1,dataKey:void 0,domain:void 0,id:mt.angleAxisId,includeHidden:!1,name:void 0,reversed:mt.reversed,scale:mt.scale,tick:mt.tick,tickCount:void 0,ticks:void 0,type:mt.type,unit:void 0},J1={allowDataOverflow:Re.allowDataOverflow,allowDecimals:!1,allowDuplicatedCategory:Re.allowDuplicatedCategory,dataKey:void 0,domain:void 0,id:Re.radiusAxisId,includeHidden:!1,name:void 0,reversed:!1,scale:Re.scale,tick:Re.tick,tickCount:Re.tickCount,ticks:void 0,type:Re.type,unit:void 0},Q1={allowDataOverflow:!1,allowDecimals:!1,allowDuplicatedCategory:mt.allowDuplicatedCategory,dataKey:void 0,domain:void 0,id:mt.angleAxisId,includeHidden:!1,name:void 0,reversed:!1,scale:mt.scale,tick:mt.tick,tickCount:void 0,ticks:void 0,type:"number",unit:void 0},eO={allowDataOverflow:Re.allowDataOverflow,allowDecimals:!1,allowDuplicatedCategory:Re.allowDuplicatedCategory,dataKey:void 0,domain:void 0,id:Re.radiusAxisId,includeHidden:!1,name:void 0,reversed:!1,scale:Re.scale,tick:Re.tick,tickCount:Re.tickCount,ticks:void 0,type:"category",unit:void 0},il=(e,t)=>e.polarAxis.angleAxis[t]!=null?e.polarAxis.angleAxis[t]:e.layout.layoutType==="radial"?Q1:Z1,al=(e,t)=>e.polarAxis.radiusAxis[t]!=null?e.polarAxis.radiusAxis[t]:e.layout.layoutType==="radial"?eO:J1,ca=e=>e.polarOptions,ol=S([_t,Tt,ve],vv),Rh=S([ca,ol],(e,t)=>{if(e!=null)return Te(e.innerRadius,t,0)}),Bh=S([ca,ol],(e,t)=>{if(e!=null)return Te(e.outerRadius,t,t*.8)}),tO=e=>{if(e==null)return[0,0];var{startAngle:t,endAngle:r}=e;return[t,r]},Kh=S([ca],tO);S([il,Kh],la);var zh=S([ol,Rh,Bh],(e,t,r)=>{if(!(e==null||t==null||r==null))return[t,r]});S([al,zh],la);var rO=S([Y,ca,Rh,Bh,_t,Tt],(e,t,r,n,i,a)=>{if(!(e!=="centric"&&e!=="radial"||t==null||r==null||n==null)){var{cx:o,cy:u,startAngle:c,endAngle:s}=t;return{cx:Te(o,i,i/2),cy:Te(u,a,a/2),innerRadius:r,outerRadius:n,startAngle:c,endAngle:s,clockWise:!1}}}),oe=(e,t)=>t,On=(e,t,r)=>r;function Ks(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function mi(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Ks(Object(r),!0).forEach(function(n){nO(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Ks(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function nO(e,t,r){return(t=iO(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function iO(e){var t=aO(e,"string");return typeof t=="symbol"?t:t+""}function aO(e,t){if(typeof e!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t||"default");if(typeof n!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var To=[0,"auto"],Ne={allowDataOverflow:!1,allowDecimals:!0,allowDuplicatedCategory:!0,angle:0,dataKey:void 0,domain:void 0,height:30,hide:!0,id:0,includeHidden:!1,interval:"preserveEnd",minTickGap:5,mirror:!1,name:void 0,orientation:"bottom",padding:{left:0,right:0},reversed:!1,scale:"auto",tick:!0,tickCount:5,tickFormatter:void 0,ticks:void 0,type:"category",unit:void 0},Ct=(e,t)=>{var r=e.cartesianAxis.xAxis[t];return r??Ne},Le={allowDataOverflow:!1,allowDecimals:!0,allowDuplicatedCategory:!0,angle:0,dataKey:void 0,domain:To,hide:!0,id:0,includeHidden:!1,interval:"preserveEnd",minTickGap:5,mirror:!1,name:void 0,orientation:"left",padding:{top:0,bottom:0},reversed:!1,scale:"auto",tick:!0,tickCount:5,tickFormatter:void 0,ticks:void 0,type:"number",unit:void 0,width:Zi},qt=(e,t)=>{var r=e.cartesianAxis.yAxis[t];return r??Le},oO={domain:[0,"auto"],includeHidden:!1,reversed:!1,allowDataOverflow:!1,allowDuplicatedCategory:!1,dataKey:void 0,id:0,name:"",range:[64,64],scale:"auto",type:"number",unit:""},ul=(e,t)=>{var r=e.cartesianAxis.zAxis[t];return r??oO},ue=(e,t,r)=>{switch(t){case"xAxis":return Ct(e,r);case"yAxis":return qt(e,r);case"zAxis":return ul(e,r);case"angleAxis":return il(e,r);case"radiusAxis":return al(e,r);default:throw new Error("Unexpected axis type: ".concat(t))}},uO=(e,t,r)=>{switch(t){case"xAxis":return Ct(e,r);case"yAxis":return qt(e,r);default:throw new Error("Unexpected axis type: ".concat(t))}},An=(e,t,r)=>{switch(t){case"xAxis":return Ct(e,r);case"yAxis":return qt(e,r);case"angleAxis":return il(e,r);case"radiusAxis":return al(e,r);default:throw new Error("Unexpected axis type: ".concat(t))}},Fh=e=>e.graphicalItems.countOfBars>0;function ll(e,t){return r=>{switch(e){case"xAxis":return"xAxisId"in r&&r.xAxisId===t;case"yAxis":return"yAxisId"in r&&r.yAxisId===t;case"zAxis":return"zAxisId"in r&&r.zAxisId===t;case"angleAxis":return"angleAxisId"in r&&r.angleAxisId===t;case"radiusAxis":return"radiusAxisId"in r&&r.radiusAxisId===t;default:return!1}}}var sa=e=>e.graphicalItems.cartesianItems,lO=S([oe,On],ll),cl=(e,t,r)=>e.filter(r).filter(n=>(t==null?void 0:t.includeHidden)===!0?!0:!n.hide),Sn=S([sa,ue,lO],cl),Wh=e=>e.filter(t=>t.stackId===void 0),cO=S([Sn],Wh),sl=e=>e.map(t=>t.data).filter(Boolean).flat(1),sO=S([Sn],sl),fl=(e,t)=>{var{chartData:r=[],dataStartIndex:n,dataEndIndex:i}=t;return e.length>0?e:r.slice(n,i+1)},fa=S([sO,Ju],fl),dl=(e,t,r)=>(t==null?void 0:t.dataKey)!=null?e.map(n=>({value:te(n,t.dataKey)})):r.length>0?r.map(n=>n.dataKey).flatMap(n=>e.map(i=>({value:te(i,n)}))):e.map(n=>({value:n})),da=S([fa,ue,Sn],dl);function Uh(e,t){switch(e){case"xAxis":return t.direction==="x";case"yAxis":return t.direction==="y";default:return!1}}function mr(e){return e.filter(t=>vt(t)||t instanceof Date).map(Number).filter(t=>We(t)===!1)}function fO(e,t,r){return!r||typeof t!="number"||We(t)?[]:r.length?mr(r.flatMap(n=>{var i=te(e,n.dataKey),a,o;if(Array.isArray(i)?[a,o]=i:a=o=i,!(!Ye(a)||!Ye(o)))return[t-a,t+o]})):[]}var qh=(e,t,r)=>{var n={},i=t.reduce((a,o)=>(o.stackId==null||(a[o.stackId]==null&&(a[o.stackId]=[]),a[o.stackId].push(o)),a),n);return Object.fromEntries(Object.entries(i).map(a=>{var[o,u]=a,c=u.map(s=>s.dataKey);return[o,{stackedData:bb(e,c,r),graphicalItems:u}]}))},jo=S([fa,Sn,Pn],qh),Yh=(e,t,r)=>{var{dataStartIndex:n,dataEndIndex:i}=t;if(r!=="zAxis"){var a=Ob(e,n,i);if(!(a!=null&&a[0]===0&&a[1]===0))return a}},dO=S([jo,pr,oe],Yh),Hh=(e,t,r,n)=>r.length>0?e.flatMap(i=>r.flatMap(a=>{var o,u,c=(o=a.errorBars)===null||o===void 0?void 0:o.filter(l=>Uh(n,l)),s=te(i,(u=t.dataKey)!==null&&u!==void 0?u:a.dataKey);return{value:s,errorDomain:fO(i,s,c)}})).filter(Boolean):(t==null?void 0:t.dataKey)!=null?e.map(i=>({value:te(i,t.dataKey),errorDomain:[]})):e.map(i=>({value:i,errorDomain:[]})),vO=S(fa,ue,cO,oe,Hh);function hO(e){var{value:t}=e;if(vt(t)||t instanceof Date)return t}var pO=e=>{var t=e.flatMap(n=>[n.value,n.errorDomain]).flat(1),r=mr(t);if(r.length!==0)return[Math.min(...r),Math.max(...r)]},mO=(e,t,r)=>{var n=e.map(hO).filter(i=>i!=null);return r&&(t.dataKey==null||t.allowDuplicatedCategory&&Hf(n))?Kv(0,e.length):t.allowDuplicatedCategory?n:Array.from(new Set(n))},vl=e=>{var t;if(e==null||!("domain"in e))return To;if(e.domain!=null)return e.domain;if(e.ticks!=null){if(e.type==="number"){var r=mr(e.ticks);return[Math.min(...r),Math.max(...r)]}if(e.type==="category")return e.ticks.map(String)}return(t=e==null?void 0:e.domain)!==null&&t!==void 0?t:To},hl=function(){for(var t=arguments.length,r=new Array(t),n=0;n<t;n++)r[n]=arguments[n];var i=r.filter(Boolean);if(i.length!==0){var a=i.flat(),o=Math.min(...a),u=Math.max(...a);return[o,u]}},Gh=e=>e.referenceElements.dots,Rr=(e,t,r)=>e.filter(n=>n.ifOverflow==="extendDomain").filter(n=>t==="xAxis"?n.xAxisId===r:n.yAxisId===r),yO=S([Gh,oe,On],Rr),Vh=e=>e.referenceElements.areas,gO=S([Vh,oe,On],Rr),Xh=e=>e.referenceElements.lines,bO=S([Xh,oe,On],Rr),Zh=(e,t)=>{var r=mr(e.map(n=>t==="xAxis"?n.x:n.y));if(r.length!==0)return[Math.min(...r),Math.max(...r)]},wO=S(yO,oe,Zh),Jh=(e,t)=>{var r=mr(e.flatMap(n=>[t==="xAxis"?n.x1:n.y1,t==="xAxis"?n.x2:n.y2]));if(r.length!==0)return[Math.min(...r),Math.max(...r)]},xO=S([gO,oe],Jh),Qh=(e,t)=>{var r=mr(e.map(n=>t==="xAxis"?n.x:n.y));if(r.length!==0)return[Math.min(...r),Math.max(...r)]},PO=S(bO,oe,Qh),OO=S(wO,PO,xO,(e,t,r)=>hl(e,r,t)),ep=S([ue],vl),pl=(e,t,r,n,i)=>{var a=N1(t,e.allowDataOverflow);return a??L1(t,hl(r,i,pO(n)),e.allowDataOverflow)},AO=S([ue,ep,dO,vO,OO],pl),SO=[0,1],ml=(e,t,r,n,i,a,o)=>{if(!(e==null||r==null||r.length===0)){var{dataKey:u,type:c}=e,s=Ft(t,a);return s&&u==null?Kv(0,r.length):c==="category"?mO(n,e,s):i==="expand"?SO:o}},yl=S([ue,Y,fa,da,Pn,oe,AO],ml),tp=(e,t,r,n,i)=>{if(e!=null){var{scale:a,type:o}=e;if(a==="auto")return t==="radial"&&i==="radiusAxis"?"band":t==="radial"&&i==="angleAxis"?"linear":o==="category"&&n&&(n.indexOf("LineChart")>=0||n.indexOf("AreaChart")>=0||n.indexOf("ComposedChart")>=0&&!r)?"point":o==="category"?"band":"linear";if(typeof a=="string"){var u="scale".concat(hn(a));return u in Jr?u:"point"}}},Br=S([ue,Y,Fh,tl,oe],tp);function EO(e){if(e!=null){if(e in Jr)return Jr[e]();var t="scale".concat(hn(e));if(t in Jr)return Jr[t]()}}function gl(e,t,r,n){if(!(r==null||n==null)){if(typeof e.scale=="function")return e.scale.copy().domain(r).range(n);var i=EO(t);if(i!=null){var a=i.domain(r).range(n);return hb(a),a}}}var bl=(e,t,r)=>{var n=vl(t);if(!(r!=="auto"&&r!=="linear")){if(t!=null&&t.tickCount&&Array.isArray(n)&&(n[0]==="auto"||n[1]==="auto")&&Mr(e))return H1(e,t.tickCount,t.allowDecimals);if(t!=null&&t.tickCount&&t.type==="number"&&Mr(e))return G1(e,t.tickCount,t.allowDecimals)}},wl=S([yl,An,Br],bl),xl=(e,t,r,n)=>{if(n!=="angleAxis"&&(e==null?void 0:e.type)==="number"&&Mr(t)&&Array.isArray(r)&&r.length>0){var i=t[0],a=r[0],o=t[1],u=r[r.length-1];return[Math.min(i,a),Math.max(o,u)]}return t},_O=S([ue,yl,wl,oe],xl),TO=S(da,ue,(e,t)=>{if(!(!t||t.type!=="number")){var r=1/0,n=Array.from(mr(e.map(u=>u.value))).sort((u,c)=>u-c);if(n.length<2)return 1/0;var i=n[n.length-1]-n[0];if(i===0)return 1/0;for(var a=0;a<n.length-1;a++){var o=n[a+1]-n[a];r=Math.min(r,o)}return r/i}}),rp=S(TO,Y,Nh,ve,(e,t,r,n)=>n,(e,t,r,n,i)=>{if(!Ye(e))return 0;var a=t==="vertical"?n.height:n.width;if(i==="gap")return e*a/2;if(i==="no-gap"){var o=Te(r,e*a),u=e*a/2;return u-o-(u-o)/a*o}return 0}),jO=(e,t)=>{var r=Ct(e,t);return r==null||typeof r.padding!="string"?0:rp(e,"xAxis",t,r.padding)},CO=(e,t)=>{var r=qt(e,t);return r==null||typeof r.padding!="string"?0:rp(e,"yAxis",t,r.padding)},kO=S(Ct,jO,(e,t)=>{var r,n;if(e==null)return{left:0,right:0};var{padding:i}=e;return typeof i=="string"?{left:t,right:t}:{left:((r=i.left)!==null&&r!==void 0?r:0)+t,right:((n=i.right)!==null&&n!==void 0?n:0)+t}}),MO=S(qt,CO,(e,t)=>{var r,n;if(e==null)return{top:0,bottom:0};var{padding:i}=e;return typeof i=="string"?{top:t,bottom:t}:{top:((r=i.top)!==null&&r!==void 0?r:0)+t,bottom:((n=i.bottom)!==null&&n!==void 0?n:0)+t}}),DO=S([ve,kO,Qi,Ji,(e,t,r)=>r],(e,t,r,n,i)=>{var{padding:a}=n;return i?[a.left,r.width-a.right]:[e.left+t.left,e.left+e.width-t.right]}),IO=S([ve,Y,MO,Qi,Ji,(e,t,r)=>r],(e,t,r,n,i,a)=>{var{padding:o}=i;return a?[n.height-o.bottom,o.top]:t==="horizontal"?[e.top+e.height-r.bottom,e.top+r.top]:[e.top+r.top,e.top+e.height-r.bottom]}),En=(e,t,r,n)=>{var i;switch(t){case"xAxis":return DO(e,r,n);case"yAxis":return IO(e,r,n);case"zAxis":return(i=ul(e,r))===null||i===void 0?void 0:i.range;case"angleAxis":return Kh(e);case"radiusAxis":return zh(e,r);default:return}},np=S([ue,En],la),Kr=S([ue,Br,_O,np],gl);S(Sn,oe,(e,t)=>e.flatMap(r=>{var n;return(n=r.errorBars)!==null&&n!==void 0?n:[]}).filter(r=>Uh(t,r)));function ip(e,t){return e.id<t.id?-1:e.id>t.id?1:0}var va=(e,t)=>t,ha=(e,t,r)=>r,$O=S(bu,va,ha,(e,t,r)=>e.filter(n=>n.orientation===t).filter(n=>n.mirror===r).sort(ip)),NO=S(wu,va,ha,(e,t,r)=>e.filter(n=>n.orientation===t).filter(n=>n.mirror===r).sort(ip)),ap=(e,t)=>({width:e.width,height:t.height}),LO=(e,t)=>{var r=typeof t.width=="number"?t.width:Zi;return{width:r,height:e.height}},op=S(ve,Ct,ap),RO=(e,t,r)=>{switch(t){case"top":return e.top;case"bottom":return r-e.bottom;default:return 0}},BO=(e,t,r)=>{switch(t){case"left":return e.left;case"right":return r-e.right;default:return 0}},KO=S(Tt,ve,$O,va,ha,(e,t,r,n,i)=>{var a={},o;return r.forEach(u=>{var c=ap(t,u);o==null&&(o=RO(t,n,e));var s=n==="top"&&!i||n==="bottom"&&i;a[u.id]=o-Number(s)*c.height,o+=(s?-1:1)*c.height}),a}),zO=S(_t,ve,NO,va,ha,(e,t,r,n,i)=>{var a={},o;return r.forEach(u=>{var c=LO(t,u);o==null&&(o=BO(t,n,e));var s=n==="left"&&!i||n==="right"&&i;a[u.id]=o-Number(s)*c.width,o+=(s?-1:1)*c.width}),a}),FO=(e,t)=>{var r=ve(e),n=Ct(e,t);if(n!=null){var i=KO(e,n.orientation,n.mirror),a=i[t];return a==null?{x:r.left,y:0}:{x:r.left,y:a}}},WO=(e,t)=>{var r=ve(e),n=qt(e,t);if(n!=null){var i=zO(e,n.orientation,n.mirror),a=i[t];return a==null?{x:0,y:r.top}:{x:a,y:r.top}}},up=S(ve,qt,(e,t)=>{var r=typeof t.width=="number"?t.width:Zi;return{width:r,height:e.height}}),zs=(e,t,r)=>{switch(t){case"xAxis":return op(e,r).width;case"yAxis":return up(e,r).height;default:return}},lp=(e,t,r,n)=>{if(r!=null){var{allowDuplicatedCategory:i,type:a,dataKey:o}=r,u=Ft(e,n),c=t.map(s=>s.value);if(o&&u&&a==="category"&&i&&Hf(c))return c}},Pl=S([Y,da,ue,oe],lp),cp=(e,t,r,n)=>{if(!(r==null||r.dataKey==null)){var{type:i,scale:a}=r,o=Ft(e,n);if(o&&(i==="number"||a!=="auto"))return t.map(u=>u.value)}},Ol=S([Y,da,An,oe],cp),Fs=S([Y,uO,Br,Kr,Pl,Ol,En,wl,oe],(e,t,r,n,i,a,o,u,c)=>{if(t==null)return null;var s=Ft(e,c);return{angle:t.angle,interval:t.interval,minTickGap:t.minTickGap,orientation:t.orientation,tick:t.tick,tickCount:t.tickCount,tickFormatter:t.tickFormatter,ticks:t.ticks,type:t.type,unit:t.unit,axisType:c,categoricalDomain:a,duplicateDomain:i,isCategorical:s,niceTicks:u,range:o,realScaleType:r,scale:n}}),UO=(e,t,r,n,i,a,o,u,c)=>{if(!(t==null||n==null)){var s=Ft(e,c),{type:l,ticks:f,tickCount:d}=t,v=r==="scaleBand"&&typeof n.bandwidth=="function"?n.bandwidth()/2:2,h=l==="category"&&n.bandwidth?n.bandwidth()/v:0;h=c==="angleAxis"&&a!=null&&a.length>=2?me(a[0]-a[1])*2*h:h;var y=f||i;if(y){var m=y.map((g,b)=>{var w=o?o.indexOf(g):g;return{index:b,coordinate:n(w)+h,value:g,offset:h}});return m.filter(g=>!We(g.coordinate))}return s&&u?u.map((g,b)=>({coordinate:n(g)+h,value:g,index:b,offset:h})):n.ticks?n.ticks(d).map(g=>({coordinate:n(g)+h,value:g,offset:h})):n.domain().map((g,b)=>({coordinate:n(g)+h,value:o?o[g]:g,index:b,offset:h}))}},sp=S([Y,An,Br,Kr,wl,En,Pl,Ol,oe],UO),qO=(e,t,r,n,i,a,o)=>{if(!(t==null||r==null||n==null||n[0]===n[1])){var u=Ft(e,o),{tickCount:c}=t,s=0;return s=o==="angleAxis"&&(n==null?void 0:n.length)>=2?me(n[0]-n[1])*2*s:s,u&&a?a.map((l,f)=>({coordinate:r(l)+s,value:l,index:f,offset:s})):r.ticks?r.ticks(c).map(l=>({coordinate:r(l)+s,value:l,offset:s})):r.domain().map((l,f)=>({coordinate:r(l)+s,value:i?i[l]:l,index:f,offset:s}))}},Kt=S([Y,An,Kr,En,Pl,Ol,oe],qO),pt=S(ue,Kr,(e,t)=>{if(!(e==null||t==null))return mi(mi({},e),{},{scale:t})}),YO=S([ue,Br,yl,np],gl);S((e,t,r)=>ul(e,r),YO,(e,t)=>{if(!(e==null||t==null))return mi(mi({},e),{},{scale:t})});var HO=S([Y,bu,wu],(e,t,r)=>{switch(e){case"horizontal":return t.some(n=>n.reversed)?"right-to-left":"left-to-right";case"vertical":return r.some(n=>n.reversed)?"bottom-to-top":"top-to-bottom";case"centric":case"radial":return"left-to-right";default:return}}),fp=e=>e.options.defaultTooltipEventType,dp=e=>e.options.validateTooltipEventTypes;function vp(e,t,r){if(e==null)return t;var n=e?"axis":"item";return r==null?t:r.includes(n)?n:t}function Al(e,t){var r=fp(e),n=dp(e);return vp(t,r,n)}function GO(e){return $(t=>Al(t,e))}var hp=(e,t)=>{var r,n=Number(t);if(!(We(n)||t==null))return n>=0?e==null||(r=e[n])===null||r===void 0?void 0:r.value:void 0},VO=e=>e.tooltip.settings,$t={active:!1,index:null,dataKey:void 0,coordinate:void 0},XO={itemInteraction:{click:$t,hover:$t},axisInteraction:{click:$t,hover:$t},keyboardInteraction:$t,syncInteraction:{active:!1,index:null,dataKey:void 0,label:void 0,coordinate:void 0},tooltipItemPayloads:[],settings:{shared:void 0,trigger:"hover",axisId:0,active:!1,defaultIndex:void 0}},pp=it({name:"tooltip",initialState:XO,reducers:{addTooltipEntrySettings(e,t){e.tooltipItemPayloads.push(t.payload)},removeTooltipEntrySettings(e,t){var r=wt(e).tooltipItemPayloads.indexOf(t.payload);r>-1&&e.tooltipItemPayloads.splice(r,1)},setTooltipSettingsState(e,t){e.settings=t.payload},setActiveMouseOverItemIndex(e,t){e.syncInteraction.active=!1,e.keyboardInteraction.active=!1,e.itemInteraction.hover.active=!0,e.itemInteraction.hover.index=t.payload.activeIndex,e.itemInteraction.hover.dataKey=t.payload.activeDataKey,e.itemInteraction.hover.coordinate=t.payload.activeCoordinate},mouseLeaveChart(e){e.itemInteraction.hover.active=!1,e.axisInteraction.hover.active=!1},mouseLeaveItem(e){e.itemInteraction.hover.active=!1},setActiveClickItemIndex(e,t){e.syncInteraction.active=!1,e.itemInteraction.click.active=!0,e.keyboardInteraction.active=!1,e.itemInteraction.click.index=t.payload.activeIndex,e.itemInteraction.click.dataKey=t.payload.activeDataKey,e.itemInteraction.click.coordinate=t.payload.activeCoordinate},setMouseOverAxisIndex(e,t){e.syncInteraction.active=!1,e.axisInteraction.hover.active=!0,e.keyboardInteraction.active=!1,e.axisInteraction.hover.index=t.payload.activeIndex,e.axisInteraction.hover.dataKey=t.payload.activeDataKey,e.axisInteraction.hover.coordinate=t.payload.activeCoordinate},setMouseClickAxisIndex(e,t){e.syncInteraction.active=!1,e.keyboardInteraction.active=!1,e.axisInteraction.click.active=!0,e.axisInteraction.click.index=t.payload.activeIndex,e.axisInteraction.click.dataKey=t.payload.activeDataKey,e.axisInteraction.click.coordinate=t.payload.activeCoordinate},setSyncInteraction(e,t){e.syncInteraction=t.payload},setKeyboardInteraction(e,t){e.keyboardInteraction.active=t.payload.active,e.keyboardInteraction.index=t.payload.activeIndex,e.keyboardInteraction.coordinate=t.payload.activeCoordinate,e.keyboardInteraction.dataKey=t.payload.activeDataKey}}}),{addTooltipEntrySettings:ZO,removeTooltipEntrySettings:JO,setTooltipSettingsState:QO,setActiveMouseOverItemIndex:mp,mouseLeaveItem:eA,mouseLeaveChart:yp,setActiveClickItemIndex:tA,setMouseOverAxisIndex:gp,setMouseClickAxisIndex:rA,setSyncInteraction:Co,setKeyboardInteraction:ko}=pp.actions,nA=pp.reducer;function Ws(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Kn(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Ws(Object(r),!0).forEach(function(n){iA(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Ws(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function iA(e,t,r){return(t=aA(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function aA(e){var t=oA(e,"string");return typeof t=="symbol"?t:t+""}function oA(e,t){if(typeof e!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t||"default");if(typeof n!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function uA(e,t,r){return t==="axis"?r==="click"?e.axisInteraction.click:e.axisInteraction.hover:r==="click"?e.itemInteraction.click:e.itemInteraction.hover}function lA(e){return e.index!=null}var bp=(e,t,r,n)=>{if(t==null)return $t;var i=uA(e,t,r);if(i==null)return $t;if(i.active)return i;if(e.keyboardInteraction.active)return e.keyboardInteraction;if(e.syncInteraction.active&&e.syncInteraction.index!=null)return e.syncInteraction;var a=e.settings.active===!0;if(lA(i)){if(a)return Kn(Kn({},i),{},{active:!0})}else if(n!=null)return{active:!0,coordinate:void 0,dataKey:void 0,index:n};return Kn(Kn({},$t),{},{coordinate:i.coordinate})},Sl=(e,t)=>{var r=e==null?void 0:e.index;if(r==null)return null;var n=Number(r);if(!Ye(n))return r;var i=0,a=1/0;return t.length>0&&(a=t.length-1),String(Math.max(i,Math.min(n,a)))},wp=(e,t,r,n,i,a,o,u)=>{if(!(a==null||u==null)){var c=o[0],s=c==null?void 0:u(c.positions,a);if(s!=null)return s;var l=i==null?void 0:i[Number(a)];if(l)switch(r){case"horizontal":return{x:l.coordinate,y:(n.top+t)/2};default:return{x:(n.left+e)/2,y:l.coordinate}}}},xp=(e,t,r,n)=>{if(t==="axis")return e.tooltipItemPayloads;if(e.tooltipItemPayloads.length===0)return[];var i;return r==="hover"?i=e.itemInteraction.hover.dataKey:i=e.itemInteraction.click.dataKey,i==null&&n!=null?[e.tooltipItemPayloads[0]]:e.tooltipItemPayloads.filter(a=>{var o;return((o=a.settings)===null||o===void 0?void 0:o.dataKey)===i})},_n=e=>e.options.tooltipPayloadSearcher,zr=e=>e.tooltip;function Us(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function qs(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Us(Object(r),!0).forEach(function(n){cA(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Us(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function cA(e,t,r){return(t=sA(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function sA(e){var t=fA(e,"string");return typeof t=="symbol"?t:t+""}function fA(e,t){if(typeof e!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t||"default");if(typeof n!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function dA(e,t,r){return Array.isArray(e)&&e&&t+r!==0?e.slice(t,r+1):e}function vA(e,t){return e??t}var Pp=(e,t,r,n,i,a,o)=>{if(!(t==null||a==null)){var{chartData:u,computedData:c,dataStartIndex:s,dataEndIndex:l}=r,f=[];return e.reduce((d,v)=>{var h,{dataDefinedOnItem:y,settings:m}=v,g=vA(y,u),b=dA(g,s,l),w=(h=m==null?void 0:m.dataKey)!==null&&h!==void 0?h:n==null?void 0:n.dataKey,P=m==null?void 0:m.nameKey,x;if(n!=null&&n.dataKey&&Array.isArray(b)&&!Array.isArray(b[0])&&o==="axis"?x=Gf(b,n.dataKey,i):x=a(b,t,c,P),Array.isArray(x))x.forEach(A=>{var E=qs(qs({},m),{},{name:A.name,unit:A.unit,color:void 0,fill:void 0});d.push(Ic({tooltipEntrySettings:E,dataKey:A.dataKey,payload:A.payload,value:te(A.payload,A.dataKey),name:A.name}))});else{var O;d.push(Ic({tooltipEntrySettings:m,dataKey:w,payload:x,value:te(x,w),name:(O=te(x,P))!==null&&O!==void 0?O:m==null?void 0:m.name}))}return d},f)}},be=e=>{var t=Y(e);return t==="horizontal"?"xAxis":t==="vertical"?"yAxis":t==="centric"?"angleAxis":"radiusAxis"},Fr=e=>e.tooltip.settings.axisId,Oe=e=>{var t=be(e),r=Fr(e);return An(e,t,r)},El=S([Oe,Y,Fh,tl,be],tp),hA=S([e=>e.graphicalItems.cartesianItems,e=>e.graphicalItems.polarItems],(e,t)=>[...e,...t]),pA=S([be,Fr],ll),pa=S([hA,Oe,pA],cl),mA=S([pa],sl),yr=S([mA,pr],fl),_l=S([yr,Oe,pa],dl),yA=S([Oe],vl),gA=S([yr,pa,Pn],qh),bA=S([gA,pr,be],Yh),wA=S([pa],Wh),xA=S([yr,Oe,wA,be],Hh),PA=S([Gh,be,Fr],Rr),OA=S([PA,be],Zh),AA=S([Vh,be,Fr],Rr),SA=S([AA,be],Jh),EA=S([Xh,be,Fr],Rr),_A=S([EA,be],Qh),TA=S([OA,_A,SA],hl),jA=S([Oe,yA,bA,xA,TA],pl),Op=S([Oe,Y,yr,_l,Pn,be,jA],ml),CA=S([Op,Oe,El],bl),kA=S([Oe,Op,CA,be],xl),Ap=e=>{var t=be(e),r=Fr(e),n=!1;return En(e,t,r,n)},Sp=S([Oe,Ap],la),Ep=S([Oe,El,kA,Sp],gl),MA=S([Y,_l,Oe,be],lp),DA=S([Y,_l,Oe,be],cp),IA=(e,t,r,n,i,a,o,u)=>{if(t){var{type:c}=t,s=Ft(e,u);if(n){var l=r==="scaleBand"&&n.bandwidth?n.bandwidth()/2:2,f=c==="category"&&n.bandwidth?n.bandwidth()/l:0;return f=u==="angleAxis"&&i!=null&&(i==null?void 0:i.length)>=2?me(i[0]-i[1])*2*f:f,s&&o?o.map((d,v)=>({coordinate:n(d)+f,value:d,index:v,offset:f})):n.domain().map((d,v)=>({coordinate:n(d)+f,value:a?a[d]:d,index:v,offset:f}))}}},kt=S([Y,Oe,El,Ep,Ap,MA,DA,be],IA),Tl=S([fp,dp,VO],(e,t,r)=>vp(r.shared,e,t)),_p=e=>e.tooltip.settings.trigger,jl=e=>e.tooltip.settings.defaultIndex,ma=S([zr,Tl,_p,jl],bp),zt=S([ma,yr],Sl),Tp=S([kt,zt],hp),jp=S([ma],e=>{if(e)return e.dataKey}),Cp=S([zr,Tl,_p,jl],xp),$A=S([_t,Tt,Y,ve,kt,jl,Cp,_n],wp),NA=S([ma,$A],(e,t)=>e!=null&&e.coordinate?e.coordinate:t),LA=S([ma],e=>e.active),RA=S([Cp,zt,pr,Oe,Tp,_n,Tl],Pp),BA=S([RA],e=>{if(e!=null){var t=e.map(r=>r.payload).filter(r=>r!=null);return Array.from(new Set(t))}});function Ys(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Hs(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Ys(Object(r),!0).forEach(function(n){KA(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Ys(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function KA(e,t,r){return(t=zA(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function zA(e){var t=FA(e,"string");return typeof t=="symbol"?t:t+""}function FA(e,t){if(typeof e!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t||"default");if(typeof n!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var WA=()=>$(Oe),UA=()=>{var e=WA(),t=$(kt),r=$(Ep);return nn(Hs(Hs({},e),{},{scale:r}),t)},qA=()=>$(tl),Cl=(e,t)=>t,kp=(e,t,r)=>r,kl=(e,t,r,n)=>n,YA=S(kt,e=>Ui(e,t=>t.coordinate)),Ml=S([zr,Cl,kp,kl],bp),Mp=S([Ml,yr],Sl),HA=(e,t,r)=>{if(t!=null){var n=zr(e);return t==="axis"?r==="hover"?n.axisInteraction.hover.dataKey:n.axisInteraction.click.dataKey:r==="hover"?n.itemInteraction.hover.dataKey:n.itemInteraction.click.dataKey}},Dp=S([zr,Cl,kp,kl],xp),yi=S([_t,Tt,Y,ve,kt,kl,Dp,_n],wp),GA=S([Ml,yi],(e,t)=>{var r;return(r=e.coordinate)!==null&&r!==void 0?r:t}),Ip=S(kt,Mp,hp),VA=S([Dp,Mp,pr,Oe,Ip,_n,Cl],Pp),XA=S([Ml],e=>({isActive:e.active,activeIndex:e.index})),ZA=(e,t,r,n,i,a,o,u)=>{if(!(!e||!t||!n||!i||!a)){var c=Ab(e.chartX,e.chartY,t,r,u);if(c){var s=Eb(c,t),l=db(s,o,a,n,i),f=Sb(t,a,l,c);return{activeIndex:String(l),activeCoordinate:f}}}};function Mo(){return Mo=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Mo.apply(null,arguments)}function Gs(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function zn(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Gs(Object(r),!0).forEach(function(n){JA(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Gs(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function JA(e,t,r){return(t=QA(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function QA(e){var t=eS(e,"string");return typeof t=="symbol"?t:t+""}function eS(e,t){if(typeof e!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t||"default");if(typeof n!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function tS(e){var{coordinate:t,payload:r,index:n,offset:i,tooltipAxisBandSize:a,layout:o,cursor:u,tooltipEventType:c,chartName:s}=e,l=t,f=r,d=n;if(!u||!l||s!=="ScatterChart"&&c!=="axis")return null;var v,h;if(s==="ScatterChart")v=l,h=Ow;else if(s==="BarChart")v=Aw(o,l,i,a),h=Dv;else if(o==="radial"){var{cx:y,cy:m,radius:g,startAngle:b,endAngle:w}=Iv(l);v={cx:y,cy:m,startAngle:b,endAngle:w,innerRadius:g,outerRadius:g},h=Nv}else v={points:ax(o,l,i)},h=Eu;var P=typeof u=="object"&&"className"in u?u.className:void 0,x=zn(zn(zn(zn({stroke:"#ccc",pointerEvents:"none"},i),v),z(u,!1)),{},{payload:f,payloadIndex:d,className:U("recharts-tooltip-cursor",P)});return p.isValidElement(u)?p.cloneElement(u,x):p.createElement(h,x)}function rS(e){var t=UA(),r=xv(),n=ea(),i=qA();return p.createElement(tS,Mo({},e,{coordinate:e.coordinate,index:e.index,payload:e.payload,offset:r,layout:n,tooltipAxisBandSize:t,chartName:i}))}var $p=p.createContext(null),nS=()=>p.useContext($p),Np={exports:{}};(function(e){var t=Object.prototype.hasOwnProperty,r="~";function n(){}Object.create&&(n.prototype=Object.create(null),new n().__proto__||(r=!1));function i(c,s,l){this.fn=c,this.context=s,this.once=l||!1}function a(c,s,l,f,d){if(typeof l!="function")throw new TypeError("The listener must be a function");var v=new i(l,f||c,d),h=r?r+s:s;return c._events[h]?c._events[h].fn?c._events[h]=[c._events[h],v]:c._events[h].push(v):(c._events[h]=v,c._eventsCount++),c}function o(c,s){--c._eventsCount===0?c._events=new n:delete c._events[s]}function u(){this._events=new n,this._eventsCount=0}u.prototype.eventNames=function(){var s=[],l,f;if(this._eventsCount===0)return s;for(f in l=this._events)t.call(l,f)&&s.push(r?f.slice(1):f);return Object.getOwnPropertySymbols?s.concat(Object.getOwnPropertySymbols(l)):s},u.prototype.listeners=function(s){var l=r?r+s:s,f=this._events[l];if(!f)return[];if(f.fn)return[f.fn];for(var d=0,v=f.length,h=new Array(v);d<v;d++)h[d]=f[d].fn;return h},u.prototype.listenerCount=function(s){var l=r?r+s:s,f=this._events[l];return f?f.fn?1:f.length:0},u.prototype.emit=function(s,l,f,d,v,h){var y=r?r+s:s;if(!this._events[y])return!1;var m=this._events[y],g=arguments.length,b,w;if(m.fn){switch(m.once&&this.removeListener(s,m.fn,void 0,!0),g){case 1:return m.fn.call(m.context),!0;case 2:return m.fn.call(m.context,l),!0;case 3:return m.fn.call(m.context,l,f),!0;case 4:return m.fn.call(m.context,l,f,d),!0;case 5:return m.fn.call(m.context,l,f,d,v),!0;case 6:return m.fn.call(m.context,l,f,d,v,h),!0}for(w=1,b=new Array(g-1);w<g;w++)b[w-1]=arguments[w];m.fn.apply(m.context,b)}else{var P=m.length,x;for(w=0;w<P;w++)switch(m[w].once&&this.removeListener(s,m[w].fn,void 0,!0),g){case 1:m[w].fn.call(m[w].context);break;case 2:m[w].fn.call(m[w].context,l);break;case 3:m[w].fn.call(m[w].context,l,f);break;case 4:m[w].fn.call(m[w].context,l,f,d);break;default:if(!b)for(x=1,b=new Array(g-1);x<g;x++)b[x-1]=arguments[x];m[w].fn.apply(m[w].context,b)}}return!0},u.prototype.on=function(s,l,f){return a(this,s,l,f,!1)},u.prototype.once=function(s,l,f){return a(this,s,l,f,!0)},u.prototype.removeListener=function(s,l,f,d){var v=r?r+s:s;if(!this._events[v])return this;if(!l)return o(this,v),this;var h=this._events[v];if(h.fn)h.fn===l&&(!d||h.once)&&(!f||h.context===f)&&o(this,v);else{for(var y=0,m=[],g=h.length;y<g;y++)(h[y].fn!==l||d&&!h[y].once||f&&h[y].context!==f)&&m.push(h[y]);m.length?this._events[v]=m.length===1?m[0]:m:o(this,v)}return this},u.prototype.removeAllListeners=function(s){var l;return s?(l=r?r+s:s,this._events[l]&&o(this,l)):(this._events=new n,this._eventsCount=0),this},u.prototype.off=u.prototype.removeListener,u.prototype.addListener=u.prototype.on,u.prefixed=r,u.EventEmitter=u,e.exports=u})(Np);var iS=Np.exports;const aS=Et(iS);var fn=new aS,Do="recharts.syncEvent.tooltip",Vs="recharts.syncEvent.brush";function Dl(e,t){if(t){var r=Number.parseInt(t,10);if(!We(r))return e==null?void 0:e[r]}}var oS={chartName:"",tooltipPayloadSearcher:void 0,eventEmitter:void 0,defaultTooltipEventType:"axis"},Lp=it({name:"options",initialState:oS,reducers:{createEventEmitter:e=>{e.eventEmitter==null&&(e.eventEmitter=Symbol("rechartsEventEmitter"))}}}),uS=Lp.reducer,{createEventEmitter:lS}=Lp.actions;function cS(e){return e.tooltip.syncInteraction}var sS={chartData:void 0,computedData:void 0,dataStartIndex:0,dataEndIndex:0},Rp=it({name:"chartData",initialState:sS,reducers:{setChartData(e,t){if(e.chartData=t.payload,t.payload==null){e.dataStartIndex=0,e.dataEndIndex=0;return}t.payload.length>0&&e.dataEndIndex!==t.payload.length-1&&(e.dataEndIndex=t.payload.length-1)},setComputedData(e,t){e.computedData=t.payload},setDataStartEndIndexes(e,t){var{startIndex:r,endIndex:n}=t.payload;r!=null&&(e.dataStartIndex=r),n!=null&&(e.dataEndIndex=n)}}}),{setChartData:Xs,setDataStartEndIndexes:fS,setComputedData:Fk}=Rp.actions,dS=Rp.reducer,Bp=()=>{};function vS(){var e=$(rl),t=$(nl),r=ne(),n=$(Lh),i=$(kt),a=ea(),o=xu(),u=$(c=>c.rootProps.className);p.useEffect(()=>{if(e==null)return Bp;var c=(s,l,f)=>{if(t!==f&&e===s){if(n==="index"){r(l);return}if(i!=null){var d;if(typeof n=="function"){var v={activeTooltipIndex:l.payload.index==null?void 0:Number(l.payload.index),isTooltipActive:l.payload.active,activeIndex:l.payload.index==null?void 0:Number(l.payload.index),activeLabel:l.payload.label,activeDataKey:l.payload.dataKey,activeCoordinate:l.payload.coordinate},h=n(i,v);d=i[h]}else n==="value"&&(d=i.find(O=>String(O.value)===l.payload.label));var{coordinate:y}=l.payload;if(d==null||l.payload.active===!1||y==null||o==null){r(Co({active:!1,coordinate:void 0,dataKey:void 0,index:null,label:void 0}));return}var{x:m,y:g}=y,b=Math.min(m,o.x+o.width),w=Math.min(g,o.y+o.height),P={x:a==="horizontal"?d.coordinate:b,y:a==="horizontal"?w:d.coordinate},x=Co({active:l.payload.active,coordinate:P,dataKey:l.payload.dataKey,index:String(d.index),label:l.payload.label});r(x)}}};return fn.on(Do,c),()=>{fn.off(Do,c)}},[u,r,t,e,n,i,a,o])}function hS(){var e=$(rl),t=$(nl),r=ne();p.useEffect(()=>{if(e==null)return Bp;var n=(i,a,o)=>{t!==o&&e===i&&r(fS(a))};return fn.on(Vs,n),()=>{fn.off(Vs,n)}},[r,t,e])}function pS(){var e=ne();p.useEffect(()=>{e(lS())},[e]),vS(),hS()}function mS(e,t,r,n,i,a){var o=$(d=>HA(d,e,t)),u=$(nl),c=$(rl),s=$(Lh),l=$(cS),f=l==null?void 0:l.active;p.useEffect(()=>{if(!f&&c!=null&&u!=null){var d=Co({active:a,coordinate:r,dataKey:o,index:i,label:typeof n=="number"?String(n):n});fn.emit(Do,c,d,u)}},[f,r,o,i,n,u,c,s,a])}function Zs(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Js(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Zs(Object(r),!0).forEach(function(n){yS(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Zs(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function yS(e,t,r){return(t=gS(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function gS(e){var t=bS(e,"string");return typeof t=="symbol"?t:t+""}function bS(e,t){if(typeof e!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t||"default");if(typeof n!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function wS(e){return e.dataKey}function xS(e,t){return p.isValidElement(e)?p.cloneElement(e,t):typeof e=="function"?p.createElement(e,t):p.createElement(tw,t)}var Qs=[],PS={allowEscapeViewBox:{x:!1,y:!1},animationDuration:400,animationEasing:"ease",axisId:0,contentStyle:{},cursor:!0,filterNull:!0,isAnimationActive:!dr.isSsr,itemSorter:"name",itemStyle:{},labelStyle:{},offset:10,reverseDirection:{x:!1,y:!1},separator:" : ",trigger:"hover",useTranslate3d:!1,wrapperStyle:{}};function Wk(e){var t=at(e,PS),{active:r,allowEscapeViewBox:n,animationDuration:i,animationEasing:a,content:o,filterNull:u,isAnimationActive:c,offset:s,payloadUniqBy:l,position:f,reverseDirection:d,useTranslate3d:v,wrapperStyle:h,cursor:y,shared:m,trigger:g,defaultIndex:b,portal:w,axisId:P}=t,x=ne(),O=typeof b=="number"?String(b):b;p.useEffect(()=>{x(QO({shared:m,trigger:g,axisId:P,active:r,defaultIndex:O}))},[x,m,g,P,r,O]);var A=xu(),E=Sv(),_=GO(m),{activeIndex:D,isActive:j}=$(Ge=>XA(Ge,_,g,O)),C=$(Ge=>VA(Ge,_,g,O)),N=$(Ge=>Ip(Ge,_,g,O)),R=$(Ge=>GA(Ge,_,g,O)),F=C,G=nS(),B=r??j,[fe,ae]=Fd([F,B]),De=_==="axis"?N:void 0;mS(_,g,R,De,D,B);var He=w??G;if(He==null)return null;var L=F??Qs;B||(L=Qs),u&&L.length&&(L=Cd(F.filter(Ge=>Ge.value!=null&&(Ge.hide!==!0||t.includeHidden)),l,wS));var we=L.length>0,Ht=p.createElement(lw,{allowEscapeViewBox:n,animationDuration:i,animationEasing:a,isAnimationActive:c,active:B,coordinate:R,hasPayload:we,offset:s,position:f,reverseDirection:d,useTranslate3d:v,viewBox:A,wrapperStyle:h,lastBoundingBox:fe,innerRef:ae,hasPortalFromProps:!!w},xS(o,Js(Js({},t),{},{payload:L,label:De,active:B,coordinate:R,accessibilityLayer:E})));return p.createElement(p.Fragment,null,Wf.createPortal(Ht,He),B&&p.createElement(rS,{cursor:y,tooltipEventType:_,coordinate:R,payload:F,index:D}))}var Kp={},zp={};(function(e){Object.defineProperty(e,Symbol.toStringTag,{value:"Module"});function t(r,n=0,i={}){typeof i!="object"&&(i={});let a=null,o=null,u=null,c=0,s=null,l;const{leading:f=!1,trailing:d=!0,maxWait:v}=i,h="maxWait"in i,y=h?Math.max(Number(v)||0,n):0,m=A=>(a!==null&&(l=r.apply(o,a)),a=o=null,c=A,l),g=A=>(c=A,s=setTimeout(x,n),f&&a!==null?m(A):l),b=A=>(s=null,d&&a!==null?m(A):l),w=A=>{if(u===null)return!0;const E=A-u,_=E>=n||E<0,D=h&&A-c>=y;return _||D},P=A=>{const E=u===null?0:A-u,_=n-E,D=y-(A-c);return h?Math.min(_,D):_},x=()=>{const A=Date.now();if(w(A))return b(A);s=setTimeout(x,P(A))},O=function(...A){const E=Date.now(),_=w(E);if(a=A,o=this,u=E,_){if(s===null)return g(E);if(h)return clearTimeout(s),s=setTimeout(x,n),m(E)}return s===null&&(s=setTimeout(x,n)),l};return O.cancel=()=>{s!==null&&clearTimeout(s),c=0,u=a=o=s=null},O.flush=()=>s===null?l:b(Date.now()),O}e.debounce=t})(zp);(function(e){Object.defineProperty(e,Symbol.toStringTag,{value:"Module"});const t=zp;function r(n,i=0,a={}){const{leading:o=!0,trailing:u=!0}=a;return t.debounce(n,i,{leading:o,maxWait:i,trailing:u})}e.throttle=r})(Kp);var OS=Kp.throttle;const AS=Et(OS);var Qr=function(t,r){for(var n=arguments.length,i=new Array(n>2?n-2:0),a=2;a<n;a++)i[a-2]=arguments[a]};function ef(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Fa(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?ef(Object(r),!0).forEach(function(n){SS(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):ef(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function SS(e,t,r){return(t=ES(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function ES(e){var t=_S(e,"string");return typeof t=="symbol"?t:t+""}function _S(e,t){if(typeof e!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t||"default");if(typeof n!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var Uk=p.forwardRef((e,t)=>{var{aspect:r,initialDimension:n={width:-1,height:-1},width:i="100%",height:a="100%",minWidth:o=0,minHeight:u,maxHeight:c,children:s,debounce:l=0,id:f,className:d,onResize:v,style:h={}}=e,y=p.useRef(null),m=p.useRef();m.current=v,p.useImperativeHandle(t,()=>y.current);var[g,b]=p.useState({containerWidth:n.width,containerHeight:n.height}),w=p.useCallback((x,O)=>{b(A=>{var E=Math.round(x),_=Math.round(O);return A.containerWidth===E&&A.containerHeight===_?A:{containerWidth:E,containerHeight:_}})},[]);p.useEffect(()=>{var x=_=>{var D,{width:j,height:C}=_[0].contentRect;w(j,C),(D=m.current)===null||D===void 0||D.call(m,j,C)};l>0&&(x=AS(x,l,{trailing:!0,leading:!1}));var O=new ResizeObserver(x),{width:A,height:E}=y.current.getBoundingClientRect();return w(A,E),O.observe(y.current),()=>{O.disconnect()}},[w,l]);var P=p.useMemo(()=>{var{containerWidth:x,containerHeight:O}=g;if(x<0||O<0)return null;Qr(Jt(i)||Jt(a),`The width(%s) and height(%s) are both fixed numbers,
       maybe you don't need to use a ResponsiveContainer.`,i,a),Qr(!r||r>0,"The aspect(%s) must be greater than zero.",r);var A=Jt(i)?x:i,E=Jt(a)?O:a;return r&&r>0&&(A?E=A/r:E&&(A=E*r),c&&E>c&&(E=c)),Qr(A>0||E>0,`The width(%s) and height(%s) of chart should be greater than 0,
       please check the style of container, or the props width(%s) and height(%s),
       or add a minWidth(%s) or minHeight(%s) or use aspect(%s) to control the
       height and width.`,A,E,i,a,o,u,r),p.Children.map(s,_=>p.cloneElement(_,{width:A,height:E,style:Fa({width:A,height:E},_.props.style)}))},[r,s,a,c,u,o,g,i]);return p.createElement("div",{id:f?"".concat(f):void 0,className:U("recharts-responsive-container",d),style:Fa(Fa({},h),{},{width:i,height:a,minWidth:o,minHeight:u,maxHeight:c}),ref:y},p.createElement("div",{style:{width:0,height:0,overflow:"visible"}},P))}),ya=e=>null;ya.displayName="Cell";function tf(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Io(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?tf(Object(r),!0).forEach(function(n){TS(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):tf(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function TS(e,t,r){return(t=jS(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function jS(e){var t=CS(e,"string");return typeof t=="symbol"?t:t+""}function CS(e,t){if(typeof e!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t||"default");if(typeof n!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var br={widthCache:{},cacheCount:0},kS=2e3,MS={position:"absolute",top:"-20000px",left:0,padding:0,margin:0,border:"none",whiteSpace:"pre"},rf="recharts_measurement_span";function DS(e){var t=Io({},e);return Object.keys(t).forEach(r=>{t[r]||delete t[r]}),t}var en=function(t){var r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};if(t==null||dr.isSsr)return{width:0,height:0};var n=DS(r),i=JSON.stringify({text:t,copyStyle:n});if(br.widthCache[i])return br.widthCache[i];try{var a=document.getElementById(rf);a||(a=document.createElement("span"),a.setAttribute("id",rf),a.setAttribute("aria-hidden","true"),document.body.appendChild(a));var o=Io(Io({},MS),n);Object.assign(a.style,o),a.textContent="".concat(t);var u=a.getBoundingClientRect(),c={width:u.width,height:u.height};return br.widthCache[i]=c,++br.cacheCount>kS&&(br.cacheCount=0,br.widthCache={}),c}catch{return{width:0,height:0}}},nf=/(-?\d+(?:\.\d+)?[a-zA-Z%]*)([*/])(-?\d+(?:\.\d+)?[a-zA-Z%]*)/,af=/(-?\d+(?:\.\d+)?[a-zA-Z%]*)([+-])(-?\d+(?:\.\d+)?[a-zA-Z%]*)/,IS=/^px|cm|vh|vw|em|rem|%|mm|in|pt|pc|ex|ch|vmin|vmax|Q$/,$S=/(-?\d+(?:\.\d+)?)([a-zA-Z%]+)?/,Fp={cm:96/2.54,mm:96/25.4,pt:96/72,pc:96/6,in:96,Q:96/(2.54*40),px:1},NS=Object.keys(Fp),wr="NaN";function LS(e,t){return e*Fp[t]}class ke{static parse(t){var r,[,n,i]=(r=$S.exec(t))!==null&&r!==void 0?r:[];return new ke(parseFloat(n),i??"")}constructor(t,r){this.num=t,this.unit=r,this.num=t,this.unit=r,We(t)&&(this.unit=""),r!==""&&!IS.test(r)&&(this.num=NaN,this.unit=""),NS.includes(r)&&(this.num=LS(t,r),this.unit="px")}add(t){return this.unit!==t.unit?new ke(NaN,""):new ke(this.num+t.num,this.unit)}subtract(t){return this.unit!==t.unit?new ke(NaN,""):new ke(this.num-t.num,this.unit)}multiply(t){return this.unit!==""&&t.unit!==""&&this.unit!==t.unit?new ke(NaN,""):new ke(this.num*t.num,this.unit||t.unit)}divide(t){return this.unit!==""&&t.unit!==""&&this.unit!==t.unit?new ke(NaN,""):new ke(this.num/t.num,this.unit||t.unit)}toString(){return"".concat(this.num).concat(this.unit)}isNaN(){return We(this.num)}}function Wp(e){if(e.includes(wr))return wr;for(var t=e;t.includes("*")||t.includes("/");){var r,[,n,i,a]=(r=nf.exec(t))!==null&&r!==void 0?r:[],o=ke.parse(n??""),u=ke.parse(a??""),c=i==="*"?o.multiply(u):o.divide(u);if(c.isNaN())return wr;t=t.replace(nf,c.toString())}for(;t.includes("+")||/.-\d+(?:\.\d+)?/.test(t);){var s,[,l,f,d]=(s=af.exec(t))!==null&&s!==void 0?s:[],v=ke.parse(l??""),h=ke.parse(d??""),y=f==="+"?v.add(h):v.subtract(h);if(y.isNaN())return wr;t=t.replace(af,y.toString())}return t}var of=/\(([^()]*)\)/;function RS(e){for(var t=e,r;(r=of.exec(t))!=null;){var[,n]=r;t=t.replace(of,Wp(n))}return t}function BS(e){var t=e.replace(/\s+/g,"");return t=RS(t),t=Wp(t),t}function KS(e){try{return BS(e)}catch{return wr}}function Wa(e){var t=KS(e.slice(5,-1));return t===wr?"":t}var zS=["x","y","lineHeight","capHeight","scaleToFit","textAnchor","verticalAnchor","fill"],FS=["dx","dy","angle","className","breakAll"];function $o(){return $o=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},$o.apply(null,arguments)}function uf(e,t){if(e==null)return{};var r,n,i=WS(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],t.indexOf(r)===-1&&{}.propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}function WS(e,t){if(e==null)return{};var r={};for(var n in e)if({}.hasOwnProperty.call(e,n)){if(t.indexOf(n)!==-1)continue;r[n]=e[n]}return r}var Up=/[ \f\n\r\t\v\u2028\u2029]+/,qp=e=>{var{children:t,breakAll:r,style:n}=e;try{var i=[];X(t)||(r?i=t.toString().split(""):i=t.toString().split(Up));var a=i.map(u=>({word:u,width:en(u,n).width})),o=r?0:en(" ",n).width;return{wordsWithComputedWidth:a,spaceWidth:o}}catch{return null}},US=(e,t,r,n,i)=>{var{maxLines:a,children:o,style:u,breakAll:c}=e,s=k(a),l=o,f=function(){var j=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[];return j.reduce((C,N)=>{var{word:R,width:F}=N,G=C[C.length-1];if(G&&(n==null||i||G.width+F+r<Number(n)))G.words.push(R),G.width+=F+r;else{var B={words:[R],width:F};C.push(B)}return C},[])},d=f(t),v=D=>D.reduce((j,C)=>j.width>C.width?j:C);if(!s||i)return d;var h=d.length>a||v(d).width>Number(n);if(!h)return d;for(var y="…",m=D=>{var j=l.slice(0,D),C=qp({breakAll:c,style:u,children:j+y}).wordsWithComputedWidth,N=f(C),R=N.length>a||v(N).width>Number(n);return[R,N]},g=0,b=l.length-1,w=0,P;g<=b&&w<=l.length-1;){var x=Math.floor((g+b)/2),O=x-1,[A,E]=m(O),[_]=m(x);if(!A&&!_&&(g=x+1),A&&_&&(b=x-1),!A&&_){P=E;break}w++}return P||d},lf=e=>{var t=X(e)?[]:e.toString().split(Up);return[{words:t}]},qS=e=>{var{width:t,scaleToFit:r,children:n,style:i,breakAll:a,maxLines:o}=e;if((t||r)&&!dr.isSsr){var u,c,s=qp({breakAll:a,children:n,style:i});if(s){var{wordsWithComputedWidth:l,spaceWidth:f}=s;u=l,c=f}else return lf(n);return US({breakAll:a,children:n,maxLines:o,style:i},u,c,t,r)}return lf(n)},cf="#808080",ga=p.forwardRef((e,t)=>{var{x:r=0,y:n=0,lineHeight:i="1em",capHeight:a="0.71em",scaleToFit:o=!1,textAnchor:u="start",verticalAnchor:c="end",fill:s=cf}=e,l=uf(e,zS),f=p.useMemo(()=>qS({breakAll:l.breakAll,children:l.children,maxLines:l.maxLines,scaleToFit:o,style:l.style,width:l.width}),[l.breakAll,l.children,l.maxLines,o,l.style,l.width]),{dx:d,dy:v,angle:h,className:y,breakAll:m}=l,g=uf(l,FS);if(!vt(r)||!vt(n))return null;var b=r+(k(d)?d:0),w=n+(k(v)?v:0),P;switch(c){case"start":P=Wa("calc(".concat(a,")"));break;case"middle":P=Wa("calc(".concat((f.length-1)/2," * -").concat(i," + (").concat(a," / 2))"));break;default:P=Wa("calc(".concat(f.length-1," * -").concat(i,")"));break}var x=[];if(o){var O=f[0].width,{width:A}=l;x.push("scale(".concat(k(A)?A/O:1,")"))}return h&&x.push("rotate(".concat(h,", ").concat(b,", ").concat(w,")")),x.length&&(g.transform=x.join(" ")),p.createElement("text",$o({},z(g,!0),{ref:t,x:b,y:w,className:U("recharts-text",y),textAnchor:u,fill:s.includes("url")?cf:s}),f.map((E,_)=>{var D=E.words.join(m?"":" ");return p.createElement("tspan",{x:b,dy:_===0?P:i,key:"".concat(D,"-").concat(_)},D)}))});ga.displayName="Text";var YS=["offset"],HS=["labelRef"];function sf(e,t){if(e==null)return{};var r,n,i=GS(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],t.indexOf(r)===-1&&{}.propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}function GS(e,t){if(e==null)return{};var r={};for(var n in e)if({}.hasOwnProperty.call(e,n)){if(t.indexOf(n)!==-1)continue;r[n]=e[n]}return r}function ff(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function le(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?ff(Object(r),!0).forEach(function(n){VS(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):ff(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function VS(e,t,r){return(t=XS(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function XS(e){var t=ZS(e,"string");return typeof t=="symbol"?t:t+""}function ZS(e,t){if(typeof e!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t||"default");if(typeof n!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function yt(){return yt=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},yt.apply(null,arguments)}var JS=e=>{var{value:t,formatter:r}=e,n=X(e.children)?t:e.children;return typeof r=="function"?r(n):n},Il=e=>e!=null&&typeof e=="function",QS=(e,t)=>{var r=me(t-e),n=Math.min(Math.abs(t-e),360);return r*n},eE=(e,t,r)=>{var{position:n,viewBox:i,offset:a,className:o}=e,{cx:u,cy:c,innerRadius:s,outerRadius:l,startAngle:f,endAngle:d,clockWise:v}=i,h=(s+l)/2,y=QS(f,d),m=y>=0?1:-1,g,b;n==="insideStart"?(g=f+m*a,b=v):n==="insideEnd"?(g=d-m*a,b=!v):n==="end"&&(g=d+m*a,b=v),b=y<=0?b:!b;var w=ce(u,c,h,g),P=ce(u,c,h,g+(b?1:-1)*359),x="M".concat(w.x,",").concat(w.y,`
    A`).concat(h,",").concat(h,",0,1,").concat(b?0:1,`,
    `).concat(P.x,",").concat(P.y),O=X(e.id)?ur("recharts-radial-line-"):e.id;return p.createElement("text",yt({},r,{dominantBaseline:"central",className:U("recharts-radial-bar-label",o)}),p.createElement("defs",null,p.createElement("path",{id:O,d:x})),p.createElement("textPath",{xlinkHref:"#".concat(O)},t))},tE=e=>{var{viewBox:t,offset:r,position:n}=e,{cx:i,cy:a,innerRadius:o,outerRadius:u,startAngle:c,endAngle:s}=t,l=(c+s)/2;if(n==="outside"){var{x:f,y:d}=ce(i,a,u+r,l);return{x:f,y:d,textAnchor:f>=i?"start":"end",verticalAnchor:"middle"}}if(n==="center")return{x:i,y:a,textAnchor:"middle",verticalAnchor:"middle"};if(n==="centerTop")return{x:i,y:a,textAnchor:"middle",verticalAnchor:"start"};if(n==="centerBottom")return{x:i,y:a,textAnchor:"middle",verticalAnchor:"end"};var v=(o+u)/2,{x:h,y}=ce(i,a,v,l);return{x:h,y,textAnchor:"middle",verticalAnchor:"middle"}},rE=(e,t)=>{var{parentViewBox:r,offset:n,position:i}=e,{x:a,y:o,width:u,height:c}=t,s=c>=0?1:-1,l=s*n,f=s>0?"end":"start",d=s>0?"start":"end",v=u>=0?1:-1,h=v*n,y=v>0?"end":"start",m=v>0?"start":"end";if(i==="top"){var g={x:a+u/2,y:o-s*n,textAnchor:"middle",verticalAnchor:f};return le(le({},g),r?{height:Math.max(o-r.y,0),width:u}:{})}if(i==="bottom"){var b={x:a+u/2,y:o+c+l,textAnchor:"middle",verticalAnchor:d};return le(le({},b),r?{height:Math.max(r.y+r.height-(o+c),0),width:u}:{})}if(i==="left"){var w={x:a-h,y:o+c/2,textAnchor:y,verticalAnchor:"middle"};return le(le({},w),r?{width:Math.max(w.x-r.x,0),height:c}:{})}if(i==="right"){var P={x:a+u+h,y:o+c/2,textAnchor:m,verticalAnchor:"middle"};return le(le({},P),r?{width:Math.max(r.x+r.width-P.x,0),height:c}:{})}var x=r?{width:u,height:c}:{};return i==="insideLeft"?le({x:a+h,y:o+c/2,textAnchor:m,verticalAnchor:"middle"},x):i==="insideRight"?le({x:a+u-h,y:o+c/2,textAnchor:y,verticalAnchor:"middle"},x):i==="insideTop"?le({x:a+u/2,y:o+l,textAnchor:"middle",verticalAnchor:d},x):i==="insideBottom"?le({x:a+u/2,y:o+c-l,textAnchor:"middle",verticalAnchor:f},x):i==="insideTopLeft"?le({x:a+h,y:o+l,textAnchor:m,verticalAnchor:d},x):i==="insideTopRight"?le({x:a+u-h,y:o+l,textAnchor:y,verticalAnchor:d},x):i==="insideBottomLeft"?le({x:a+h,y:o+c-l,textAnchor:m,verticalAnchor:f},x):i==="insideBottomRight"?le({x:a+u-h,y:o+c-l,textAnchor:y,verticalAnchor:f},x):i&&typeof i=="object"&&(k(i.x)||Jt(i.x))&&(k(i.y)||Jt(i.y))?le({x:a+Te(i.x,u),y:o+Te(i.y,c),textAnchor:"end",verticalAnchor:"end"},x):le({x:a+u/2,y:o+c/2,textAnchor:"middle",verticalAnchor:"middle"},x)},nE=e=>"cx"in e&&k(e.cx);function Ke(e){var{offset:t=5}=e,r=sf(e,YS),n=le({offset:t},r),{viewBox:i,position:a,value:o,children:u,content:c,className:s="",textBreakAll:l,labelRef:f}=n,d=xu(),v=i||d;if(!v||X(o)&&X(u)&&!p.isValidElement(c)&&typeof c!="function")return null;if(p.isValidElement(c)){var h=sf(n,HS);return p.cloneElement(c,h)}var y;if(typeof c=="function"){if(y=p.createElement(c,n),p.isValidElement(y))return y}else y=JS(n);var m=nE(v),g=z(n,!0);if(m&&(a==="insideStart"||a==="insideEnd"||a==="end"))return eE(n,y,g);var b=m?tE(n):rE(n,v);return p.createElement(ga,yt({ref:f,className:U("recharts-label",s)},g,b,{breakAll:l}),y)}Ke.displayName="Label";var Yp=e=>{var{cx:t,cy:r,angle:n,startAngle:i,endAngle:a,r:o,radius:u,innerRadius:c,outerRadius:s,x:l,y:f,top:d,left:v,width:h,height:y,clockWise:m,labelViewBox:g}=e;if(g)return g;if(k(h)&&k(y)){if(k(l)&&k(f))return{x:l,y:f,width:h,height:y};if(k(d)&&k(v))return{x:d,y:v,width:h,height:y}}if(k(l)&&k(f))return{x:l,y:f,width:0,height:0};if(k(t)&&k(r))return{cx:t,cy:r,startAngle:i||n||0,endAngle:a||n||0,innerRadius:c||0,outerRadius:s||u||o||0,clockWise:m};if(e.viewBox)return e.viewBox},iE=(e,t,r)=>{if(!e)return null;var n={viewBox:t,labelRef:r};return e===!0?p.createElement(Ke,yt({key:"label-implicit"},n)):vt(e)?p.createElement(Ke,yt({key:"label-implicit",value:e},n)):p.isValidElement(e)?e.type===Ke?p.cloneElement(e,le({key:"label-implicit"},n)):p.createElement(Ke,yt({key:"label-implicit",content:e},n)):Il(e)?p.createElement(Ke,yt({key:"label-implicit",content:e},n)):e&&typeof e=="object"?p.createElement(Ke,yt({},e,{key:"label-implicit"},n)):null},aE=function(t,r){var n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!0;if(!t||!t.children&&n&&!t.label)return null;var{children:i,labelRef:a}=t,o=Yp(t),u=mn(i,Ke).map((s,l)=>p.cloneElement(s,{viewBox:r||o,key:"label-".concat(l)}));if(!n)return u;var c=iE(t.label,r||o,a);return[c,...u]};Ke.parseViewBox=Yp;Ke.renderCallByParent=aE;var Hp={},Gp={};(function(e){Object.defineProperty(e,Symbol.toStringTag,{value:"Module"});function t(r){return r[r.length-1]}e.last=t})(Gp);var Vp={};(function(e){Object.defineProperty(e,Symbol.toStringTag,{value:"Module"});function t(r){return Array.isArray(r)?r:Array.from(r)}e.toArray=t})(Vp);(function(e){Object.defineProperty(e,Symbol.toStringTag,{value:"Module"});const t=Gp,r=Vp,n=Bi;function i(a){if(n.isArrayLike(a))return t.last(r.toArray(a))}e.last=i})(Hp);var oE=Hp.last;const uE=Et(oE);var lE=["valueAccessor"],cE=["data","dataKey","clockWise","id","textBreakAll"];function gi(){return gi=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},gi.apply(null,arguments)}function df(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function vf(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?df(Object(r),!0).forEach(function(n){sE(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):df(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function sE(e,t,r){return(t=fE(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function fE(e){var t=dE(e,"string");return typeof t=="symbol"?t:t+""}function dE(e,t){if(typeof e!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t||"default");if(typeof n!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function hf(e,t){if(e==null)return{};var r,n,i=vE(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],t.indexOf(r)===-1&&{}.propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}function vE(e,t){if(e==null)return{};var r={};for(var n in e)if({}.hasOwnProperty.call(e,n)){if(t.indexOf(n)!==-1)continue;r[n]=e[n]}return r}var hE=e=>Array.isArray(e.value)?uE(e.value):e.value;function Lt(e){var{valueAccessor:t=hE}=e,r=hf(e,lE),{data:n,dataKey:i,clockWise:a,id:o,textBreakAll:u}=r,c=hf(r,cE);return!n||!n.length?null:p.createElement(ye,{className:"recharts-label-list"},n.map((s,l)=>{var f=X(i)?t(s,l):te(s&&s.payload,i),d=X(o)?{}:{id:"".concat(o,"-").concat(l)};return p.createElement(Ke,gi({},z(s,!0),c,d,{parentViewBox:s.parentViewBox,value:f,textBreakAll:u,viewBox:Ke.parseViewBox(X(a)?s:vf(vf({},s),{},{clockWise:a})),key:"label-".concat(l),index:l}))}))}Lt.displayName="LabelList";function pE(e,t){return e?e===!0?p.createElement(Lt,{key:"labelList-implicit",data:t}):p.isValidElement(e)||Il(e)?p.createElement(Lt,{key:"labelList-implicit",data:t,content:e}):typeof e=="object"?p.createElement(Lt,gi({data:t},e,{key:"labelList-implicit"})):null:null}function mE(e,t){var r=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!0;if(!e||!e.children&&r&&!e.label)return null;var{children:n}=e,i=mn(n,Lt).map((o,u)=>p.cloneElement(o,{data:t,key:"labelList-".concat(u)}));if(!r)return i;var a=pE(e.label,t);return[a,...i]}Lt.renderCallByParent=mE;function No(){return No=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},No.apply(null,arguments)}var Xp=e=>{var{cx:t,cy:r,r:n,className:i}=e,a=U("recharts-dot",i);return t===+t&&r===+r&&n===+n?p.createElement("circle",No({},z(e,!1),Xo(e),{className:a,cx:t,cy:r,r:n})):null},Zp=e=>e.graphicalItems.polarItems,yE=S([oe,On],ll),$l=S([Zp,ue,yE],cl),gE=S([$l],sl),Nl=S([gE,Zu],fl),bE=S([Nl,ue,$l],dl),wE=S([Nl,ue,$l],(e,t,r)=>r.length>0?e.flatMap(n=>r.flatMap(i=>{var a,o=te(n,(a=t.dataKey)!==null&&a!==void 0?a:i.dataKey);return{value:o,errorDomain:[]}})).filter(Boolean):(t==null?void 0:t.dataKey)!=null?e.map(n=>({value:te(n,t.dataKey),errorDomain:[]})):e.map(n=>({value:n,errorDomain:[]}))),pf=()=>{},xE=S([ue,ep,pf,wE,pf],pl),Jp=S([ue,Y,Nl,bE,Pn,oe,xE],ml),PE=S([Jp,ue,Br],bl);S([ue,Jp,PE,oe],xl);var OE={radiusAxis:{},angleAxis:{}},Qp=it({name:"polarAxis",initialState:OE,reducers:{addRadiusAxis(e,t){e.radiusAxis[t.payload.id]=t.payload},removeRadiusAxis(e,t){delete e.radiusAxis[t.payload.id]},addAngleAxis(e,t){e.angleAxis[t.payload.id]=t.payload},removeAngleAxis(e,t){delete e.angleAxis[t.payload.id]}}});Qp.actions;var AE=Qp.reducer;function mf(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function yf(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?mf(Object(r),!0).forEach(function(n){SE(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):mf(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function SE(e,t,r){return(t=EE(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function EE(e){var t=_E(e,"string");return typeof t=="symbol"?t:t+""}function _E(e,t){if(typeof e!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t||"default");if(typeof n!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var Ll=(e,t)=>t,TE=[],Rl=(e,t,r)=>(r==null?void 0:r.length)===0?TE:r,em=S([Zu,Ll,Rl],(e,t,r)=>{var{chartData:n}=e,i;if((t==null?void 0:t.data)!=null&&t.data.length>0?i=t.data:i=n,(!i||!i.length)&&r!=null&&(i=r.map(a=>yf(yf({},t.presentationProps),a.props))),i!=null)return i}),jE=S([em,Ll,Rl],(e,t,r)=>{if(e!=null)return e.map((n,i)=>{var a,o=te(n,t.nameKey,t.name),u;return r!=null&&(a=r[i])!==null&&a!==void 0&&(a=a.props)!==null&&a!==void 0&&a.fill?u=r[i].props.fill:typeof n=="object"&&n!=null&&"fill"in n?u=n.fill:u=t.fill,{value:Ir(o,t.dataKey),color:u,payload:n,type:t.legendType}})}),CE=S([Zp,Ll],(e,t)=>{if(e.some(r=>r.type==="pie"&&t.dataKey===r.dataKey&&t.data===r.data))return t}),kE=S([em,CE,Rl,ve],(e,t,r,n)=>{if(!(t==null||e==null))return w_({offset:n,pieSettings:t,displayedData:e,cells:r})}),ME={countOfBars:0,cartesianItems:[],polarItems:[]},tm=it({name:"graphicalItems",initialState:ME,reducers:{addBar(e){e.countOfBars+=1},removeBar(e){e.countOfBars-=1},addCartesianGraphicalItem(e,t){e.cartesianItems.push(t.payload)},replaceCartesianGraphicalItem(e,t){var{prev:r,next:n}=t.payload,i=wt(e).cartesianItems.indexOf(r);i>-1&&(e.cartesianItems[i]=n)},removeCartesianGraphicalItem(e,t){var r=wt(e).cartesianItems.indexOf(t.payload);r>-1&&e.cartesianItems.splice(r,1)},addPolarGraphicalItem(e,t){e.polarItems.push(t.payload)},removePolarGraphicalItem(e,t){var r=wt(e).polarItems.indexOf(t.payload);r>-1&&e.polarItems.splice(r,1)}}}),{addBar:DE,removeBar:IE,addCartesianGraphicalItem:$E,replaceCartesianGraphicalItem:NE,removeCartesianGraphicalItem:LE,addPolarGraphicalItem:RE,removePolarGraphicalItem:BE}=tm.actions,KE=tm.reducer;function gf(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function bf(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?gf(Object(r),!0).forEach(function(n){zE(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):gf(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function zE(e,t,r){return(t=FE(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function FE(e){var t=WE(e,"string");return typeof t=="symbol"?t:t+""}function WE(e,t){if(typeof e!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t||"default");if(typeof n!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function UE(e){var t=ne(),r=p.useRef(null);return p.useEffect(()=>{var n=bf(bf({},e),{},{stackId:mv(e.stackId)});r.current===null?t($E(n)):r.current!==n&&t(NE({prev:r.current,next:n})),r.current=n},[t,e]),p.useEffect(()=>()=>{r.current&&(t(LE(r.current)),r.current=null)},[t]),null}function qE(e){var t=ne();return p.useEffect(()=>(t(RE(e)),()=>{t(BE(e))}),[t,e]),null}var rm={};(function(e){Object.defineProperty(e,Symbol.toStringTag,{value:"Module"});function t(r){var i;if(typeof r!="object"||r==null)return!1;if(Object.getPrototypeOf(r)===null)return!0;if(Object.prototype.toString.call(r)!=="[object Object]"){const a=r[Symbol.toStringTag];return a==null||!((i=Object.getOwnPropertyDescriptor(r,Symbol.toStringTag))!=null&&i.writable)?!1:r.toString()===`[object ${a}]`}let n=r;for(;Object.getPrototypeOf(n)!==null;)n=Object.getPrototypeOf(n);return Object.getPrototypeOf(r)===n}e.isPlainObject=t})(rm);var YE=rm.isPlainObject;const HE=Et(YE);function bi(){return bi=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},bi.apply(null,arguments)}var wf=(e,t,r,n,i)=>{var a=r-n,o;return o="M ".concat(e,",").concat(t),o+="L ".concat(e+r,",").concat(t),o+="L ".concat(e+r-a/2,",").concat(t+i),o+="L ".concat(e+r-a/2-n,",").concat(t+i),o+="L ".concat(e,",").concat(t," Z"),o},GE={x:0,y:0,upperWidth:0,lowerWidth:0,height:0,isUpdateAnimationActive:!1,animationBegin:0,animationDuration:1500,animationEasing:"ease"},VE=e=>{var t=at(e,GE),r=p.useRef(),[n,i]=p.useState(-1);p.useEffect(()=>{if(r.current&&r.current.getTotalLength)try{var m=r.current.getTotalLength();m&&i(m)}catch{}},[]);var{x:a,y:o,upperWidth:u,lowerWidth:c,height:s,className:l}=t,{animationEasing:f,animationDuration:d,animationBegin:v,isUpdateAnimationActive:h}=t;if(a!==+a||o!==+o||u!==+u||c!==+c||s!==+s||u===0&&c===0||s===0)return null;var y=U("recharts-trapezoid",l);return h?p.createElement(Bt,{canBegin:n>0,from:{upperWidth:0,lowerWidth:0,height:s,x:a,y:o},to:{upperWidth:u,lowerWidth:c,height:s,x:a,y:o},duration:d,animationEasing:f,isActive:h},m=>{var{upperWidth:g,lowerWidth:b,height:w,x:P,y:x}=m;return p.createElement(Bt,{canBegin:n>0,from:"0px ".concat(n===-1?1:n,"px"),to:"".concat(n,"px 0px"),attributeName:"strokeDasharray",begin:v,duration:d,easing:f},p.createElement("path",bi({},z(t,!0),{className:y,d:wf(P,x,g,b,w),ref:r})))}):p.createElement("g",null,p.createElement("path",bi({},z(t,!0),{className:y,d:wf(a,o,u,c,s)})))},XE=["option","shapeType","propTransformer","activeClassName","isActive"];function ZE(e,t){if(e==null)return{};var r,n,i=JE(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],t.indexOf(r)===-1&&{}.propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}function JE(e,t){if(e==null)return{};var r={};for(var n in e)if({}.hasOwnProperty.call(e,n)){if(t.indexOf(n)!==-1)continue;r[n]=e[n]}return r}function xf(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function wi(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?xf(Object(r),!0).forEach(function(n){QE(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):xf(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function QE(e,t,r){return(t=e_(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function e_(e){var t=t_(e,"string");return typeof t=="symbol"?t:t+""}function t_(e,t){if(typeof e!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t||"default");if(typeof n!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function r_(e,t){return wi(wi({},t),e)}function n_(e,t){return e==="symbols"}function Pf(e){var{shapeType:t,elementProps:r}=e;switch(t){case"rectangle":return p.createElement(Dv,r);case"trapezoid":return p.createElement(VE,r);case"sector":return p.createElement(Nv,r);case"symbols":if(n_(t))return p.createElement(tu,r);break;default:return null}}function i_(e){return p.isValidElement(e)?e.props:e}function nm(e){var{option:t,shapeType:r,propTransformer:n=r_,activeClassName:i="recharts-active-shape",isActive:a}=e,o=ZE(e,XE),u;if(p.isValidElement(t))u=p.cloneElement(t,wi(wi({},o),i_(t)));else if(typeof t=="function")u=t(o);else if(HE(t)&&typeof t!="boolean"){var c=n(t,o);u=p.createElement(Pf,{shapeType:r,elementProps:c})}else{var s=o;u=p.createElement(Pf,{shapeType:r,elementProps:s})}return a?p.createElement(ye,{className:i},u):u}var Bl=(e,t)=>{var r=ne();return(n,i)=>a=>{e==null||e(n,i,a),r(mp({activeIndex:String(i),activeDataKey:t,activeCoordinate:n.tooltipPosition}))}},Kl=e=>{var t=ne();return(r,n)=>i=>{e==null||e(r,n,i),t(eA())}},zl=(e,t)=>{var r=ne();return(n,i)=>a=>{e==null||e(n,i,a),r(tA({activeIndex:String(i),activeDataKey:t,activeCoordinate:n.tooltipPosition}))}};function Fl(e){var{fn:t,args:r}=e,n=ne(),i=je();return p.useEffect(()=>{if(!i){var a=t(r);return n(ZO(a)),()=>{n(JO(a))}}},[t,r,n,i]),null}var im=()=>{};function am(e){var{legendPayload:t}=e,r=ne(),n=je();return p.useEffect(()=>n?im:(r(Ov(t)),()=>{r(Av(t))}),[r,n,t]),null}function a_(e){var{legendPayload:t}=e,r=ne(),n=$(Y);return p.useEffect(()=>n!=="centric"&&n!=="radial"?im:(r(Ov(t)),()=>{r(Av(t))}),[r,n,t]),null}function Wl(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:"animation-",r=p.useRef(ur(t)),n=p.useRef(e);return n.current!==e&&(r.current=ur(t),n.current=e),r.current}var o_=["onMouseEnter","onClick","onMouseLeave"];function u_(e,t){if(e==null)return{};var r,n,i=l_(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],t.indexOf(r)===-1&&{}.propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}function l_(e,t){if(e==null)return{};var r={};for(var n in e)if({}.hasOwnProperty.call(e,n)){if(t.indexOf(n)!==-1)continue;r[n]=e[n]}return r}function Of(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function ie(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Of(Object(r),!0).forEach(function(n){ba(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Of(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function ba(e,t,r){return(t=c_(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function c_(e){var t=s_(e,"string");return typeof t=="symbol"?t:t+""}function s_(e,t){if(typeof e!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t||"default");if(typeof n!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function fr(){return fr=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},fr.apply(null,arguments)}function f_(e){var t=p.useMemo(()=>z(e,!1),[e]),r=p.useMemo(()=>mn(e.children,ya),[e.children]),n=p.useMemo(()=>({name:e.name,nameKey:e.nameKey,tooltipType:e.tooltipType,data:e.data,dataKey:e.dataKey,cx:e.cx,cy:e.cy,startAngle:e.startAngle,endAngle:e.endAngle,minAngle:e.minAngle,paddingAngle:e.paddingAngle,innerRadius:e.innerRadius,outerRadius:e.outerRadius,cornerRadius:e.cornerRadius,legendType:e.legendType,fill:e.fill,presentationProps:t}),[e.cornerRadius,e.cx,e.cy,e.data,e.dataKey,e.endAngle,e.innerRadius,e.minAngle,e.name,e.nameKey,e.outerRadius,e.paddingAngle,e.startAngle,e.tooltipType,e.legendType,e.fill,t]),i=$(a=>jE(a,n,r));return p.createElement(a_,{legendPayload:i})}function d_(e){var{dataKey:t,nameKey:r,sectors:n,stroke:i,strokeWidth:a,fill:o,name:u,hide:c,tooltipType:s}=e;return{dataDefinedOnItem:n==null?void 0:n.map(l=>l.tooltipPayload),positions:n==null?void 0:n.map(l=>l.tooltipPosition),settings:{stroke:i,strokeWidth:a,fill:o,dataKey:t,nameKey:r,name:Ir(u,t),hide:c,type:s,color:o,unit:""}}}var v_=(e,t)=>e>t?"start":e<t?"end":"middle",h_=(e,t,r)=>typeof t=="function"?t(e):Te(t,r,r*.8),p_=(e,t,r)=>{var{top:n,left:i,width:a,height:o}=t,u=vv(a,o),c=i+Te(e.cx,a,a/2),s=n+Te(e.cy,o,o/2),l=Te(e.innerRadius,u,0),f=h_(r,e.outerRadius,u),d=e.maxRadius||Math.sqrt(a*a+o*o)/2;return{cx:c,cy:s,innerRadius:l,outerRadius:f,maxRadius:d}},m_=(e,t)=>{var r=me(t-e),n=Math.min(Math.abs(t-e),360);return r*n},y_=(e,t)=>{if(p.isValidElement(e))return p.cloneElement(e,t);if(typeof e=="function")return e(t);var r=U("recharts-pie-label-line",typeof e!="boolean"?e.className:"");return p.createElement(Eu,fr({},t,{type:"linear",className:r}))},g_=(e,t,r)=>{if(p.isValidElement(e))return p.cloneElement(e,t);var n=r;if(typeof e=="function"&&(n=e(t),p.isValidElement(n)))return n;var i=U("recharts-pie-label-text",typeof e!="boolean"&&typeof e!="function"?e.className:"");return p.createElement(ga,fr({},t,{alignmentBaseline:"middle",className:i}),n)};function b_(e){var{sectors:t,props:r,showLabels:n}=e,{label:i,labelLine:a,dataKey:o}=r;if(!n||!i||!t)return null;var u=z(r,!1),c=z(i,!1),s=z(a,!1),l=typeof i=="object"&&"offsetRadius"in i&&i.offsetRadius||20,f=t.map((d,v)=>{var h=(d.startAngle+d.endAngle)/2,y=ce(d.cx,d.cy,d.outerRadius+l,h),m=ie(ie(ie(ie({},u),d),{},{stroke:"none"},c),{},{index:v,textAnchor:v_(y.x,d.cx)},y),g=ie(ie(ie(ie({},u),d),{},{fill:"none",stroke:d.fill},s),{},{index:v,points:[ce(d.cx,d.cy,d.outerRadius,h),y],key:"line"});return p.createElement(ye,{key:"label-".concat(d.startAngle,"-").concat(d.endAngle,"-").concat(d.midAngle,"-").concat(v)},a&&y_(a,g),g_(i,m,te(d,o)))});return p.createElement(ye,{className:"recharts-pie-labels"},f)}function om(e){var{sectors:t,activeShape:r,inactiveShape:n,allOtherPieProps:i,showLabels:a}=e,o=$(zt),{onMouseEnter:u,onClick:c,onMouseLeave:s}=i,l=u_(i,o_),f=Bl(u,i.dataKey),d=Kl(s),v=zl(c,i.dataKey);return t==null?null:p.createElement(p.Fragment,null,t.map((h,y)=>{if((h==null?void 0:h.startAngle)===0&&(h==null?void 0:h.endAngle)===0&&t.length!==1)return null;var m=r&&String(y)===o,g=o?n:null,b=m?r:g,w=ie(ie({},h),{},{stroke:h.stroke,tabIndex:-1,[gv]:y,[bv]:i.dataKey});return p.createElement(ye,fr({tabIndex:-1,className:"recharts-pie-sector"},pn(l,h,y),{onMouseEnter:f(h,y),onMouseLeave:d(h,y),onClick:v(h,y),key:"sector-".concat(h==null?void 0:h.startAngle,"-").concat(h==null?void 0:h.endAngle,"-").concat(h.midAngle,"-").concat(y)}),p.createElement(nm,fr({option:b,isActive:m,shapeType:"sector"},w)))}),p.createElement(b_,{sectors:t,props:i,showLabels:a}))}function w_(e){var t,{pieSettings:r,displayedData:n,cells:i,offset:a}=e,{cornerRadius:o,startAngle:u,endAngle:c,dataKey:s,nameKey:l,tooltipType:f}=r,d=Math.abs(r.minAngle),v=m_(u,c),h=Math.abs(v),y=n.length<=1?0:(t=r.paddingAngle)!==null&&t!==void 0?t:0,m=n.filter(O=>te(O,s,0)!==0).length,g=(h>=360?m:m-1)*y,b=h-m*d-g,w=n.reduce((O,A)=>{var E=te(A,s,0);return O+(k(E)?E:0)},0),P;if(w>0){var x;P=n.map((O,A)=>{var E=te(O,s,0),_=te(O,l,A),D=p_(r,a,O),j=(k(E)?E:0)/w,C,N=ie(ie({},O),i&&i[A]&&i[A].props);A?C=x.endAngle+me(v)*y*(E!==0?1:0):C=u;var R=C+me(v)*((E!==0?d:0)+j*b),F=(C+R)/2,G=(D.innerRadius+D.outerRadius)/2,B=[{name:_,value:E,payload:N,dataKey:s,type:f}],fe=ce(D.cx,D.cy,G,F);return x=ie(ie(ie(ie({},r.presentationProps),{},{percent:j,cornerRadius:o,name:_,tooltipPayload:B,midAngle:F,middleRadius:G,tooltipPosition:fe},N),D),{},{value:te(O,s),startAngle:C,endAngle:R,payload:N,paddingAngle:me(v)*y}),x})}return P}function x_(e){var{props:t,previousSectorsRef:r}=e,{sectors:n,isAnimationActive:i,animationBegin:a,animationDuration:o,animationEasing:u,activeShape:c,inactiveShape:s,onAnimationStart:l,onAnimationEnd:f}=t,d=Wl(t,"recharts-pie-"),v=r.current,[h,y]=p.useState(!0),m=p.useCallback(()=>{typeof f=="function"&&f(),y(!1)},[f]),g=p.useCallback(()=>{typeof l=="function"&&l(),y(!0)},[l]);return p.createElement(Bt,{begin:a,duration:o,isActive:i,easing:u,from:{t:0},to:{t:1},onAnimationStart:g,onAnimationEnd:m,key:d},b=>{var{t:w}=b,P=[],x=n&&n[0],O=x.startAngle;return n.forEach((A,E)=>{var _=v&&v[E],D=E>0?Rt(A,"paddingAngle",0):0;if(_){var j=Be(_.endAngle-_.startAngle,A.endAngle-A.startAngle),C=ie(ie({},A),{},{startAngle:O+D,endAngle:O+j(w)+D});P.push(C),O=C.endAngle}else{var{endAngle:N,startAngle:R}=A,F=Be(0,N-R),G=F(w),B=ie(ie({},A),{},{startAngle:O+D,endAngle:O+G+D});P.push(B),O=B.endAngle}}),r.current=P,p.createElement(ye,null,p.createElement(om,{sectors:P,activeShape:c,inactiveShape:s,allOtherPieProps:t,showLabels:!h}))})}function P_(e){var{sectors:t,isAnimationActive:r,activeShape:n,inactiveShape:i}=e,a=p.useRef(null),o=a.current;return r&&t&&t.length&&(!o||o!==t)?p.createElement(x_,{props:e,previousSectorsRef:a}):p.createElement(om,{sectors:t,activeShape:n,inactiveShape:i,allOtherPieProps:e,showLabels:!0})}function O_(e){var{hide:t,className:r,rootTabIndex:n}=e,i=U("recharts-pie",r);return t?null:p.createElement(ye,{tabIndex:n,className:i},p.createElement(P_,e))}var um={animationBegin:400,animationDuration:1500,animationEasing:"ease",cx:"50%",cy:"50%",dataKey:"value",endAngle:360,fill:"#808080",hide:!1,innerRadius:0,isAnimationActive:!dr.isSsr,labelLine:!0,legendType:"rect",minAngle:0,nameKey:"name",outerRadius:"80%",paddingAngle:0,rootTabIndex:0,startAngle:0,stroke:"#fff"};function A_(e){var t=at(e,um),r=p.useMemo(()=>mn(e.children,ya),[e.children]),n=z(t,!1),i=p.useMemo(()=>({name:t.name,nameKey:t.nameKey,tooltipType:t.tooltipType,data:t.data,dataKey:t.dataKey,cx:t.cx,cy:t.cy,startAngle:t.startAngle,endAngle:t.endAngle,minAngle:t.minAngle,paddingAngle:t.paddingAngle,innerRadius:t.innerRadius,outerRadius:t.outerRadius,cornerRadius:t.cornerRadius,legendType:t.legendType,fill:t.fill,presentationProps:n}),[t.cornerRadius,t.cx,t.cy,t.data,t.dataKey,t.endAngle,t.innerRadius,t.minAngle,t.name,t.nameKey,t.outerRadius,t.paddingAngle,t.startAngle,t.tooltipType,t.legendType,t.fill,n]),a=$(o=>kE(o,i,r));return p.createElement(p.Fragment,null,p.createElement(Fl,{fn:d_,args:ie(ie({},t),{},{sectors:a})}),p.createElement(O_,fr({},t,{sectors:a})))}class lm extends p.PureComponent{constructor(){super(...arguments),ba(this,"id",ur("recharts-pie-"))}render(){return p.createElement(p.Fragment,null,p.createElement(qE,{data:this.props.data,dataKey:this.props.dataKey,hide:this.props.hide,angleAxisId:0,radiusAxisId:0,stackId:void 0,barSize:void 0,type:"pie"}),p.createElement(f_,this.props),p.createElement(A_,this.props),this.props.children)}}ba(lm,"displayName","Pie");ba(lm,"defaultProps",um);var S_=S([ve],e=>{if(e)return{top:e.top,bottom:e.bottom,left:e.left,right:e.right}}),E_=S([S_,_t,Tt],(e,t,r)=>{if(!(!e||t==null||r==null))return{x:e.left,y:e.top,width:Math.max(0,t-e.left-e.right),height:Math.max(0,r-e.top-e.bottom)}}),__=e=>{var t=je();return $(r=>pt(r,"xAxis",e,t))},T_=e=>{var t=je();return $(r=>pt(r,"yAxis",e,t))},Ul=()=>$(E_),j_=()=>$(BA);function Af(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Sf(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Af(Object(r),!0).forEach(function(n){C_(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Af(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function C_(e,t,r){return(t=k_(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function k_(e){var t=M_(e,"string");return typeof t=="symbol"?t:t+""}function M_(e,t){if(typeof e!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t||"default");if(typeof n!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var D_=e=>{var{point:t,childIndex:r,mainColor:n,activeDot:i,dataKey:a}=e;if(i===!1||t.x==null||t.y==null)return null;var o=Sf(Sf({index:r,dataKey:a,cx:t.x,cy:t.y,r:4,fill:n??"none",strokeWidth:2,stroke:"#fff",payload:t.payload,value:t.value},z(i,!1)),Xo(i)),u;return p.isValidElement(i)?u=p.cloneElement(i,o):typeof i=="function"?u=i(o):u=p.createElement(Xp,o),p.createElement(ye,{className:"recharts-active-dot"},u)};function I_(e){var{points:t,mainColor:r,activeDot:n,itemDataKey:i}=e,a=$(zt),o=j_();if(t==null||o==null)return null;var u=t.find(c=>o.includes(c.payload));return X(u)?null:D_({point:u,childIndex:Number(a),mainColor:r,dataKey:i,activeDot:n})}var $_=()=>{var e=ne();return p.useEffect(()=>(e(DE()),()=>{e(IE())})),null},N_=["children"];function L_(e,t){if(e==null)return{};var r,n,i=R_(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],t.indexOf(r)===-1&&{}.propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}function R_(e,t){if(e==null)return{};var r={};for(var n in e)if({}.hasOwnProperty.call(e,n)){if(t.indexOf(n)!==-1)continue;r[n]=e[n]}return r}var Ef=()=>{},cm=p.createContext({addErrorBar:Ef,removeErrorBar:Ef}),B_={data:[],xAxisId:"xAxis-0",yAxisId:"yAxis-0",dataPointFormatter:()=>({x:0,y:0,value:0}),errorBarOffset:0},sm=p.createContext(B_);function fm(e){var{children:t}=e,r=L_(e,N_);return p.createElement(sm.Provider,{value:r},t)}var K_=()=>p.useContext(sm),dm=e=>{var{children:t,xAxisId:r,yAxisId:n,zAxisId:i,dataKey:a,data:o,stackId:u,hide:c,type:s,barSize:l}=e,[f,d]=p.useState([]),v=p.useCallback(m=>{d(g=>[...g,m])},[d]),h=p.useCallback(m=>{d(g=>g.filter(b=>b!==m))},[d]),y=je();return p.createElement(cm.Provider,{value:{addErrorBar:v,removeErrorBar:h}},p.createElement(UE,{type:s,data:o,xAxisId:r,yAxisId:n,zAxisId:i,dataKey:a,errorBars:f,stackId:u,hide:c,barSize:l,isPanorama:y}),t)};function z_(e){var{addErrorBar:t,removeErrorBar:r}=p.useContext(cm);return p.useEffect(()=>(t(e),()=>{r(e)}),[t,r,e]),null}var F_=["direction","width","dataKey","isAnimationActive","animationBegin","animationDuration","animationEasing"];function vm(e,t,r){return(t=W_(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function W_(e){var t=U_(e,"string");return typeof t=="symbol"?t:t+""}function U_(e,t){if(typeof e!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t||"default");if(typeof n!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function dn(){return dn=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},dn.apply(null,arguments)}function q_(e,t){if(e==null)return{};var r,n,i=Y_(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],t.indexOf(r)===-1&&{}.propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}function Y_(e,t){if(e==null)return{};var r={};for(var n in e)if({}.hasOwnProperty.call(e,n)){if(t.indexOf(n)!==-1)continue;r[n]=e[n]}return r}function H_(e){var{direction:t,width:r,dataKey:n,isAnimationActive:i,animationBegin:a,animationDuration:o,animationEasing:u}=e,c=q_(e,F_),s=z(c,!1),{data:l,dataPointFormatter:f,xAxisId:d,yAxisId:v,errorBarOffset:h}=K_(),y=__(d),m=T_(v);if((y==null?void 0:y.scale)==null||(m==null?void 0:m.scale)==null||l==null||t==="x"&&y.type!=="number")return null;var g=l.map(b=>{var{x:w,y:P,value:x,errorVal:O}=f(b,n,t);if(!O)return null;var A=[],E,_;if(Array.isArray(O)?[E,_]=O:E=_=O,t==="x"){var{scale:D}=y,j=P+h,C=j+r,N=j-r,R=D(x-E),F=D(x+_);A.push({x1:F,y1:C,x2:F,y2:N}),A.push({x1:R,y1:j,x2:F,y2:j}),A.push({x1:R,y1:C,x2:R,y2:N})}else if(t==="y"){var{scale:G}=m,B=w+h,fe=B-r,ae=B+r,De=G(x-E),He=G(x+_);A.push({x1:fe,y1:He,x2:ae,y2:He}),A.push({x1:B,y1:De,x2:B,y2:He}),A.push({x1:fe,y1:De,x2:ae,y2:De})}var L="".concat(w+h,"px ").concat(P+h,"px");return p.createElement(ye,dn({className:"recharts-errorBar",key:"bar-".concat(A.map(we=>"".concat(we.x1,"-").concat(we.x2,"-").concat(we.y1,"-").concat(we.y2)))},s),A.map(we=>{var Ht=i?{transformOrigin:"".concat(we.x1-5,"px")}:void 0;return p.createElement(Bt,{from:{transform:"scaleY(0)",transformOrigin:L},to:{transform:"scaleY(1)",transformOrigin:L},begin:a,easing:u,isActive:i,duration:o,key:"line-".concat(we.x1,"-").concat(we.x2,"-").concat(we.y1,"-").concat(we.y2),style:{transformOrigin:L}},p.createElement("line",dn({},we,{style:Ht})))}))});return p.createElement(ye,{className:"recharts-errorBars"},g)}var hm=p.createContext(void 0);function G_(e){var t=p.useContext(hm);return e??t??"x"}function pm(e){var{direction:t,children:r}=e;return p.createElement(hm.Provider,{value:t},r)}var mm={stroke:"black",strokeWidth:1.5,width:5,offset:0,isAnimationActive:!0,animationBegin:0,animationDuration:400,animationEasing:"ease-in-out"};function V_(e){var t=G_(e.direction),{width:r,isAnimationActive:n,animationBegin:i,animationDuration:a,animationEasing:o}=at(e,mm);return p.createElement(p.Fragment,null,p.createElement(z_,{dataKey:e.dataKey,direction:t}),p.createElement(H_,dn({},e,{direction:t,width:r,isAnimationActive:n,animationBegin:i,animationDuration:a,animationEasing:o})))}class ym extends p.Component{render(){return p.createElement(V_,this.props)}}vm(ym,"defaultProps",mm);vm(ym,"displayName","ErrorBar");var X_=!0,Ua="Invariant failed";function Z_(e,t){if(!e){if(X_)throw new Error(Ua);var r=typeof t=="function"?t():t,n=r?"".concat(Ua,": ").concat(r):Ua;throw new Error(n)}}var J_=["x","y"];function Lo(){return Lo=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Lo.apply(null,arguments)}function _f(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Xr(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?_f(Object(r),!0).forEach(function(n){Q_(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):_f(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function Q_(e,t,r){return(t=eT(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function eT(e){var t=tT(e,"string");return typeof t=="symbol"?t:t+""}function tT(e,t){if(typeof e!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t||"default");if(typeof n!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function rT(e,t){if(e==null)return{};var r,n,i=nT(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],t.indexOf(r)===-1&&{}.propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}function nT(e,t){if(e==null)return{};var r={};for(var n in e)if({}.hasOwnProperty.call(e,n)){if(t.indexOf(n)!==-1)continue;r[n]=e[n]}return r}function iT(e,t){var{x:r,y:n}=e,i=rT(e,J_),a="".concat(r),o=parseInt(a,10),u="".concat(n),c=parseInt(u,10),s="".concat(t.height||i.height),l=parseInt(s,10),f="".concat(t.width||i.width),d=parseInt(f,10);return Xr(Xr(Xr(Xr(Xr({},t),i),o?{x:o}:{}),c?{y:c}:{}),{},{height:l,width:d,name:t.name,radius:t.radius})}function gm(e){return p.createElement(nm,Lo({shapeType:"rectangle",propTransformer:iT,activeClassName:"recharts-active-bar"},e))}var aT=function(t){var r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0;return(n,i)=>{if(k(t))return t;var a=k(n)||X(n);return a?t(n,i):(a||Z_(!1),r)}};function ql(e,t){var r,n,i=$(s=>Ct(s,e)),a=$(s=>qt(s,t)),o=(r=i==null?void 0:i.allowDataOverflow)!==null&&r!==void 0?r:Ne.allowDataOverflow,u=(n=a==null?void 0:a.allowDataOverflow)!==null&&n!==void 0?n:Le.allowDataOverflow,c=o||u;return{needClip:c,needClipX:o,needClipY:u}}function bm(e){var{xAxisId:t,yAxisId:r,clipPathId:n}=e,i=Ul(),{needClipX:a,needClipY:o,needClip:u}=ql(t,r);if(!u)return null;var{x:c,y:s,width:l,height:f}=i;return p.createElement("clipPath",{id:"clipPath-".concat(n)},p.createElement("rect",{x:a?c:c-l/2,y:o?s:s-f/2,width:a?l:l*2,height:o?f:f*2}))}var oT=["onMouseEnter","onMouseLeave","onClick"],uT=["value","background","tooltipPosition"],lT=["onMouseEnter","onClick","onMouseLeave"];function vn(){return vn=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},vn.apply(null,arguments)}function Tf(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Se(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Tf(Object(r),!0).forEach(function(n){wa(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Tf(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function wa(e,t,r){return(t=cT(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function cT(e){var t=sT(e,"string");return typeof t=="symbol"?t:t+""}function sT(e,t){if(typeof e!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t||"default");if(typeof n!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function Ro(e,t){if(e==null)return{};var r,n,i=fT(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],t.indexOf(r)===-1&&{}.propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}function fT(e,t){if(e==null)return{};var r={};for(var n in e)if({}.hasOwnProperty.call(e,n)){if(t.indexOf(n)!==-1)continue;r[n]=e[n]}return r}var dT=e=>{var{dataKey:t,name:r,fill:n,legendType:i,hide:a}=e;return[{inactive:a,dataKey:t,type:i,color:n,value:Ir(r,t),payload:e}]};function vT(e){var{dataKey:t,stroke:r,strokeWidth:n,fill:i,name:a,hide:o,unit:u}=e;return{dataDefinedOnItem:void 0,positions:void 0,settings:{stroke:r,strokeWidth:n,fill:i,dataKey:t,nameKey:void 0,name:Ir(a,t),hide:o,type:e.tooltipType,color:e.fill,unit:u}}}function hT(e){var t=$(zt),{data:r,dataKey:n,background:i,allOtherBarProps:a}=e,{onMouseEnter:o,onMouseLeave:u,onClick:c}=a,s=Ro(a,oT),l=Bl(o,n),f=Kl(u),d=zl(c,n);if(!i||r==null)return null;var v=z(i,!1);return p.createElement(p.Fragment,null,r.map((h,y)=>{var{value:m,background:g,tooltipPosition:b}=h,w=Ro(h,uT);if(!g)return null;var P=l(h,y),x=f(h,y),O=d(h,y),A=Se(Se(Se(Se(Se({option:i,isActive:String(y)===t},w),{},{fill:"#eee"},g),v),pn(s,h,y)),{},{onMouseEnter:P,onMouseLeave:x,onClick:O,dataKey:n,index:y,className:"recharts-bar-background-rectangle"});return p.createElement(gm,vn({key:"background-bar-".concat(y)},A))}))}function wm(e){var{data:t,props:r,showLabels:n}=e,i=z(r,!1),{shape:a,dataKey:o,activeBar:u}=r,c=$(zt),s=$(jp),{onMouseEnter:l,onClick:f,onMouseLeave:d}=r,v=Ro(r,lT),h=Bl(l,o),y=Kl(d),m=zl(f,o);return t?p.createElement(p.Fragment,null,t.map((g,b)=>{var w=u&&String(b)===c&&(s==null||o===s),P=w?u:a,x=Se(Se(Se({},i),g),{},{isActive:w,option:P,index:b,dataKey:o});return p.createElement(ye,vn({className:"recharts-bar-rectangle"},pn(v,g,b),{onMouseEnter:h(g,b),onMouseLeave:y(g,b),onClick:m(g,b),key:"rectangle-".concat(g==null?void 0:g.x,"-").concat(g==null?void 0:g.y,"-").concat(g==null?void 0:g.value,"-").concat(b)}),p.createElement(gm,x))}),n&&Lt.renderCallByParent(r,t)):null}function pT(e){var{props:t,previousRectanglesRef:r}=e,{data:n,layout:i,isAnimationActive:a,animationBegin:o,animationDuration:u,animationEasing:c,onAnimationEnd:s,onAnimationStart:l}=t,f=r.current,d=Wl(t,"recharts-bar-"),[v,h]=p.useState(!1),y=p.useCallback(()=>{typeof s=="function"&&s(),h(!1)},[s]),m=p.useCallback(()=>{typeof l=="function"&&l(),h(!0)},[l]);return p.createElement(Bt,{begin:o,duration:u,isActive:a,easing:c,from:{t:0},to:{t:1},onAnimationEnd:y,onAnimationStart:m,key:d},g=>{var{t:b}=g,w=b===1?n:n.map((P,x)=>{var O=f&&f[x];if(O){var A=Be(O.x,P.x),E=Be(O.y,P.y),_=Be(O.width,P.width),D=Be(O.height,P.height);return Se(Se({},P),{},{x:A(b),y:E(b),width:_(b),height:D(b)})}if(i==="horizontal"){var j=Be(0,P.height),C=j(b);return Se(Se({},P),{},{y:P.y+P.height-C,height:C})}var N=Be(0,P.width),R=N(b);return Se(Se({},P),{},{width:R})});return b>0&&(r.current=w),p.createElement(ye,null,p.createElement(wm,{props:t,data:w,showLabels:!v}))})}function mT(e){var{data:t,isAnimationActive:r}=e,n=p.useRef(null);return r&&t&&t.length&&(n.current==null||n.current!==t)?p.createElement(pT,{previousRectanglesRef:n,props:e}):p.createElement(wm,{props:e,data:t,showLabels:!0})}var xm=0,yT=(e,t)=>{var r=Array.isArray(e.value)?e.value[1]:e.value;return{x:e.x,y:e.y,value:r,errorVal:te(e,t)}};class gT extends p.PureComponent{constructor(){super(...arguments),wa(this,"id",ur("recharts-bar-"))}render(){var{hide:t,data:r,dataKey:n,className:i,xAxisId:a,yAxisId:o,needClip:u,background:c,id:s,layout:l}=this.props;if(t)return null;var f=U("recharts-bar",i),d=X(s)?this.id:s;return p.createElement(ye,{className:f},u&&p.createElement("defs",null,p.createElement(bm,{clipPathId:d,xAxisId:a,yAxisId:o})),p.createElement(ye,{className:"recharts-bar-rectangles",clipPath:u?"url(#clipPath-".concat(d,")"):null},p.createElement(hT,{data:r,dataKey:n,background:c,allOtherBarProps:this.props}),p.createElement(mT,this.props)),p.createElement(pm,{direction:l==="horizontal"?"y":"x"},this.props.children))}}var Pm={activeBar:!1,animationBegin:0,animationDuration:400,animationEasing:"ease",hide:!1,isAnimationActive:!dr.isSsr,legendType:"rect",minPointSize:xm,xAxisId:0,yAxisId:0};function bT(e){var{xAxisId:t,yAxisId:r,hide:n,legendType:i,minPointSize:a,activeBar:o,animationBegin:u,animationDuration:c,animationEasing:s,isAnimationActive:l}=at(e,Pm),{needClip:f}=ql(t,r),d=ea(),v=je(),h=p.useMemo(()=>({barSize:e.barSize,data:void 0,dataKey:e.dataKey,maxBarSize:e.maxBarSize,minPointSize:a,stackId:mv(e.stackId)}),[e.barSize,e.dataKey,e.maxBarSize,a,e.stackId]),y=mn(e.children,ya),m=$(w=>HT(w,t,r,v,h,y));if(d!=="vertical"&&d!=="horizontal")return null;var g,b=m==null?void 0:m[0];return b==null||b.height==null||b.width==null?g=0:g=d==="vertical"?b.height/2:b.width/2,p.createElement(fm,{xAxisId:t,yAxisId:r,data:m,dataPointFormatter:yT,errorBarOffset:g},p.createElement(gT,vn({},e,{layout:d,needClip:f,data:m,xAxisId:t,yAxisId:r,hide:n,legendType:i,minPointSize:a,activeBar:o,animationBegin:u,animationDuration:c,animationEasing:s,isAnimationActive:l})))}function wT(e){var{layout:t,barSettings:{dataKey:r,minPointSize:n},pos:i,bandSize:a,xAxis:o,yAxis:u,xAxisTicks:c,yAxisTicks:s,stackedData:l,displayedData:f,offset:d,cells:v}=e,h=t==="horizontal"?u:o,y=l?h.scale.domain():null,m=wb({numericAxis:h});return f.map((g,b)=>{var w,P,x,O,A,E;l?w=pb(l[b],y):(w=te(g,r),Array.isArray(w)||(w=[m,w]));var _=aT(n,xm)(w[1],b);if(t==="horizontal"){var D,[j,C]=[u.scale(w[0]),u.scale(w[1])];P=kc({axis:o,ticks:c,bandSize:a,offset:i.offset,entry:g,index:b}),x=(D=C??j)!==null&&D!==void 0?D:void 0,O=i.size;var N=j-C;if(A=We(N)?0:N,E={x:P,y:d.top,width:O,height:d.height},Math.abs(_)>0&&Math.abs(A)<Math.abs(_)){var R=me(A||_)*(Math.abs(_)-Math.abs(A));x-=R,A+=R}}else{var[F,G]=[o.scale(w[0]),o.scale(w[1])];if(P=F,x=kc({axis:u,ticks:s,bandSize:a,offset:i.offset,entry:g,index:b}),O=G-F,A=i.size,E={x:d.left,y:x,width:d.width,height:A},Math.abs(_)>0&&Math.abs(O)<Math.abs(_)){var B=me(O||_)*(Math.abs(_)-Math.abs(O));O+=B}}var fe=Se(Se({},g),{},{x:P,y:x,width:O,height:A,value:l?w:w[1],payload:g,background:E,tooltipPosition:{x:P+O/2,y:x+A/2}},v&&v[b]&&v[b].props);return fe})}class Om extends p.PureComponent{render(){return p.createElement(dm,{type:"bar",data:null,xAxisId:this.props.xAxisId,yAxisId:this.props.yAxisId,zAxisId:0,dataKey:this.props.dataKey,stackId:this.props.stackId,hide:this.props.hide,barSize:this.props.barSize},p.createElement($_,null),p.createElement(am,{legendPayload:dT(this.props)}),p.createElement(Fl,{fn:vT,args:this.props}),p.createElement(bT,this.props))}}wa(Om,"displayName","Bar");wa(Om,"defaultProps",Pm);function jf(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Fn(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?jf(Object(r),!0).forEach(function(n){xT(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):jf(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function xT(e,t,r){return(t=PT(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function PT(e){var t=OT(e,"string");return typeof t=="symbol"?t:t+""}function OT(e,t){if(typeof e!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t||"default");if(typeof n!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var AT=(e,t)=>t,ST=(e,t,r)=>r,ET=(e,t,r,n)=>n,Yl=(e,t,r,n,i)=>i,_T=(e,t,r,n,i)=>i.maxBarSize,TT=(e,t,r,n,i,a)=>a,Cf=(e,t,r)=>{var n=r??e;if(!X(n))return Te(n,t,0)},jT=S([Y,sa,AT,ST,ET],(e,t,r,n,i)=>t.filter(a=>e==="horizontal"?a.xAxisId===r:a.yAxisId===n).filter(a=>a.isPanorama===i).filter(a=>a.hide===!1).filter(a=>a.type==="bar")),CT=(e,t,r,n)=>{var i=Y(e);return i==="horizontal"?jo(e,"yAxis",r,n):jo(e,"xAxis",t,n)},kT=(e,t,r)=>{var n=Y(e);return n==="horizontal"?zs(e,"xAxis",t):zs(e,"yAxis",r)};function MT(e){return e.stackId!=null&&e.dataKey!=null}var DT=(e,t,r)=>{var n={},i=e.filter(MT),a=e.filter(s=>s.stackId==null),o=i.reduce((s,l)=>(s[l.stackId]||(s[l.stackId]=[]),s[l.stackId].push(l),s),n),u=Object.entries(o).map(s=>{var[l,f]=s,d=f.map(h=>h.dataKey),v=Cf(t,r,f[0].barSize);return{stackId:l,dataKeys:d,barSize:v}}),c=a.map(s=>{var l=[s.dataKey].filter(d=>d!=null),f=Cf(t,r,s.barSize);return{stackId:void 0,dataKeys:l,barSize:f}});return[...u,...c]},IT=S([jT,X1,kT],DT),$T=(e,t,r,n,i)=>{var a,o,u=Y(e),c=$h(e),{maxBarSize:s}=i,l=X(s)?c:s,f,d;return u==="horizontal"?(f=pt(e,"xAxis",t,n),d=Kt(e,"xAxis",t,n)):(f=pt(e,"yAxis",r,n),d=Kt(e,"yAxis",r,n)),(a=(o=nn(f,d,!0))!==null&&o!==void 0?o:l)!==null&&a!==void 0?a:0},Am=(e,t,r,n)=>{var i=Y(e),a,o;return i==="horizontal"?(a=pt(e,"xAxis",t,n),o=Kt(e,"xAxis",t,n)):(a=pt(e,"yAxis",r,n),o=Kt(e,"yAxis",r,n)),nn(a,o)};function NT(e,t,r,n,i){var a=n.length;if(!(a<1)){var o=Te(e,r,0,!0),u,c=[];if(Ye(n[0].barSize)){var s=!1,l=r/a,f=n.reduce((g,b)=>g+(b.barSize||0),0);f+=(a-1)*o,f>=r&&(f-=(a-1)*o,o=0),f>=r&&l>0&&(s=!0,l*=.9,f=a*l);var d=(r-f)/2>>0,v={offset:d-o,size:0};u=n.reduce((g,b)=>{var w,P={stackId:b.stackId,dataKeys:b.dataKeys,position:{offset:v.offset+v.size+o,size:s?l:(w=b.barSize)!==null&&w!==void 0?w:0}},x=[...g,P];return v=x[x.length-1].position,x},c)}else{var h=Te(t,r,0,!0);r-2*h-(a-1)*o<=0&&(o=0);var y=(r-2*h-(a-1)*o)/a;y>1&&(y>>=0);var m=Ye(i)?Math.min(y,i):y;u=n.reduce((g,b,w)=>[...g,{stackId:b.stackId,dataKeys:b.dataKeys,position:{offset:h+(y+o)*w+(y-m)/2,size:m}}],c)}return u}}var LT=(e,t,r,n,i,a,o)=>{var u=X(o)?t:o,c=NT(r,n,i!==a?i:a,e,u);return i!==a&&c!=null&&(c=c.map(s=>Fn(Fn({},s),{},{position:Fn(Fn({},s.position),{},{offset:s.position.offset-i/2})}))),c},RT=S([IT,$h,V1,Nh,$T,Am,_T],LT),BT=(e,t,r,n)=>pt(e,"xAxis",t,n),KT=(e,t,r,n)=>pt(e,"yAxis",r,n),zT=(e,t,r,n)=>Kt(e,"xAxis",t,n),FT=(e,t,r,n)=>Kt(e,"yAxis",r,n),WT=S([RT,Yl],(e,t)=>{if(e!=null){var r=e.find(n=>n.stackId===t.stackId&&n.dataKeys.includes(t.dataKey));if(r!=null)return r.position}}),UT=(e,t)=>{if(!(!e||(t==null?void 0:t.dataKey)==null)){var{stackId:r}=t;if(r!=null){var n=e[r];if(n){var{stackedData:i}=n;if(i){var a=i.find(o=>o.key===t.dataKey);return a}}}}},qT=S([sa,Yl],(e,t)=>{if(e.some(r=>r.type==="bar"&&t.dataKey===r.dataKey&&t.stackId===r.stackId&&t.stackId===r.stackId))return t}),YT=S([CT,Yl],UT),HT=S([ve,BT,KT,zT,FT,WT,Y,Ju,Am,YT,qT,TT],(e,t,r,n,i,a,o,u,c,s,l,f)=>{var{chartData:d,dataStartIndex:v,dataEndIndex:h}=u;if(!(l==null||a==null||o!=="horizontal"&&o!=="vertical"||t==null||r==null||n==null||i==null||c==null)){var{data:y}=l,m;if(y!=null&&y.length>0?m=y:m=d==null?void 0:d.slice(v,h+1),m!=null)return wT({layout:o,barSettings:l,pos:a,bandSize:c,xAxis:t,yAxis:r,xAxisTicks:n,yAxisTicks:i,stackedData:s,displayedData:m,offset:e,cells:f})}}),Sm=e=>{var{chartData:t}=e,r=ne(),n=je();return p.useEffect(()=>n?()=>{}:(r(Xs(t)),()=>{r(Xs(void 0))}),[t,r,n]),null},kf={x:0,y:0,width:0,height:0,padding:{top:0,right:0,bottom:0,left:0}},Em=it({name:"brush",initialState:kf,reducers:{setBrushSettings(e,t){return t.payload==null?kf:t.payload}}});Em.actions;var GT=Em.reducer;function VT(e,t,r){return(t=XT(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function XT(e){var t=ZT(e,"string");return typeof t=="symbol"?t:t+""}function ZT(e,t){if(typeof e!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t||"default");if(typeof n!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}class Hl{static create(t){return new Hl(t)}constructor(t){this.scale=t}get domain(){return this.scale.domain}get range(){return this.scale.range}get rangeMin(){return this.range()[0]}get rangeMax(){return this.range()[1]}get bandwidth(){return this.scale.bandwidth}apply(t){var{bandAware:r,position:n}=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};if(t!==void 0){if(n)switch(n){case"start":return this.scale(t);case"middle":{var i=this.bandwidth?this.bandwidth()/2:0;return this.scale(t)+i}case"end":{var a=this.bandwidth?this.bandwidth():0;return this.scale(t)+a}default:return this.scale(t)}if(r){var o=this.bandwidth?this.bandwidth()/2:0;return this.scale(t)+o}return this.scale(t)}}isInRange(t){var r=this.range(),n=r[0],i=r[r.length-1];return n<=i?t>=n&&t<=i:t>=i&&t<=n}}VT(Hl,"EPS",1e-4);function JT(e){return(e%180+180)%180}var QT=function(t){var{width:r,height:n}=t,i=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0,a=JT(i),o=a*Math.PI/180,u=Math.atan(n/r),c=o>u&&o<Math.PI-u?n/Math.sin(o):r/Math.cos(o);return Math.abs(c)},ej={dots:[],areas:[],lines:[]},_m=it({name:"referenceElements",initialState:ej,reducers:{addDot:(e,t)=>{e.dots.push(t.payload)},removeDot:(e,t)=>{var r=wt(e).dots.findIndex(n=>n===t.payload);r!==-1&&e.dots.splice(r,1)},addArea:(e,t)=>{e.areas.push(t.payload)},removeArea:(e,t)=>{var r=wt(e).areas.findIndex(n=>n===t.payload);r!==-1&&e.areas.splice(r,1)},addLine:(e,t)=>{e.lines.push(t.payload)},removeLine:(e,t)=>{var r=wt(e).lines.findIndex(n=>n===t.payload);r!==-1&&e.lines.splice(r,1)}}});_m.actions;var tj=_m.reducer,rj=p.createContext(void 0),nj=e=>{var{children:t}=e,[r]=p.useState("".concat(ur("recharts"),"-clip")),n=Ul();if(n==null)return null;var{x:i,y:a,width:o,height:u}=n;return p.createElement(rj.Provider,{value:r},p.createElement("defs",null,p.createElement("clipPath",{id:r},p.createElement("rect",{x:i,y:a,height:u,width:o}))),t)};function qa(e,t){for(var r in e)if({}.hasOwnProperty.call(e,r)&&(!{}.hasOwnProperty.call(t,r)||e[r]!==t[r]))return!1;for(var n in t)if({}.hasOwnProperty.call(t,n)&&!{}.hasOwnProperty.call(e,n))return!1;return!0}function Tm(e,t,r){if(t<1)return[];if(t===1&&r===void 0)return e;for(var n=[],i=0;i<e.length;i+=t)if(r===void 0||r(e[i])===!0)n.push(e[i]);else return;return n}function ij(e,t,r){var n={width:e.width+t.width,height:e.height+t.height};return QT(n,r)}function aj(e,t,r){var n=r==="width",{x:i,y:a,width:o,height:u}=e;return t===1?{start:n?i:a,end:n?i+o:a+u}:{start:n?i+o:a+u,end:n?i:a}}function xi(e,t,r,n,i){if(e*t<e*n||e*t>e*i)return!1;var a=r();return e*(t-e*a/2-n)>=0&&e*(t+e*a/2-i)<=0}function oj(e,t){return Tm(e,t+1)}function uj(e,t,r,n,i){for(var a=(n||[]).slice(),{start:o,end:u}=t,c=0,s=1,l=o,f=function(){var h=n==null?void 0:n[c];if(h===void 0)return{v:Tm(n,s)};var y=c,m,g=()=>(m===void 0&&(m=r(h,y)),m),b=h.coordinate,w=c===0||xi(e,b,g,l,u);w||(c=0,l=o,s+=1),w&&(l=b+e*(g()/2+i),c+=s)},d;s<=a.length;)if(d=f(),d)return d.v;return[]}function Mf(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Ae(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Mf(Object(r),!0).forEach(function(n){lj(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Mf(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function lj(e,t,r){return(t=cj(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function cj(e){var t=sj(e,"string");return typeof t=="symbol"?t:t+""}function sj(e,t){if(typeof e!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t||"default");if(typeof n!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function fj(e,t,r,n,i){for(var a=(n||[]).slice(),o=a.length,{start:u}=t,{end:c}=t,s=function(d){var v=a[d],h,y=()=>(h===void 0&&(h=r(v,d)),h);if(d===o-1){var m=e*(v.coordinate+e*y()/2-c);a[d]=v=Ae(Ae({},v),{},{tickCoord:m>0?v.coordinate-m*e:v.coordinate})}else a[d]=v=Ae(Ae({},v),{},{tickCoord:v.coordinate});var g=xi(e,v.tickCoord,y,u,c);g&&(c=v.tickCoord-e*(y()/2+i),a[d]=Ae(Ae({},v),{},{isShow:!0}))},l=o-1;l>=0;l--)s(l);return a}function dj(e,t,r,n,i,a){var o=(n||[]).slice(),u=o.length,{start:c,end:s}=t;if(a){var l=n[u-1],f=r(l,u-1),d=e*(l.coordinate+e*f/2-s);o[u-1]=l=Ae(Ae({},l),{},{tickCoord:d>0?l.coordinate-d*e:l.coordinate});var v=xi(e,l.tickCoord,()=>f,c,s);v&&(s=l.tickCoord-e*(f/2+i),o[u-1]=Ae(Ae({},l),{},{isShow:!0}))}for(var h=a?u-1:u,y=function(b){var w=o[b],P,x=()=>(P===void 0&&(P=r(w,b)),P);if(b===0){var O=e*(w.coordinate-e*x()/2-c);o[b]=w=Ae(Ae({},w),{},{tickCoord:O<0?w.coordinate-O*e:w.coordinate})}else o[b]=w=Ae(Ae({},w),{},{tickCoord:w.coordinate});var A=xi(e,w.tickCoord,x,c,s);A&&(c=w.tickCoord+e*(x()/2+i),o[b]=Ae(Ae({},w),{},{isShow:!0}))},m=0;m<h;m++)y(m);return o}function Gl(e,t,r){var{tick:n,ticks:i,viewBox:a,minTickGap:o,orientation:u,interval:c,tickFormatter:s,unit:l,angle:f}=e;if(!i||!i.length||!n)return[];if(k(c)||dr.isSsr){var d;return(d=oj(i,k(c)?c:0))!==null&&d!==void 0?d:[]}var v=[],h=u==="top"||u==="bottom"?"width":"height",y=l&&h==="width"?en(l,{fontSize:t,letterSpacing:r}):{width:0,height:0},m=(w,P)=>{var x=typeof s=="function"?s(w.value,P):w.value;return h==="width"?ij(en(x,{fontSize:t,letterSpacing:r}),y,f):en(x,{fontSize:t,letterSpacing:r})[h]},g=i.length>=2?me(i[1].coordinate-i[0].coordinate):1,b=aj(a,g,h);return c==="equidistantPreserveStart"?uj(g,b,m,i,o):(c==="preserveStart"||c==="preserveStartEnd"?v=dj(g,b,m,i,o,c==="preserveStartEnd"):v=fj(g,b,m,i,o),v.filter(w=>w.isShow))}var vj=["viewBox"],hj=["viewBox"];function xr(){return xr=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},xr.apply(null,arguments)}function Df(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function de(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Df(Object(r),!0).forEach(function(n){Vl(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Df(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function If(e,t){if(e==null)return{};var r,n,i=pj(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],t.indexOf(r)===-1&&{}.propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}function pj(e,t){if(e==null)return{};var r={};for(var n in e)if({}.hasOwnProperty.call(e,n)){if(t.indexOf(n)!==-1)continue;r[n]=e[n]}return r}function Vl(e,t,r){return(t=mj(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function mj(e){var t=yj(e,"string");return typeof t=="symbol"?t:t+""}function yj(e,t){if(typeof e!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t||"default");if(typeof n!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}class Yt extends p.Component{constructor(t){super(t),this.tickRefs=p.createRef(),this.tickRefs.current=[],this.state={fontSize:"",letterSpacing:""}}shouldComponentUpdate(t,r){var{viewBox:n}=t,i=If(t,vj),a=this.props,{viewBox:o}=a,u=If(a,hj);return!qa(n,o)||!qa(i,u)||!qa(r,this.state)}getTickLineCoord(t){var{x:r,y:n,width:i,height:a,orientation:o,tickSize:u,mirror:c,tickMargin:s}=this.props,l,f,d,v,h,y,m=c?-1:1,g=t.tickSize||u,b=k(t.tickCoord)?t.tickCoord:t.coordinate;switch(o){case"top":l=f=t.coordinate,v=n+ +!c*a,d=v-m*g,y=d-m*s,h=b;break;case"left":d=v=t.coordinate,f=r+ +!c*i,l=f-m*g,h=l-m*s,y=b;break;case"right":d=v=t.coordinate,f=r+ +c*i,l=f+m*g,h=l+m*s,y=b;break;default:l=f=t.coordinate,v=n+ +c*a,d=v+m*g,y=d+m*s,h=b;break}return{line:{x1:l,y1:d,x2:f,y2:v},tick:{x:h,y}}}getTickTextAnchor(){var{orientation:t,mirror:r}=this.props,n;switch(t){case"left":n=r?"start":"end";break;case"right":n=r?"end":"start";break;default:n="middle";break}return n}getTickVerticalAnchor(){var{orientation:t,mirror:r}=this.props;switch(t){case"left":case"right":return"middle";case"top":return r?"start":"end";default:return r?"end":"start"}}renderAxisLine(){var{x:t,y:r,width:n,height:i,orientation:a,mirror:o,axisLine:u}=this.props,c=de(de(de({},z(this.props,!1)),z(u,!1)),{},{fill:"none"});if(a==="top"||a==="bottom"){var s=+(a==="top"&&!o||a==="bottom"&&o);c=de(de({},c),{},{x1:t,y1:r+s*i,x2:t+n,y2:r+s*i})}else{var l=+(a==="left"&&!o||a==="right"&&o);c=de(de({},c),{},{x1:t+l*n,y1:r,x2:t+l*n,y2:r+i})}return p.createElement("line",xr({},c,{className:U("recharts-cartesian-axis-line",Rt(u,"className"))}))}static renderTickItem(t,r,n){var i,a=U(r.className,"recharts-cartesian-axis-tick-value");if(p.isValidElement(t))i=p.cloneElement(t,de(de({},r),{},{className:a}));else if(typeof t=="function")i=t(de(de({},r),{},{className:a}));else{var o="recharts-cartesian-axis-tick-value";typeof t!="boolean"&&(o=U(o,t.className)),i=p.createElement(ga,xr({},r,{className:o}),n)}return i}renderTicks(t,r){var n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:[],{tickLine:i,stroke:a,tick:o,tickFormatter:u,unit:c}=this.props,s=Gl(de(de({},this.props),{},{ticks:n}),t,r),l=this.getTickTextAnchor(),f=this.getTickVerticalAnchor(),d=z(this.props,!1),v=z(o,!1),h=de(de({},d),{},{fill:"none"},z(i,!1)),y=s.map((m,g)=>{var{line:b,tick:w}=this.getTickLineCoord(m),P=de(de(de(de({textAnchor:l,verticalAnchor:f},d),{},{stroke:"none",fill:a},v),w),{},{index:g,payload:m,visibleTicksCount:s.length,tickFormatter:u});return p.createElement(ye,xr({className:"recharts-cartesian-axis-tick",key:"tick-".concat(m.value,"-").concat(m.coordinate,"-").concat(m.tickCoord)},pn(this.props,m,g)),i&&p.createElement("line",xr({},h,b,{className:U("recharts-cartesian-axis-tick-line",Rt(i,"className"))})),o&&Yt.renderTickItem(o,P,"".concat(typeof u=="function"?u(m.value,g):m.value).concat(c||"")))});return y.length>0?p.createElement("g",{className:"recharts-cartesian-axis-ticks"},y):null}render(){var{axisLine:t,width:r,height:n,className:i,hide:a}=this.props;if(a)return null;var{ticks:o}=this.props;return r!=null&&r<=0||n!=null&&n<=0?null:p.createElement(ye,{className:U("recharts-cartesian-axis",i),ref:u=>{if(u){var c=u.getElementsByClassName("recharts-cartesian-axis-tick-value");this.tickRefs.current=Array.from(c);var s=c[0];if(s){var l=window.getComputedStyle(s).fontSize,f=window.getComputedStyle(s).letterSpacing;(l!==this.state.fontSize||f!==this.state.letterSpacing)&&this.setState({fontSize:window.getComputedStyle(s).fontSize,letterSpacing:window.getComputedStyle(s).letterSpacing})}}}},t&&this.renderAxisLine(),this.renderTicks(this.state.fontSize,this.state.letterSpacing,o),Ke.renderCallByParent(this.props))}}Vl(Yt,"displayName","CartesianAxis");Vl(Yt,"defaultProps",{x:0,y:0,width:0,height:0,viewBox:{x:0,y:0,width:0,height:0},orientation:"bottom",ticks:[],stroke:"#666",tickLine:!0,axisLine:!0,tick:!0,mirror:!1,minTickGap:5,tickSize:6,tickMargin:2,interval:"preserveEnd"});var gj=["x1","y1","x2","y2","key"],bj=["offset"],wj=["xAxisId","yAxisId"],xj=["xAxisId","yAxisId"];function $f(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Ee(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?$f(Object(r),!0).forEach(function(n){Pj(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):$f(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function Pj(e,t,r){return(t=Oj(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Oj(e){var t=Aj(e,"string");return typeof t=="symbol"?t:t+""}function Aj(e,t){if(typeof e!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t||"default");if(typeof n!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function rr(){return rr=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},rr.apply(null,arguments)}function Pi(e,t){if(e==null)return{};var r,n,i=Sj(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],t.indexOf(r)===-1&&{}.propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}function Sj(e,t){if(e==null)return{};var r={};for(var n in e)if({}.hasOwnProperty.call(e,n)){if(t.indexOf(n)!==-1)continue;r[n]=e[n]}return r}var Ej=e=>{var{fill:t}=e;if(!t||t==="none")return null;var{fillOpacity:r,x:n,y:i,width:a,height:o,ry:u}=e;return p.createElement("rect",{x:n,y:i,ry:u,width:a,height:o,stroke:"none",fill:t,fillOpacity:r,className:"recharts-cartesian-grid-bg"})};function jm(e,t){var r;if(p.isValidElement(e))r=p.cloneElement(e,t);else if(typeof e=="function")r=e(t);else{var{x1:n,y1:i,x2:a,y2:o,key:u}=t,c=Pi(t,gj),s=z(c,!1),l=Pi(s,bj);r=p.createElement("line",rr({},l,{x1:n,y1:i,x2:a,y2:o,fill:"none",key:u}))}return r}function _j(e){var{x:t,width:r,horizontal:n=!0,horizontalPoints:i}=e;if(!n||!i||!i.length)return null;var a=Pi(e,wj),o=i.map((u,c)=>{var s=Ee(Ee({},a),{},{x1:t,y1:u,x2:t+r,y2:u,key:"line-".concat(c),index:c});return jm(n,s)});return p.createElement("g",{className:"recharts-cartesian-grid-horizontal"},o)}function Tj(e){var{y:t,height:r,vertical:n=!0,verticalPoints:i}=e;if(!n||!i||!i.length)return null;var a=Pi(e,xj),o=i.map((u,c)=>{var s=Ee(Ee({},a),{},{x1:u,y1:t,x2:u,y2:t+r,key:"line-".concat(c),index:c});return jm(n,s)});return p.createElement("g",{className:"recharts-cartesian-grid-vertical"},o)}function jj(e){var{horizontalFill:t,fillOpacity:r,x:n,y:i,width:a,height:o,horizontalPoints:u,horizontal:c=!0}=e;if(!c||!t||!t.length)return null;var s=u.map(f=>Math.round(f+i-i)).sort((f,d)=>f-d);i!==s[0]&&s.unshift(0);var l=s.map((f,d)=>{var v=!s[d+1],h=v?i+o-f:s[d+1]-f;if(h<=0)return null;var y=d%t.length;return p.createElement("rect",{key:"react-".concat(d),y:f,x:n,height:h,width:a,stroke:"none",fill:t[y],fillOpacity:r,className:"recharts-cartesian-grid-bg"})});return p.createElement("g",{className:"recharts-cartesian-gridstripes-horizontal"},l)}function Cj(e){var{vertical:t=!0,verticalFill:r,fillOpacity:n,x:i,y:a,width:o,height:u,verticalPoints:c}=e;if(!t||!r||!r.length)return null;var s=c.map(f=>Math.round(f+i-i)).sort((f,d)=>f-d);i!==s[0]&&s.unshift(0);var l=s.map((f,d)=>{var v=!s[d+1],h=v?i+o-f:s[d+1]-f;if(h<=0)return null;var y=d%r.length;return p.createElement("rect",{key:"react-".concat(d),x:f,y:a,width:h,height:u,stroke:"none",fill:r[y],fillOpacity:n,className:"recharts-cartesian-grid-bg"})});return p.createElement("g",{className:"recharts-cartesian-gridstripes-vertical"},l)}var kj=(e,t)=>{var{xAxis:r,width:n,height:i,offset:a}=e;return hv(Gl(Ee(Ee(Ee({},Yt.defaultProps),r),{},{ticks:pv(r,!0),viewBox:{x:0,y:0,width:n,height:i}})),a.left,a.left+a.width,t)},Mj=(e,t)=>{var{yAxis:r,width:n,height:i,offset:a}=e;return hv(Gl(Ee(Ee(Ee({},Yt.defaultProps),r),{},{ticks:pv(r,!0),viewBox:{x:0,y:0,width:n,height:i}})),a.top,a.top+a.height,t)},Dj={horizontal:!0,vertical:!0,horizontalPoints:[],verticalPoints:[],stroke:"#ccc",fill:"none",verticalFill:[],horizontalFill:[],xAxisId:0,yAxisId:0};function Ij(e){var t=Pu(),r=Ou(),n=xv(),i=Ee(Ee({},at(e,Dj)),{},{x:k(e.x)?e.x:n.left,y:k(e.y)?e.y:n.top,width:k(e.width)?e.width:n.width,height:k(e.height)?e.height:n.height}),{xAxisId:a,yAxisId:o,x:u,y:c,width:s,height:l,syncWithTicks:f,horizontalValues:d,verticalValues:v}=i,h=je(),y=$(_=>Fs(_,"xAxis",a,h)),m=$(_=>Fs(_,"yAxis",o,h));if(!k(s)||s<=0||!k(l)||l<=0||!k(u)||u!==+u||!k(c)||c!==+c)return null;var g=i.verticalCoordinatesGenerator||kj,b=i.horizontalCoordinatesGenerator||Mj,{horizontalPoints:w,verticalPoints:P}=i;if((!w||!w.length)&&typeof b=="function"){var x=d&&d.length,O=b({yAxis:m?Ee(Ee({},m),{},{ticks:x?d:m.ticks}):void 0,width:t,height:r,offset:n},x?!0:f);Qr(Array.isArray(O),"horizontalCoordinatesGenerator should return Array but instead it returned [".concat(typeof O,"]")),Array.isArray(O)&&(w=O)}if((!P||!P.length)&&typeof g=="function"){var A=v&&v.length,E=g({xAxis:y?Ee(Ee({},y),{},{ticks:A?v:y.ticks}):void 0,width:t,height:r,offset:n},A?!0:f);Qr(Array.isArray(E),"verticalCoordinatesGenerator should return Array but instead it returned [".concat(typeof E,"]")),Array.isArray(E)&&(P=E)}return p.createElement("g",{className:"recharts-cartesian-grid"},p.createElement(Ej,{fill:i.fill,fillOpacity:i.fillOpacity,x:i.x,y:i.y,width:i.width,height:i.height,ry:i.ry}),p.createElement(jj,rr({},i,{horizontalPoints:w})),p.createElement(Cj,rr({},i,{verticalPoints:P})),p.createElement(_j,rr({},i,{offset:n,horizontalPoints:w,xAxis:y,yAxis:m})),p.createElement(Tj,rr({},i,{offset:n,verticalPoints:P,xAxis:y,yAxis:m})))}Ij.displayName="CartesianGrid";var Cm=(e,t,r,n)=>pt(e,"xAxis",t,n),km=(e,t,r,n)=>Kt(e,"xAxis",t,n),Mm=(e,t,r,n)=>pt(e,"yAxis",r,n),Dm=(e,t,r,n)=>Kt(e,"yAxis",r,n),$j=S([Y,Cm,Mm,km,Dm],(e,t,r,n,i)=>Ft(e,"xAxis")?nn(t,n,!1):nn(r,i,!1)),Nj=(e,t,r,n,i)=>i,Lj=S([sa,Nj],(e,t)=>{if(e.some(r=>r.type==="line"&&t.dataKey===r.dataKey&&t.data===r.data))return t}),Rj=S([Y,Cm,Mm,km,Dm,Lj,$j,Ju],(e,t,r,n,i,a,o,u)=>{var{chartData:c,dataStartIndex:s,dataEndIndex:l}=u;if(!(a==null||t==null||r==null||n==null||i==null||n.length===0||i.length===0||o==null)){var{dataKey:f,data:d}=a,v;if(d!=null&&d.length>0?v=d:v=c==null?void 0:c.slice(s,l+1),v!=null)return nC({layout:e,xAxis:t,yAxis:r,xAxisTicks:n,yAxisTicks:i,dataKey:f,bandSize:o,displayedData:v})}}),Bj=["type","layout","connectNulls","needClip"],Kj=["activeDot","animateNewValues","animationBegin","animationDuration","animationEasing","connectNulls","dot","hide","isAnimationActive","label","legendType","xAxisId","yAxisId"];function Im(e,t){if(e==null)return{};var r,n,i=zj(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],t.indexOf(r)===-1&&{}.propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}function zj(e,t){if(e==null)return{};var r={};for(var n in e)if({}.hasOwnProperty.call(e,n)){if(t.indexOf(n)!==-1)continue;r[n]=e[n]}return r}function Nf(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function ut(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Nf(Object(r),!0).forEach(function(n){xa(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Nf(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function xa(e,t,r){return(t=Fj(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Fj(e){var t=Wj(e,"string");return typeof t=="symbol"?t:t+""}function Wj(e,t){if(typeof e!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t||"default");if(typeof n!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function Dr(){return Dr=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Dr.apply(null,arguments)}var Uj=e=>{var{dataKey:t,name:r,stroke:n,legendType:i,hide:a}=e;return[{inactive:a,dataKey:t,type:i,color:n,value:Ir(r,t),payload:e}]};function qj(e){var{dataKey:t,data:r,stroke:n,strokeWidth:i,fill:a,name:o,hide:u,unit:c}=e;return{dataDefinedOnItem:r,positions:void 0,settings:{stroke:n,strokeWidth:i,fill:a,dataKey:t,nameKey:void 0,name:Ir(o,t),hide:u,type:e.tooltipType,color:e.stroke,unit:c}}}var $m=(e,t)=>"".concat(t,"px ").concat(e-t,"px");function Yj(e,t){for(var r=e.length%2!==0?[...e,0]:e,n=[],i=0;i<t;++i)n=[...n,...r];return n}var Hj=(e,t,r)=>{var n=r.reduce((f,d)=>f+d);if(!n)return $m(t,e);for(var i=Math.floor(e/n),a=e%n,o=t-e,u=[],c=0,s=0;c<r.length;s+=r[c],++c)if(s+r[c]>a){u=[...r.slice(0,c),a-s];break}var l=u.length%2===0?[0,o]:[o];return[...Yj(r,i),...u,...l].map(f=>"".concat(f,"px")).join(", ")};function Gj(e,t){var r;if(p.isValidElement(e))r=p.cloneElement(e,t);else if(typeof e=="function")r=e(t);else{var n=U("recharts-line-dot",typeof e!="boolean"?e.className:"");r=p.createElement(Xp,Dr({},t,{className:n}))}return r}function Vj(e,t){return e==null?!1:t?!0:e.length===1}function Xj(e){var{clipPathId:t,points:r,props:n}=e,{dot:i,dataKey:a,needClip:o}=n;if(!Vj(r,i))return null;var u=Xf(i),c=z(n,!1),s=z(i,!0),l=r.map((d,v)=>{var h=ut(ut(ut({key:"dot-".concat(v),r:3},c),s),{},{index:v,cx:d.x,cy:d.y,dataKey:a,value:d.value,payload:d.payload,points:r});return Gj(i,h)}),f={clipPath:o?"url(#clipPath-".concat(u?"":"dots-").concat(t,")"):null};return p.createElement(ye,Dr({className:"recharts-line-dots",key:"dots"},f),l)}function Bo(e){var{clipPathId:t,pathRef:r,points:n,strokeDasharray:i,props:a,showLabels:o}=e,{type:u,layout:c,connectNulls:s,needClip:l}=a,f=Im(a,Bj),d=ut(ut({},z(f,!0)),{},{fill:"none",className:"recharts-line-curve",clipPath:l?"url(#clipPath-".concat(t,")"):null,points:n,type:u,layout:c,connectNulls:s,strokeDasharray:i??a.strokeDasharray});return p.createElement(p.Fragment,null,(n==null?void 0:n.length)>1&&p.createElement(Eu,Dr({},d,{pathRef:r})),p.createElement(Xj,{points:n,clipPathId:t,props:a}),o&&Lt.renderCallByParent(a,n))}function Zj(e){try{return e&&e.getTotalLength&&e.getTotalLength()||0}catch{return 0}}function Jj(e){var{clipPathId:t,props:r,pathRef:n,previousPointsRef:i,longestAnimatedLengthRef:a}=e,{points:o,strokeDasharray:u,isAnimationActive:c,animationBegin:s,animationDuration:l,animationEasing:f,animateNewValues:d,width:v,height:h,onAnimationEnd:y,onAnimationStart:m}=r,g=i.current,b=Wl(r,"recharts-line-"),[w,P]=p.useState(!1),x=p.useCallback(()=>{typeof y=="function"&&y(),P(!1)},[y]),O=p.useCallback(()=>{typeof m=="function"&&m(),P(!0)},[m]),A=Zj(n.current),E=a.current;return p.createElement(Bt,{begin:s,duration:l,isActive:c,easing:f,from:{t:0},to:{t:1},onAnimationEnd:x,onAnimationStart:O,key:b},_=>{var{t:D}=_,j=Be(E,A+E),C=Math.min(j(D),A),N;if(u){var R="".concat(u).split(/[,\s]+/gim).map(B=>parseFloat(B));N=Hj(C,A,R)}else N=$m(A,C);if(g){var F=g.length/o.length,G=D===1?o:o.map((B,fe)=>{var ae=Math.floor(fe*F);if(g[ae]){var De=g[ae],He=Be(De.x,B.x),L=Be(De.y,B.y);return ut(ut({},B),{},{x:He(D),y:L(D)})}if(d){var we=Be(v*2,B.x),Ht=Be(h/2,B.y);return ut(ut({},B),{},{x:we(D),y:Ht(D)})}return ut(ut({},B),{},{x:B.x,y:B.y})});return i.current=G,p.createElement(Bo,{props:r,points:G,clipPathId:t,pathRef:n,showLabels:!w,strokeDasharray:N})}return D>0&&A>0&&(i.current=o,a.current=C),p.createElement(Bo,{props:r,points:o,clipPathId:t,pathRef:n,showLabels:!w,strokeDasharray:N})})}function Qj(e){var{clipPathId:t,props:r}=e,{points:n,isAnimationActive:i}=r,a=p.useRef(null),o=p.useRef(0),u=p.useRef(null),c=a.current;return i&&n&&n.length&&c!==n?p.createElement(Jj,{props:r,clipPathId:t,previousPointsRef:a,longestAnimatedLengthRef:o,pathRef:u}):p.createElement(Bo,{props:r,points:n,clipPathId:t,pathRef:u,showLabels:!0})}var eC=(e,t)=>({x:e.x,y:e.y,value:e.value,errorVal:te(e.payload,t)});class tC extends p.Component{constructor(){super(...arguments),xa(this,"id",ur("recharts-line-"))}render(){var t,{hide:r,dot:n,points:i,className:a,xAxisId:o,yAxisId:u,top:c,left:s,width:l,height:f,id:d,needClip:v,layout:h}=this.props;if(r)return null;var y=U("recharts-line",a),m=X(d)?this.id:d,{r:g=3,strokeWidth:b=2}=(t=z(n,!1))!==null&&t!==void 0?t:{r:3,strokeWidth:2},w=Xf(n),P=g*2+b;return p.createElement(p.Fragment,null,p.createElement(ye,{className:y},v&&p.createElement("defs",null,p.createElement(bm,{clipPathId:m,xAxisId:o,yAxisId:u}),!w&&p.createElement("clipPath",{id:"clipPath-dots-".concat(m)},p.createElement("rect",{x:s-P/2,y:c-P/2,width:l+P,height:f+P}))),p.createElement(Qj,{props:this.props,clipPathId:m}),p.createElement(pm,{direction:h==="horizontal"?"y":"x"},p.createElement(fm,{xAxisId:o,yAxisId:u,data:i,dataPointFormatter:eC,errorBarOffset:0},this.props.children))),p.createElement(I_,{activeDot:this.props.activeDot,points:i,mainColor:this.props.stroke,itemDataKey:this.props.dataKey}))}}var Nm={activeDot:!0,animateNewValues:!0,animationBegin:0,animationDuration:1500,animationEasing:"ease",connectNulls:!1,dot:!0,fill:"#fff",hide:!1,isAnimationActive:!dr.isSsr,label:!1,legendType:"line",stroke:"#3182bd",strokeWidth:1,xAxisId:0,yAxisId:0};function rC(e){var t=at(e,Nm),{activeDot:r,animateNewValues:n,animationBegin:i,animationDuration:a,animationEasing:o,connectNulls:u,dot:c,hide:s,isAnimationActive:l,label:f,legendType:d,xAxisId:v,yAxisId:h}=t,y=Im(t,Kj),{needClip:m}=ql(v,h),{height:g,width:b,x:w,y:P}=Ul(),x=ea(),O=je(),A=p.useMemo(()=>({dataKey:e.dataKey,data:e.data}),[e.dataKey,e.data]),E=$(_=>Rj(_,v,h,O,A));return x!=="horizontal"&&x!=="vertical"?null:p.createElement(tC,Dr({},y,{connectNulls:u,dot:c,activeDot:r,animateNewValues:n,animationBegin:i,animationDuration:a,animationEasing:o,isAnimationActive:l,hide:s,label:f,legendType:d,xAxisId:v,yAxisId:h,points:E,layout:x,height:g,width:b,left:w,top:P,needClip:m}))}function nC(e){var{layout:t,xAxis:r,yAxis:n,xAxisTicks:i,yAxisTicks:a,dataKey:o,bandSize:u,displayedData:c}=e;return c.map((s,l)=>{var f=te(s,o);return t==="horizontal"?{x:Cc({axis:r,ticks:i,bandSize:u,entry:s,index:l}),y:X(f)?null:n.scale(f),value:f,payload:s}:{x:X(f)?null:r.scale(f),y:Cc({axis:n,ticks:a,bandSize:u,entry:s,index:l}),value:f,payload:s}})}class Lm extends p.PureComponent{render(){return p.createElement(dm,{type:"line",data:this.props.data,xAxisId:this.props.xAxisId,yAxisId:this.props.yAxisId,zAxisId:0,dataKey:this.props.dataKey,stackId:void 0,hide:this.props.hide,barSize:void 0},p.createElement(am,{legendPayload:Uj(this.props)}),p.createElement(Fl,{fn:qj,args:this.props}),p.createElement(rC,this.props))}}xa(Lm,"displayName","Line");xa(Lm,"defaultProps",Nm);function Lf(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Rf(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Lf(Object(r),!0).forEach(function(n){iC(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Lf(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function iC(e,t,r){return(t=aC(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function aC(e){var t=oC(e,"string");return typeof t=="symbol"?t:t+""}function oC(e,t){if(typeof e!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t||"default");if(typeof n!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var uC={xAxis:{},yAxis:{},zAxis:{}},Rm=it({name:"cartesianAxis",initialState:uC,reducers:{addXAxis(e,t){e.xAxis[t.payload.id]=t.payload},removeXAxis(e,t){delete e.xAxis[t.payload.id]},addYAxis(e,t){e.yAxis[t.payload.id]=t.payload},removeYAxis(e,t){delete e.yAxis[t.payload.id]},addZAxis(e,t){e.zAxis[t.payload.id]=t.payload},removeZAxis(e,t){delete e.zAxis[t.payload.id]},updateYAxisWidth(e,t){var{id:r,width:n}=t.payload;e.yAxis[r]&&(e.yAxis[r]=Rf(Rf({},e.yAxis[r]),{},{width:n}))}}}),{addXAxis:lC,removeXAxis:cC,addYAxis:sC,removeYAxis:fC,addZAxis:qk,removeZAxis:Yk,updateYAxisWidth:dC}=Rm.actions,vC=Rm.reducer,hC=["children"],pC=["dangerouslySetInnerHTML","ticks"];function Bm(e,t,r){return(t=mC(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function mC(e){var t=yC(e,"string");return typeof t=="symbol"?t:t+""}function yC(e,t){if(typeof e!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t||"default");if(typeof n!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function Ko(){return Ko=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Ko.apply(null,arguments)}function Km(e,t){if(e==null)return{};var r,n,i=gC(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],t.indexOf(r)===-1&&{}.propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}function gC(e,t){if(e==null)return{};var r={};for(var n in e)if({}.hasOwnProperty.call(e,n)){if(t.indexOf(n)!==-1)continue;r[n]=e[n]}return r}function bC(e){var t=ne(),r=p.useMemo(()=>{var a=Km(e,hC);return a},[e]),n=$(a=>Ct(a,r.id)),i=r===n;return p.useEffect(()=>(t(lC(r)),()=>{t(cC(r))}),[r,t]),i?e.children:null}var wC=e=>{var{xAxisId:t,className:r}=e,n=$(wv),i=je(),a="xAxis",o=$(f=>Kr(f,a,t,i)),u=$(f=>sp(f,a,t,i)),c=$(f=>op(f,t)),s=$(f=>FO(f,t));if(c==null||s==null)return null;var l=Km(e,pC);return p.createElement(Yt,Ko({},l,{scale:o,x:s.x,y:s.y,width:c.width,height:c.height,className:U("recharts-".concat(a," ").concat(a),r),viewBox:n,ticks:u}))},xC=e=>{var t,r,n,i,a;return p.createElement(bC,{interval:(t=e.interval)!==null&&t!==void 0?t:"preserveEnd",id:e.xAxisId,scale:e.scale,type:e.type,padding:e.padding,allowDataOverflow:e.allowDataOverflow,domain:e.domain,dataKey:e.dataKey,allowDuplicatedCategory:e.allowDuplicatedCategory,allowDecimals:e.allowDecimals,tickCount:e.tickCount,includeHidden:(r=e.includeHidden)!==null&&r!==void 0?r:!1,reversed:e.reversed,ticks:e.ticks,height:e.height,orientation:e.orientation,mirror:e.mirror,hide:e.hide,unit:e.unit,name:e.name,angle:(n=e.angle)!==null&&n!==void 0?n:0,minTickGap:(i=e.minTickGap)!==null&&i!==void 0?i:5,tick:(a=e.tick)!==null&&a!==void 0?a:!0,tickFormatter:e.tickFormatter},p.createElement(wC,e))};class zm extends p.Component{render(){return p.createElement(xC,this.props)}}Bm(zm,"displayName","XAxis");Bm(zm,"defaultProps",{allowDataOverflow:Ne.allowDataOverflow,allowDecimals:Ne.allowDecimals,allowDuplicatedCategory:Ne.allowDuplicatedCategory,height:Ne.height,hide:!1,mirror:Ne.mirror,orientation:Ne.orientation,padding:Ne.padding,reversed:Ne.reversed,scale:Ne.scale,tickCount:Ne.tickCount,type:Ne.type,xAxisId:0});var PC=e=>{var{ticks:t,label:r,labelGapWithTick:n=5,tickSize:i=0,tickMargin:a=0}=e,o=0;if(t){t.forEach(l=>{if(l){var f=l.getBoundingClientRect();f.width>o&&(o=f.width)}});var u=r?r.getBoundingClientRect().width:0,c=i+a,s=o+c+u+(r?n:0);return Math.round(s)}return 0},OC=["dangerouslySetInnerHTML","ticks"];function Fm(e,t,r){return(t=AC(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function AC(e){var t=SC(e,"string");return typeof t=="symbol"?t:t+""}function SC(e,t){if(typeof e!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t||"default");if(typeof n!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function zo(){return zo=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},zo.apply(null,arguments)}function EC(e,t){if(e==null)return{};var r,n,i=_C(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],t.indexOf(r)===-1&&{}.propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}function _C(e,t){if(e==null)return{};var r={};for(var n in e)if({}.hasOwnProperty.call(e,n)){if(t.indexOf(n)!==-1)continue;r[n]=e[n]}return r}function TC(e){var t=ne();return p.useEffect(()=>(t(sC(e)),()=>{t(fC(e))}),[e,t]),null}var jC=e=>{var t,{yAxisId:r,className:n,width:i,label:a}=e,o=p.useRef(null),u=p.useRef(null),c=$(wv),s=je(),l=ne(),f="yAxis",d=$(g=>Kr(g,f,r,s)),v=$(g=>up(g,r)),h=$(g=>WO(g,r)),y=$(g=>sp(g,f,r,s));if(p.useLayoutEffect(()=>{var g;if(!(i!=="auto"||!v||Il(a)||p.isValidElement(a))){var b=o.current,w=b==null||(g=b.tickRefs)===null||g===void 0?void 0:g.current,{tickSize:P,tickMargin:x}=b.props,O=PC({ticks:w,label:u.current,labelGapWithTick:5,tickSize:P,tickMargin:x});Math.round(v.width)!==Math.round(O)&&l(dC({id:r,width:O}))}},[o,o==null||(t=o.current)===null||t===void 0||(t=t.tickRefs)===null||t===void 0?void 0:t.current,v==null?void 0:v.width,v,l,a,r,i]),v==null||h==null)return null;var m=EC(e,OC);return p.createElement(Yt,zo({},m,{ref:o,labelRef:u,scale:d,x:h.x,y:h.y,width:v.width,height:v.height,className:U("recharts-".concat(f," ").concat(f),n),viewBox:c,ticks:y}))},CC=e=>{var t,r,n,i,a;return p.createElement(p.Fragment,null,p.createElement(TC,{interval:(t=e.interval)!==null&&t!==void 0?t:"preserveEnd",id:e.yAxisId,scale:e.scale,type:e.type,domain:e.domain,allowDataOverflow:e.allowDataOverflow,dataKey:e.dataKey,allowDuplicatedCategory:e.allowDuplicatedCategory,allowDecimals:e.allowDecimals,tickCount:e.tickCount,padding:e.padding,includeHidden:(r=e.includeHidden)!==null&&r!==void 0?r:!1,reversed:e.reversed,ticks:e.ticks,width:e.width,orientation:e.orientation,mirror:e.mirror,hide:e.hide,unit:e.unit,name:e.name,angle:(n=e.angle)!==null&&n!==void 0?n:0,minTickGap:(i=e.minTickGap)!==null&&i!==void 0?i:5,tick:(a=e.tick)!==null&&a!==void 0?a:!0,tickFormatter:e.tickFormatter}),p.createElement(jC,e))},kC={allowDataOverflow:Le.allowDataOverflow,allowDecimals:Le.allowDecimals,allowDuplicatedCategory:Le.allowDuplicatedCategory,hide:!1,mirror:Le.mirror,orientation:Le.orientation,padding:Le.padding,reversed:Le.reversed,scale:Le.scale,tickCount:Le.tickCount,type:Le.type,width:Le.width,yAxisId:0};class Wm extends p.Component{render(){return p.createElement(CC,this.props)}}Fm(Wm,"displayName","YAxis");Fm(Wm,"defaultProps",kC);var MC={};/**
 * @license React
 * use-sync-external-store-with-selector.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Tn=p;function DC(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var IC=typeof Object.is=="function"?Object.is:DC,$C=Tn.useSyncExternalStore,NC=Tn.useRef,LC=Tn.useEffect,RC=Tn.useMemo,BC=Tn.useDebugValue;MC.useSyncExternalStoreWithSelector=function(e,t,r,n,i){var a=NC(null);if(a.current===null){var o={hasValue:!1,value:null};a.current=o}else o=a.current;a=RC(function(){function c(v){if(!s){if(s=!0,l=v,v=n(v),i!==void 0&&o.hasValue){var h=o.value;if(i(h,v))return f=h}return f=v}if(h=f,IC(l,v))return h;var y=n(v);return i!==void 0&&i(h,y)?(l=v,h):(l=v,f=y)}var s=!1,l,f,d=r===void 0?null:r;return[function(){return c(t())},d===null?void 0:function(){return c(d())}]},[t,r,n,i]);var u=$C(e,a[0],a[1]);return LC(function(){o.hasValue=!0,o.value=u},[u]),BC(u),u};function KC(e){e()}function zC(){let e=null,t=null;return{clear(){e=null,t=null},notify(){KC(()=>{let r=e;for(;r;)r.callback(),r=r.next})},get(){const r=[];let n=e;for(;n;)r.push(n),n=n.next;return r},subscribe(r){let n=!0;const i=t={callback:r,next:null,prev:t};return i.prev?i.prev.next=i:e=i,function(){!n||e===null||(n=!1,i.next?i.next.prev=i.prev:t=i.prev,i.prev?i.prev.next=i.next:e=i.next)}}}}var Bf={notify(){},get:()=>[]};function FC(e,t){let r,n=Bf,i=0,a=!1;function o(y){l();const m=n.subscribe(y);let g=!1;return()=>{g||(g=!0,m(),f())}}function u(){n.notify()}function c(){h.onStateChange&&h.onStateChange()}function s(){return a}function l(){i++,r||(r=t?t.addNestedSub(c):e.subscribe(c),n=zC())}function f(){i--,r&&i===0&&(r(),r=void 0,n.clear(),n=Bf)}function d(){a||(a=!0,l())}function v(){a&&(a=!1,f())}const h={addNestedSub:o,notifyNestedSubs:u,handleChangeWrapper:c,isSubscribed:s,trySubscribe:d,tryUnsubscribe:v,getListeners:()=>n};return h}var WC=()=>typeof window<"u"&&typeof window.document<"u"&&typeof window.document.createElement<"u",UC=WC(),qC=()=>typeof navigator<"u"&&navigator.product==="ReactNative",YC=qC(),HC=()=>UC||YC?p.useLayoutEffect:p.useEffect,GC=HC(),Ya=Symbol.for("react-redux-context"),Ha=typeof globalThis<"u"?globalThis:{};function VC(){if(!p.createContext)return{};const e=Ha[Ya]??(Ha[Ya]=new Map);let t=e.get(p.createContext);return t||(t=p.createContext(null),e.set(p.createContext,t)),t}var XC=VC();function ZC(e){const{children:t,context:r,serverState:n,store:i}=e,a=p.useMemo(()=>{const c=FC(i);return{store:i,subscription:c,getServerState:n?()=>n:void 0}},[i,n]),o=p.useMemo(()=>i.getState(),[i]);GC(()=>{const{subscription:c}=a;return c.onStateChange=c.notifyNestedSubs,c.trySubscribe(),o!==i.getState()&&c.notifyNestedSubs(),()=>{c.tryUnsubscribe(),c.onStateChange=void 0}},[a,o]);const u=r||XC;return p.createElement(u.Provider,{value:a},t)}var JC=ZC,QC=(e,t)=>t,Xl=S([QC,Y,rO,be,Sp,kt,YA,ve],ZA),Zl=e=>{var t=e.currentTarget.getBoundingClientRect(),r=t.width/e.currentTarget.offsetWidth,n=t.height/e.currentTarget.offsetHeight;return{chartX:Math.round((e.clientX-t.left)/r),chartY:Math.round((e.clientY-t.top)/n)}},Um=tt("mouseClick"),qm=yn();qm.startListening({actionCreator:Um,effect:(e,t)=>{var r=e.payload,n=Xl(t.getState(),Zl(r));(n==null?void 0:n.activeIndex)!=null&&t.dispatch(rA({activeIndex:n.activeIndex,activeDataKey:void 0,activeCoordinate:n.activeCoordinate}))}});var Fo=tt("mouseMove"),Ym=yn();Ym.startListening({actionCreator:Fo,effect:(e,t)=>{var r=e.payload,n=t.getState(),i=Al(n,n.tooltip.settings.shared),a=Xl(n,Zl(r));i==="axis"&&((a==null?void 0:a.activeIndex)!=null?t.dispatch(gp({activeIndex:a.activeIndex,activeDataKey:void 0,activeCoordinate:a.activeCoordinate})):t.dispatch(yp()))}});function ek(e,t){return t instanceof HTMLElement?"HTMLElement <".concat(t.tagName,' class="').concat(t.className,'">'):t===window?"global.window":t}var Kf={accessibilityLayer:!0,barCategoryGap:"10%",barGap:4,barSize:void 0,className:void 0,maxBarSize:void 0,stackOffset:"none",syncId:void 0,syncMethod:"index"},Hm=it({name:"rootProps",initialState:Kf,reducers:{updateOptions:(e,t)=>{var r;e.accessibilityLayer=t.payload.accessibilityLayer,e.barCategoryGap=t.payload.barCategoryGap,e.barGap=(r=t.payload.barGap)!==null&&r!==void 0?r:Kf.barGap,e.barSize=t.payload.barSize,e.maxBarSize=t.payload.maxBarSize,e.stackOffset=t.payload.stackOffset,e.syncId=t.payload.syncId,e.syncMethod=t.payload.syncMethod,e.className=t.payload.className}}}),tk=Hm.reducer,{updateOptions:rk}=Hm.actions,Gm=it({name:"polarOptions",initialState:null,reducers:{updatePolarOptions:(e,t)=>t.payload}}),{updatePolarOptions:nk}=Gm.actions,ik=Gm.reducer,Vm=tt("keyDown"),Xm=tt("focus"),Jl=yn();Jl.startListening({actionCreator:Vm,effect:(e,t)=>{var r=t.getState(),n=r.rootProps.accessibilityLayer!==!1;if(n){var{keyboardInteraction:i}=r.tooltip,a=e.payload;if(!(a!=="ArrowRight"&&a!=="ArrowLeft"&&a!=="Enter")){var o=Number(Sl(i,yr(r))),u=kt(r);if(a==="Enter"){var c=yi(r,"axis","hover",String(i.index));t.dispatch(ko({active:!i.active,activeIndex:i.index,activeDataKey:i.dataKey,activeCoordinate:c}));return}var s=HO(r),l=s==="left-to-right"?1:-1,f=a==="ArrowRight"?1:-1,d=o+f*l;if(!(u==null||d>=u.length||d<0)){var v=yi(r,"axis","hover",String(d));t.dispatch(ko({active:!0,activeIndex:d.toString(),activeDataKey:void 0,activeCoordinate:v}))}}}}});Jl.startListening({actionCreator:Xm,effect:(e,t)=>{var r=t.getState(),n=r.rootProps.accessibilityLayer!==!1;if(n){var{keyboardInteraction:i}=r.tooltip;if(!i.active&&i.index==null){var a="0",o=yi(r,"axis","hover",String(a));t.dispatch(ko({activeDataKey:void 0,active:!0,activeIndex:a,activeCoordinate:o}))}}}});var Je=tt("externalEvent"),Zm=yn();Zm.startListening({actionCreator:Je,effect:(e,t)=>{if(e.payload.handler!=null){var r=t.getState(),n={activeCoordinate:NA(r),activeDataKey:jp(r),activeIndex:zt(r),activeLabel:Tp(r),activeTooltipIndex:zt(r),isTooltipActive:LA(r)};e.payload.handler(n,e.payload.reactEvent)}}});var ak=S([zr],e=>e.tooltipItemPayloads),ok=S([ak,_n,(e,t,r)=>t,(e,t,r)=>r],(e,t,r,n)=>{var i=e.find(u=>u.settings.dataKey===n);if(i!=null){var{positions:a}=i;if(a!=null){var o=t(a,r);return o}}}),Jm=tt("touchMove"),Qm=yn();Qm.startListening({actionCreator:Jm,effect:(e,t)=>{var r=e.payload,n=t.getState(),i=Al(n,n.tooltip.settings.shared);if(i==="axis"){var a=Xl(n,Zl({clientX:r.touches[0].clientX,clientY:r.touches[0].clientY,currentTarget:r.currentTarget}));(a==null?void 0:a.activeIndex)!=null&&t.dispatch(gp({activeIndex:a.activeIndex,activeDataKey:void 0,activeCoordinate:a.activeCoordinate}))}else if(i==="item"){var o,u=r.touches[0],c=document.elementFromPoint(u.clientX,u.clientY);if(!c||!c.getAttribute)return;var s=c.getAttribute(gv),l=(o=c.getAttribute(bv))!==null&&o!==void 0?o:void 0,f=ok(t.getState(),s,l);t.dispatch(mp({activeDataKey:l,activeIndex:s,activeCoordinate:f}))}}});var uk=Ud({brush:GT,cartesianAxis:vC,chartData:dS,graphicalItems:KE,layout:Q0,legend:Bb,options:uS,polarAxis:AE,polarOptions:ik,referenceElements:tj,rootProps:tk,tooltip:nA}),lk=function(t){var r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:"Chart";return A0({reducer:uk,preloadedState:t,middleware:n=>n({serializableCheck:!1}).concat([qm.middleware,Ym.middleware,Jl.middleware,Zm.middleware,Qm.middleware]),devTools:{serialize:{replacer:ek},name:"recharts-".concat(r)}})};function ey(e){var{preloadedState:t,children:r,reduxStoreName:n}=e,i=je(),a=p.useRef(null);if(i)return r;a.current==null&&(a.current=lk(t,n));var o=fu;return p.createElement(JC,{context:o,store:a.current},r)}function ty(e){var{layout:t,width:r,height:n,margin:i}=e,a=ne(),o=je();return p.useEffect(()=>{o||(a(X0(t)),a(Z0({width:r,height:n})),a(V0(i)))},[a,o,t,r,n,i]),null}function ry(e){var t=ne();return p.useEffect(()=>{t(rk(e))},[t,e]),null}var ck=["children"];function sk(e,t){if(e==null)return{};var r,n,i=fk(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],t.indexOf(r)===-1&&{}.propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}function fk(e,t){if(e==null)return{};var r={};for(var n in e)if({}.hasOwnProperty.call(e,n)){if(t.indexOf(n)!==-1)continue;r[n]=e[n]}return r}function Oi(){return Oi=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Oi.apply(null,arguments)}var dk={width:"100%",height:"100%"},vk=p.forwardRef((e,t)=>{var r=Pu(),n=Ou(),i=Sv();if(!Tr(r)||!Tr(n))return null;var{children:a,otherAttributes:o,title:u,desc:c}=e,s,l;return typeof o.tabIndex=="number"?s=o.tabIndex:s=i?0:void 0,typeof o.role=="string"?l=o.role:l=i?"application":void 0,p.createElement(Zo,Oi({},o,{title:u,desc:c,role:l,tabIndex:s,width:r,height:n,style:dk,ref:t}),a)}),hk=e=>{var{children:t}=e,r=$(Qi);if(!r)return null;var{width:n,height:i,y:a,x:o}=r;return p.createElement(Zo,{width:n,height:i,x:o,y:a},t)},zf=p.forwardRef((e,t)=>{var{children:r}=e,n=sk(e,ck),i=je();return i?p.createElement(hk,null,r):p.createElement(vk,Oi({ref:t},n),r)});function pk(){var e=ne(),[t,r]=p.useState(null),n=$(_b);return p.useEffect(()=>{if(t!=null){var i=t.getBoundingClientRect(),a=i.width/t.offsetWidth;Ye(a)&&a!==n&&e(J0(a))}},[t,e,n]),r}function Ff(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function mk(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Ff(Object(r),!0).forEach(function(n){yk(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Ff(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function yk(e,t,r){return(t=gk(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function gk(e){var t=bk(e,"string");return typeof t=="symbol"?t:t+""}function bk(e,t){if(typeof e!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t||"default");if(typeof n!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var wk=p.forwardRef((e,t)=>{var{children:r,className:n,height:i,onClick:a,onContextMenu:o,onDoubleClick:u,onMouseDown:c,onMouseEnter:s,onMouseLeave:l,onMouseMove:f,onMouseUp:d,onTouchEnd:v,onTouchMove:h,onTouchStart:y,style:m,width:g}=e,b=ne(),[w,P]=p.useState(null),[x,O]=p.useState(null);pS();var A=pk(),E=p.useCallback(L=>{A(L),typeof t=="function"&&t(L),P(L),O(L)},[A,t,P,O]),_=p.useCallback(L=>{b(Um(L)),b(Je({handler:a,reactEvent:L}))},[b,a]),D=p.useCallback(L=>{b(Fo(L)),b(Je({handler:s,reactEvent:L}))},[b,s]),j=p.useCallback(L=>{b(yp()),b(Je({handler:l,reactEvent:L}))},[b,l]),C=p.useCallback(L=>{b(Fo(L)),b(Je({handler:f,reactEvent:L}))},[b,f]),N=p.useCallback(()=>{b(Xm())},[b]),R=p.useCallback(L=>{b(Vm(L.key))},[b]),F=p.useCallback(L=>{b(Je({handler:o,reactEvent:L}))},[b,o]),G=p.useCallback(L=>{b(Je({handler:u,reactEvent:L}))},[b,u]),B=p.useCallback(L=>{b(Je({handler:c,reactEvent:L}))},[b,c]),fe=p.useCallback(L=>{b(Je({handler:d,reactEvent:L}))},[b,d]),ae=p.useCallback(L=>{b(Je({handler:y,reactEvent:L}))},[b,y]),De=p.useCallback(L=>{b(Jm(L)),b(Je({handler:h,reactEvent:L}))},[b,h]),He=p.useCallback(L=>{b(Je({handler:v,reactEvent:L}))},[b,v]);return p.createElement($p.Provider,{value:w},p.createElement(Zf.Provider,{value:x},p.createElement("div",{className:U("recharts-wrapper",n),style:mk({position:"relative",cursor:"default",width:g,height:i},m),onClick:_,onContextMenu:F,onDoubleClick:G,onFocus:N,onKeyDown:R,onMouseDown:B,onMouseEnter:D,onMouseLeave:j,onMouseMove:C,onMouseUp:fe,onTouchEnd:He,onTouchMove:De,onTouchStart:ae,ref:E},r)))}),xk=["children","className","width","height","style","compact","title","desc"];function Pk(e,t){if(e==null)return{};var r,n,i=Ok(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],t.indexOf(r)===-1&&{}.propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}function Ok(e,t){if(e==null)return{};var r={};for(var n in e)if({}.hasOwnProperty.call(e,n)){if(t.indexOf(n)!==-1)continue;r[n]=e[n]}return r}var ny=p.forwardRef((e,t)=>{var{children:r,className:n,width:i,height:a,style:o,compact:u,title:c,desc:s}=e,l=Pk(e,xk),f=z(l,!1);return u?p.createElement(zf,{otherAttributes:f,title:c,desc:s},r):p.createElement(wk,{className:n,style:o,width:i,height:a,onClick:e.onClick,onMouseLeave:e.onMouseLeave,onMouseEnter:e.onMouseEnter,onMouseMove:e.onMouseMove,onMouseDown:e.onMouseDown,onMouseUp:e.onMouseUp,onContextMenu:e.onContextMenu,onDoubleClick:e.onDoubleClick,onTouchStart:e.onTouchStart,onTouchMove:e.onTouchMove,onTouchEnd:e.onTouchEnd},p.createElement(zf,{otherAttributes:f,title:c,desc:s,ref:t},p.createElement(nj,null,r)))}),Ak=["width","height"];function Wo(){return Wo=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Wo.apply(null,arguments)}function Sk(e,t){if(e==null)return{};var r,n,i=Ek(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],t.indexOf(r)===-1&&{}.propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}function Ek(e,t){if(e==null)return{};var r={};for(var n in e)if({}.hasOwnProperty.call(e,n)){if(t.indexOf(n)!==-1)continue;r[n]=e[n]}return r}var _k={top:5,right:5,bottom:5,left:5},Tk={accessibilityLayer:!0,layout:"horizontal",stackOffset:"none",barCategoryGap:"10%",barGap:4,margin:_k,reverseStackOrder:!1,syncMethod:"index"},iy=p.forwardRef(function(t,r){var n,i=at(t.categoricalChartProps,Tk),{width:a,height:o}=i,u=Sk(i,Ak);if(!Tr(a)||!Tr(o))return null;var{chartName:c,defaultTooltipEventType:s,validateTooltipEventTypes:l,tooltipPayloadSearcher:f,categoricalChartProps:d}=t,v={chartName:c,defaultTooltipEventType:s,validateTooltipEventTypes:l,tooltipPayloadSearcher:f,eventEmitter:void 0};return p.createElement(ey,{preloadedState:{options:v},reduxStoreName:(n=d.id)!==null&&n!==void 0?n:c},p.createElement(Sm,{chartData:d.data}),p.createElement(ty,{width:a,height:o,layout:i.layout,margin:i.margin}),p.createElement(ry,{accessibilityLayer:i.accessibilityLayer,barCategoryGap:i.barCategoryGap,maxBarSize:i.maxBarSize,stackOffset:i.stackOffset,barGap:i.barGap,barSize:i.barSize,syncId:i.syncId,syncMethod:i.syncMethod,className:i.className}),p.createElement(ny,Wo({},u,{width:a,height:o,ref:r})))}),jk=["axis"],Hk=p.forwardRef((e,t)=>p.createElement(iy,{chartName:"LineChart",defaultTooltipEventType:"axis",validateTooltipEventTypes:jk,tooltipPayloadSearcher:Dl,categoricalChartProps:e,ref:t})),Ck=["axis","item"],Gk=p.forwardRef((e,t)=>p.createElement(iy,{chartName:"BarChart",defaultTooltipEventType:"axis",validateTooltipEventTypes:Ck,tooltipPayloadSearcher:Dl,categoricalChartProps:e,ref:t}));function kk(e){var t=ne();return p.useEffect(()=>{t(nk(e))},[t,e]),null}var Mk=["width","height","layout"];function Uo(){return Uo=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Uo.apply(null,arguments)}function Dk(e,t){if(e==null)return{};var r,n,i=Ik(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],t.indexOf(r)===-1&&{}.propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}function Ik(e,t){if(e==null)return{};var r={};for(var n in e)if({}.hasOwnProperty.call(e,n)){if(t.indexOf(n)!==-1)continue;r[n]=e[n]}return r}var $k={top:5,right:5,bottom:5,left:5},Nk={accessibilityLayer:!0,stackOffset:"none",barCategoryGap:"10%",barGap:4,margin:$k,reverseStackOrder:!1,syncMethod:"index",layout:"radial"},Lk=p.forwardRef(function(t,r){var n,i=at(t.categoricalChartProps,Nk),{width:a,height:o,layout:u}=i,c=Dk(i,Mk);if(!Tr(a)||!Tr(o))return null;var{chartName:s,defaultTooltipEventType:l,validateTooltipEventTypes:f,tooltipPayloadSearcher:d}=t,v={chartName:s,defaultTooltipEventType:l,validateTooltipEventTypes:f,tooltipPayloadSearcher:d,eventEmitter:void 0};return p.createElement(ey,{preloadedState:{options:v},reduxStoreName:(n=i.id)!==null&&n!==void 0?n:s},p.createElement(Sm,{chartData:i.data}),p.createElement(ty,{width:a,height:o,layout:u,margin:i.margin}),p.createElement(ry,{accessibilityLayer:i.accessibilityLayer,barCategoryGap:i.barCategoryGap,maxBarSize:i.maxBarSize,stackOffset:i.stackOffset,barGap:i.barGap,barSize:i.barSize,syncId:i.syncId,syncMethod:i.syncMethod,className:i.className}),p.createElement(kk,{cx:i.cx,cy:i.cy,startAngle:i.startAngle,endAngle:i.endAngle,innerRadius:i.innerRadius,outerRadius:i.outerRadius}),p.createElement(ny,Uo({width:a,height:o},c,{ref:r})))}),Rk=["item"],Bk={layout:"centric",startAngle:0,endAngle:360,cx:"50%",cy:"50%",innerRadius:0,outerRadius:"80%"},Vk=p.forwardRef((e,t)=>{var r=at(e,Bk);return p.createElement(Lk,{chartName:"PieChart",defaultTooltipEventType:"item",validateTooltipEventTypes:Rk,tooltipPayloadSearcher:Dl,categoricalChartProps:r,ref:t})});export{Gk as B,Ij as C,Lt as L,Vk as P,Uk as R,Nv as S,Wk as T,zm as X,Wm as Y,Om as a,Hk as b,Lm as c,lm as d,ya as e,Su as f};
