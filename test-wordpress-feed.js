#!/usr/bin/env node

// Test script to check WordPress.org RSS feeds directly
import fetch from "node-fetch";
import { parseString } from "xml2js";
import { promisify } from "util";

const parseXML = promisify(parseString);

async function testWordPressFeed(pluginSlug) {
  try {
    console.log(`🔍 Testing WordPress.org RSS feed for: ${pluginSlug}`);

    const feedUrl = `https://wordpress.org/support/plugin/${pluginSlug}/reviews/feed/`;
    console.log(`📡 Fetching: ${feedUrl}`);

    const response = await fetch(feedUrl, {
      headers: {
        "User-Agent":
          "PluginInsight-Test/1.0 (WordPress Plugin Analytics Tool)",
      },
      timeout: 30000,
    });

    if (!response.ok) {
      throw new Error(
        `Feed request failed: ${response.status} ${response.statusText}`
      );
    }

    const xmlData = await response.text();
    console.log(`📄 Received XML data length: ${xmlData.length} characters`);

    const parsedData = await parseXML(xmlData);

    if (
      parsedData &&
      parsedData.rss &&
      parsedData.rss.channel &&
      parsedData.rss.channel[0].item
    ) {
      const items = parsedData.rss.channel[0].item;
      console.log(`✅ Found ${items.length} reviews in RSS feed`);

      // Show details of first few reviews
      const sampleSize = Math.min(3, items.length);
      console.log(`📝 Sample reviews (first ${sampleSize}):`);

      for (let i = 0; i < sampleSize; i++) {
        const item = items[i];
        const title = item.title ? item.title[0] : "No title";
        const author = item["dc:creator"] ? item["dc:creator"][0] : "Anonymous";
        const date = item.pubDate ? item.pubDate[0] : "No date";
        const guid = item.guid ? item.guid[0] : "No guid";

        console.log(`  ${i + 1}. Title: ${title}`);
        console.log(`     Author: ${author}`);
        console.log(`     Date: ${date}`);
        console.log(`     GUID: ${guid}`);
        console.log("");
      }

      return {
        success: true,
        totalReviews: items.length,
        sampleReviews: items.slice(0, sampleSize),
      };
    } else {
      console.log(
        "⚠️ No reviews found in RSS feed or feed structure is unexpected"
      );
      return {
        success: false,
        totalReviews: 0,
        error: "No reviews found or unexpected feed structure",
      };
    }
  } catch (error) {
    console.error(`❌ Error testing feed for ${pluginSlug}:`, error.message);
    return {
      success: false,
      error: error.message,
    };
  }
}

async function testMultiplePlugins() {
  const plugins = ["betterlinks", "betterdocs", "embedpress"];

  console.log("🧪 Testing WordPress.org RSS feeds for multiple plugins...\n");

  for (const plugin of plugins) {
    const result = await testWordPressFeed(plugin);
    console.log(`Results for ${plugin}:`, result);
    console.log("---\n");

    // Add delay to avoid rate limiting
    await new Promise((resolve) => setTimeout(resolve, 1000));
  }
}

// Test individual plugin
const pluginToTest = process.argv[2] || "betterlinks";
testWordPressFeed(pluginToTest);
