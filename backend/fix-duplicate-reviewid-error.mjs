#!/usr/bin/env node

/**
 * <PERSON><PERSON><PERSON> to fix the MongoDB duplicate key error for reviewId field
 *
 * This script:
 * 1. Drops the incorrect reviewId_1 unique index that was created at document level
 * 2. Cleans up any documents with null reviewId fields at document level
 * 3. Ensures proper reviewId generation in embedded reviews array
 * 4. Validates existing reviews have proper reviewId format
 */

import mongoose from "mongoose";
import dotenv from "dotenv";

// Load environment variables
dotenv.config();

// Use environment variable or fallback to local
const MONGODB_URI =
  process.env.MONGODB_URI || "mongodb://localhost:27017/wpdev_plugin";

// Function to generate proper reviewId
function generateReviewId(pluginSlug, author, datePublished) {
  // Clean author name (remove special characters, limit length)
  const cleanAuthor = (author || "anonymous")
    .toLowerCase()
    .replace(/[^a-z0-9]/g, "")
    .substring(0, 15);

  // Format date as YYYY-MM-DD
  const date = new Date(datePublished);
  const dateStr = date.toISOString().split("T")[0];

  // Add timestamp to ensure uniqueness
  const timestamp = Date.now().toString().slice(-6);

  return `${pluginSlug}-${cleanAuthor}-${dateStr}-${timestamp}`;
}

async function fixDuplicateReviewIdError() {
  console.log("🔧 Fixing MongoDB Duplicate ReviewId Error");
  console.log("=".repeat(60));
  console.log(
    `🔗 Connecting to: ${
      MONGODB_URI.includes("mongodb+srv")
        ? "MongoDB Atlas (Production)"
        : "Local MongoDB"
    }`
  );

  try {
    // Configure connection options for both local and production
    const options = {
      dbName: "wpdev_plugin",
      serverSelectionTimeoutMS: 10000,
      connectTimeoutMS: 10000,
    };

    // Connect to MongoDB
    await mongoose.connect(MONGODB_URI, options);
    console.log("✅ Connected to MongoDB");
    console.log(`📊 Database: ${mongoose.connection.db.databaseName}`);

    const db = mongoose.connection.db;
    const reviewsCollection = db.collection("pluginreviews");

    // Step 1: Check current indexes
    console.log("\n📊 Step 1: Checking current indexes");
    const indexes = await reviewsCollection.indexes();
    console.log("Current indexes:");
    indexes.forEach((index, i) => {
      console.log(`${i + 1}. ${index.name}: ${JSON.stringify(index.key)}`);
    });

    // Step 2: Drop problematic indexes
    console.log("\n🗑️ Step 2: Dropping problematic indexes");
    const problematicIndexes = [
      "reviewId_1",
      "rating_1",
      "date_1",
      "pluginSlug_1_date_-1",
      "pluginSlug_1_rating_1",
      "pluginSlug_1_date_-1_rating_1",
      "date_-1",
    ];

    let droppedIndexes = [];
    let indexErrors = [];

    for (const indexName of problematicIndexes) {
      const indexExists = indexes.find((index) => index.name === indexName);
      if (indexExists) {
        console.log(`🗑️ Dropping incorrect index: ${indexName}`);
        try {
          await reviewsCollection.dropIndex(indexName);
          droppedIndexes.push(indexName);
          console.log(`✅ Successfully dropped ${indexName} index`);
        } catch (error) {
          indexErrors.push({ index: indexName, error: error.message });
          console.log(`⚠️ Could not drop ${indexName}: ${error.message}`);
        }
      } else {
        console.log(`ℹ️ Index ${indexName} does not exist (OK)`);
      }
    }

    // Step 3: Check for documents with null reviewId fields at document level
    console.log(
      "\n🔍 Step 3: Checking for documents with null reviewId fields at document level"
    );
    const docsWithNullReviewId = await reviewsCollection.countDocuments({
      reviewId: null,
    });

    if (docsWithNullReviewId > 0) {
      console.log(
        `⚠️ Found ${docsWithNullReviewId} documents with null reviewId at document level`
      );
      console.log(
        "This is the cause of the duplicate key error - removing these fields..."
      );

      // Remove the reviewId field from documents where it exists at document level
      const removeResult = await reviewsCollection.updateMany(
        { reviewId: { $exists: true } },
        { $unset: { reviewId: "" } }
      );
      console.log(
        `✅ Removed reviewId field from ${removeResult.modifiedCount} documents`
      );
    } else {
      console.log("✅ No documents with null reviewId found at document level");
    }

    // Step 4: Validate and fix reviewId in embedded reviews array
    console.log("\n🔍 Step 4: Validating reviewId in embedded reviews");
    const documentsWithReviews = await reviewsCollection
      .find({
        "reviews.0": { $exists: true },
      })
      .toArray();

    console.log(`Found ${documentsWithReviews.length} documents with reviews`);

    let fixedReviews = 0;
    let totalReviews = 0;

    for (const doc of documentsWithReviews) {
      if (!Array.isArray(doc.reviews)) continue;

      let docModified = false;

      for (let i = 0; i < doc.reviews.length; i++) {
        const review = doc.reviews[i];
        totalReviews++;

        // Check if reviewId is missing, null, or invalid
        if (
          !review.reviewId ||
          typeof review.reviewId !== "string" ||
          !review.reviewId.trim()
        ) {
          // Generate new reviewId using the proper format
          const newReviewId = generateReviewId(
            doc.pluginSlug,
            review.author || "anonymous",
            review.date || new Date()
          );

          doc.reviews[i].reviewId = newReviewId;
          docModified = true;
          fixedReviews++;

          console.log(`  Fixed reviewId for ${doc.pluginSlug}: ${newReviewId}`);
        }
      }

      // Save the document if it was modified
      if (docModified) {
        await reviewsCollection.updateOne(
          { _id: doc._id },
          { $set: { reviews: doc.reviews } }
        );
      }
    }

    console.log(
      `✅ Fixed ${fixedReviews} reviews out of ${totalReviews} total reviews`
    );

    // Step 5: Verify collection structure
    console.log("\n📋 Step 5: Verifying collection structure");
    const sampleDoc = await reviewsCollection.findOne();
    if (sampleDoc) {
      console.log("Sample document structure:");
      console.log("- _id:", typeof sampleDoc._id);
      console.log("- pluginSlug:", typeof sampleDoc.pluginSlug);
      console.log("- pluginName:", typeof sampleDoc.pluginName);
      console.log(
        "- reviews:",
        Array.isArray(sampleDoc.reviews)
          ? `array with ${sampleDoc.reviews.length} items`
          : typeof sampleDoc.reviews
      );
      console.log("- totalReviews:", typeof sampleDoc.totalReviews);
      console.log("- averageRating:", typeof sampleDoc.averageRating);

      if (Array.isArray(sampleDoc.reviews) && sampleDoc.reviews.length > 0) {
        const firstReview = sampleDoc.reviews[0];
        console.log("- First review structure:");
        console.log(
          "  - reviewId:",
          firstReview.reviewId ? "✅ Present" : "❌ Missing"
        );
        console.log("  - author:", firstReview.author || "N/A");
        console.log("  - rating:", firstReview.rating || "N/A");
      }
    }

    // Step 5: Show final indexes
    console.log("\n📊 Step 5: Final indexes after cleanup");
    const finalIndexes = await reviewsCollection.indexes();
    console.log("Remaining indexes:");
    finalIndexes.forEach((index, i) => {
      console.log(`${i + 1}. ${index.name}: ${JSON.stringify(index.key)}`);
    });

    // Step 6: Summary
    console.log("\n📋 Summary:");
    console.log(`✅ Dropped ${droppedIndexes.length} problematic indexes`);
    if (droppedIndexes.length > 0) {
      console.log(`   - ${droppedIndexes.join(", ")}`);
    }

    if (indexErrors.length > 0) {
      console.log(`⚠️ ${indexErrors.length} index errors occurred`);
      indexErrors.forEach((err) => {
        console.log(`   - ${err.index}: ${err.error}`);
      });
    }

    console.log(
      `✅ Fixed ${fixedReviews} reviews with missing/invalid reviewId`
    );
    console.log(`📊 Total reviews processed: ${totalReviews}`);

    console.log("\n🎉 Fix completed successfully!");
    console.log("✅ Removed problematic reviewId index at document level");
    console.log("✅ Fixed all reviews with missing/invalid reviewId");
    console.log(
      "✅ You should now be able to insert reviews without duplicate key errors."
    );
  } catch (error) {
    console.error("❌ Fix failed with error:", error.message);
    console.error("Full error:", error);
  } finally {
    await mongoose.disconnect();
    console.log("\n✅ Disconnected from MongoDB");
  }
}

// Run the fix
fixDuplicateReviewIdError();
