import express from "express";
import cors from "cors";
import dotenv from "dotenv";
import connectDB from "./config/database.js";
import authRoutes from "./routes/auth.js";
import pluginRoutes from "./routes/plugins.js";
import userRoutes from "./routes/users.js";
import keywordRoutes from "./routes/keywords.js";
import competitorRoutes from "./routes/competitors.js";
import analyticsRoutes from "./routes/analytics.js";
import settingsRoutes from "./routes/settings.js";
import pluginScheduler from "./services/scheduler.js";

dotenv.config();

const app = express();

// Connect to MongoDB and start scheduler after connection
const initializeApp = async () => {
  try {
    await connectDB();
    console.log("🚀 Database connection established, starting scheduler...");

    // Start the plugin auto-fetch scheduler after DB connection
    pluginScheduler.start();
  } catch (error) {
    console.error("❌ Failed to initialize application:", error);
    process.exit(1);
  }
};
const PORT = process.env.PORT || 5001;

// Middleware
const allowedOrigins = [
  "http://localhost:5173",
  "http://127.0.0.1:5173",
  process.env.FRONTEND_URL, // Production frontend URL
].filter(Boolean); // Remove undefined values

app.use(
  cors({
    origin: allowedOrigins,
    credentials: true,
    methods: ["GET", "POST", "PUT", "DELETE", "OPTIONS"],
    allowedHeaders: ["Content-Type", "Authorization"],
  })
);
// Configure body parser with increased limit for plugin data
app.use(express.json({ limit: "1mb" }));
app.use(express.urlencoded({ limit: "1mb", extended: true }));

// Serve static files for uploads
app.use("/uploads", express.static("uploads"));

// Global error handler for payload size errors
app.use((error, req, res, next) => {
  if (error.type === "entity.too.large") {
    console.error("Payload too large error:", {
      expected: error.expected,
      length: error.length,
      limit: error.limit,
      url: req.url,
      method: req.method,
    });

    return res.status(413).json({
      success: false,
      message:
        "Request payload is too large. Please reduce the data size and try again.",
      error: "Payload too large",
      details: {
        limit: error.limit,
        received: error.length,
      },
    });
  }
  next(error);
});

// MongoDB connection established above

// Routes
app.use("/api/auth", authRoutes);
app.use("/api/plugins", pluginRoutes);
app.use("/api/users", userRoutes);
app.use("/api/keywords", keywordRoutes);
app.use("/api/competitors", competitorRoutes);
app.use("/api/analytics", analyticsRoutes);
app.use("/api/settings", settingsRoutes);

// Health check endpoint
app.get("/api/health", (req, res) => {
  res.json({ status: "OK", message: "Server is running with MongoDB" });
});

// Scheduler status endpoint
app.get("/api/scheduler/status", (req, res) => {
  res.json({
    success: true,
    status: pluginScheduler.getStatus(),
  });
});

// Manual trigger endpoint for keyword refresh
app.post("/api/scheduler/trigger-keyword-refresh", async (req, res) => {
  try {
    console.log("🔧 Manual keyword refresh trigger requested");
    await pluginScheduler.triggerManualKeywordRefresh();
    res.json({
      success: true,
      message: "Keyword refresh triggered successfully",
    });
  } catch (error) {
    console.error("❌ Keyword refresh trigger error:", error);
    res.status(500).json({
      success: false,
      message: "Failed to trigger keyword refresh",
      error: error.message,
    });
  }
});

app.listen(PORT, () => {
  console.log(`Server running on port ${PORT}`);

  // Initialize database connection and scheduler
  initializeApp();
});
