import mongoose from "mongoose";
import PluginKeywordRank from "../models/PluginKeywordRank.js";

/**
 * Script to update existing PluginKeywordRank records to have a source field
 * This is needed for existing records that were created before the source field was added
 */
async function updateKeywordRanksSource() {
  try {
    console.log("🚀 Starting keyword ranks source field update...");
    
    // Connect to MongoDB
    const mongoUri = process.env.MONGODB_URI || "mongodb://localhost:27017/wpdev_plugin";
    await mongoose.connect(mongoUri);
    console.log("✅ Connected to MongoDB");
    
    // Find all records without a source field or with null/undefined source
    const recordsToUpdate = await PluginKeywordRank.find({
      $or: [
        { source: { $exists: false } },
        { source: null },
        { source: undefined }
      ]
    });
    
    console.log(`📊 Found ${recordsToUpdate.length} records without source field`);
    
    if (recordsToUpdate.length === 0) {
      console.log("✅ All records already have source field set");
      return;
    }
    
    let updatedCount = 0;
    let errorCount = 0;
    
    for (const record of recordsToUpdate) {
      try {
        // Set default source to "default" for existing records
        // These are likely keywords that came from plugin tags originally
        record.source = "default";
        await record.save();
        
        updatedCount++;
        console.log(`✅ Updated source for keyword: ${record.keyword} (plugin: ${record.pluginSlug})`);
        
      } catch (error) {
        errorCount++;
        console.error(`❌ Error updating keyword ${record.keyword} for plugin ${record.pluginSlug}:`, error.message);
      }
    }
    
    console.log(`\n📈 Update Summary:`);
    console.log(`   ✅ Successfully updated: ${updatedCount} records`);
    console.log(`   ❌ Errors: ${errorCount} records`);
    console.log(`   📊 Total processed: ${recordsToUpdate.length} records`);
    
    if (updatedCount > 0) {
      console.log(`\n🎉 Source field update completed successfully!`);
    }
    
  } catch (error) {
    console.error("💥 Update failed:", error);
  } finally {
    await mongoose.disconnect();
    console.log("🔌 Disconnected from MongoDB");
  }
}

// Run update if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  updateKeywordRanksSource();
}

export default updateKeywordRanksSource;
