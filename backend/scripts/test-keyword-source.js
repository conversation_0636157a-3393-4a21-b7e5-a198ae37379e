import mongoose from "mongoose";
import PluginKeyword from "../models/PluginKeyword.js";
import PluginKeywordRank from "../models/PluginKeywordRank.js";

/**
 * Test script to verify that the source field is working correctly
 * when keywords are transferred from pluginkeywords to pluginkeywordranks
 */
async function testKeywordSource() {
  try {
    console.log("🧪 Starting keyword source field test...");
    
    // Connect to MongoDB
    const mongoUri = process.env.MONGODB_URI || "mongodb://localhost:27017/wpdev_plugin";
    await mongoose.connect(mongoUri);
    console.log("✅ Connected to MongoDB");
    
    const testPluginSlug = "test-plugin-source";
    const testPluginName = "Test Plugin Source";
    
    // Clean up any existing test data
    await PluginKeyword.deleteMany({ pluginSlug: testPluginSlug });
    await PluginKeywordRank.deleteMany({ pluginSlug: testPluginSlug });
    console.log("🧹 Cleaned up existing test data");
    
    // Test 1: Create keywords with different sources in pluginkeywords collection
    console.log("\n📝 Test 1: Creating keywords with different sources...");
    
    const keywordsData = {
      "wordpress": {
        keyword: "wordpress",
        addedAt: new Date(),
        source: "default",
        type: "plugin_tags"
      },
      "plugin": {
        keyword: "plugin",
        addedAt: new Date(),
        source: "default", 
        type: "plugin_tags"
      },
      "custom-keyword": {
        keyword: "custom keyword",
        addedAt: new Date(),
        source: "manual",
        type: "plugin_tags"
      }
    };
    
    const keywordDoc = await PluginKeyword.upsertPluginKeywords(
      testPluginSlug,
      testPluginName,
      keywordsData
    );
    
    console.log(`✅ Created keyword document with ${Object.keys(keywordsData).length} keywords`);
    
    // Test 2: Transfer keywords to pluginkeywordranks using updateKeywordRank
    console.log("\n🔄 Test 2: Transferring keywords to pluginkeywordranks...");
    
    const testDate = "17-01-2025";
    const testRanks = {
      "wordpress": 5,
      "plugin": 12,
      "custom-keyword": 8
    };
    
    for (const [keyword, rank] of Object.entries(testRanks)) {
      try {
        await PluginKeyword.updateKeywordRank(
          testPluginSlug,
          keyword,
          rank,
          testDate
        );
        console.log(`✅ Updated rank for keyword: ${keyword} (rank: ${rank})`);
      } catch (error) {
        console.error(`❌ Error updating rank for ${keyword}:`, error.message);
      }
    }
    
    // Test 3: Verify source fields in pluginkeywordranks collection
    console.log("\n🔍 Test 3: Verifying source fields in pluginkeywordranks...");
    
    const rankRecords = await PluginKeywordRank.find({ pluginSlug: testPluginSlug });
    
    console.log(`Found ${rankRecords.length} rank records:`);
    
    let testsPassed = 0;
    let testsFailed = 0;
    
    for (const record of rankRecords) {
      const originalKeyword = keywordsData[record.keyword.toLowerCase()];
      const expectedSource = originalKeyword ? originalKeyword.source : "default";
      
      if (record.source === expectedSource) {
        console.log(`✅ ${record.keyword}: source = ${record.source} (correct)`);
        testsPassed++;
      } else {
        console.log(`❌ ${record.keyword}: source = ${record.source}, expected = ${expectedSource}`);
        testsFailed++;
      }
    }
    
    // Test 4: Test manual keyword addition
    console.log("\n➕ Test 4: Testing manual keyword addition...");
    
    const manualRankRecord = await PluginKeywordRank.upsertKeywordRank(
      testPluginSlug,
      testPluginName,
      "manual-test-keyword",
      15,
      testDate,
      "manual"
    );
    
    if (manualRankRecord.source === "manual") {
      console.log(`✅ Manual keyword: source = ${manualRankRecord.source} (correct)`);
      testsPassed++;
    } else {
      console.log(`❌ Manual keyword: source = ${manualRankRecord.source}, expected = manual`);
      testsFailed++;
    }
    
    // Test 5: Test getUserKeywordsFromRanks returns correct source
    console.log("\n📊 Test 5: Testing getUserKeywordsFromRanks source field...");
    
    // This would require a user and added plugin, so we'll just check the records directly
    const allRankRecords = await PluginKeywordRank.find({ pluginSlug: testPluginSlug });
    
    for (const record of allRankRecords) {
      if (record.source && ["default", "manual", "migrated"].includes(record.source)) {
        console.log(`✅ ${record.keyword}: has valid source = ${record.source}`);
        testsPassed++;
      } else {
        console.log(`❌ ${record.keyword}: invalid source = ${record.source}`);
        testsFailed++;
      }
    }
    
    // Clean up test data
    console.log("\n🧹 Cleaning up test data...");
    await PluginKeyword.deleteMany({ pluginSlug: testPluginSlug });
    await PluginKeywordRank.deleteMany({ pluginSlug: testPluginSlug });
    
    // Summary
    console.log(`\n📈 Test Summary:`);
    console.log(`   ✅ Tests passed: ${testsPassed}`);
    console.log(`   ❌ Tests failed: ${testsFailed}`);
    console.log(`   📊 Total tests: ${testsPassed + testsFailed}`);
    
    if (testsFailed === 0) {
      console.log(`\n🎉 All tests passed! Source field functionality is working correctly.`);
    } else {
      console.log(`\n⚠️  Some tests failed. Please check the implementation.`);
    }
    
  } catch (error) {
    console.error("💥 Test failed:", error);
  } finally {
    await mongoose.disconnect();
    console.log("🔌 Disconnected from MongoDB");
  }
}

// Run test if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  testKeywordSource();
}

export default testKeywordSource;
