#!/usr/bin/env node

/**
 * Test script to verify the reviewId duplicate key error fix
 */

import mongoose from "mongoose";
import PluginReview from "./models/PluginReview.js";
import dotenv from "dotenv";

// Load environment variables
dotenv.config();

const MONGODB_URI =
  process.env.MONGODB_URI || "mongodb://localhost:27017/wpdev_plugin";

// Function to generate proper reviewId (same as in routes/plugins.js)
function generateReviewId(pluginSlug, author = "", datePublished = null) {
  const cleanAuthor = (author || "anonymous")
    .toLowerCase()
    .replace(/[^a-z0-9]/g, "")
    .substring(0, 15);
  
  const date = datePublished ? new Date(datePublished) : new Date();
  const dateStr = date.toISOString().split('T')[0];
  
  const timestamp = Date.now().toString().slice(-6);
  
  return `${pluginSlug}-${cleanAuthor}-${dateStr}-${timestamp}`;
}

async function testReviewFix() {
  console.log("🧪 Testing Review Fix");
  console.log("=".repeat(50));

  try {
    // Connect to MongoDB
    await mongoose.connect(MONGODB_URI, {
      dbName: "wpdev_plugin",
      serverSelectionTimeoutMS: 10000,
      connectTimeoutMS: 10000,
    });
    console.log("✅ Connected to MongoDB");

    const testPluginSlug = "test-plugin-fix";
    const testPluginName = "Test Plugin Fix";

    // Test 1: Create a new review with proper reviewId
    console.log("\n🧪 Test 1: Creating review with proper reviewId");
    
    const reviewId1 = generateReviewId(testPluginSlug, "testuser1", new Date());
    console.log(`Generated reviewId: ${reviewId1}`);

    try {
      const result1 = await PluginReview.upsertReview({
        pluginSlug: testPluginSlug,
        pluginName: testPluginName,
        reviewId: reviewId1,
        title: "Test Review 1",
        content: "This is a test review to verify the fix",
        rating: 5,
        author: "testuser1",
        date: new Date(),
        reviewUrl: "https://example.com/review1",
      });

      console.log("✅ Test 1 PASSED: Review created successfully");
      console.log(`   Document ID: ${result1._id}`);
    } catch (error) {
      console.error("❌ Test 1 FAILED:", error.message);
      throw error;
    }

    // Test 2: Create another review with different reviewId
    console.log("\n🧪 Test 2: Creating second review with different reviewId");
    
    const reviewId2 = generateReviewId(testPluginSlug, "testuser2", new Date());
    console.log(`Generated reviewId: ${reviewId2}`);

    try {
      const result2 = await PluginReview.upsertReview({
        pluginSlug: testPluginSlug,
        pluginName: testPluginName,
        reviewId: reviewId2,
        title: "Test Review 2",
        content: "This is another test review",
        rating: 4,
        author: "testuser2",
        date: new Date(),
        reviewUrl: "https://example.com/review2",
      });

      console.log("✅ Test 2 PASSED: Second review created successfully");
      console.log(`   Document ID: ${result2._id}`);
    } catch (error) {
      console.error("❌ Test 2 FAILED:", error.message);
      throw error;
    }

    // Test 3: Try to create duplicate reviewId (should update, not error)
    console.log("\n🧪 Test 3: Testing duplicate reviewId handling");
    
    try {
      const result3 = await PluginReview.upsertReview({
        pluginSlug: testPluginSlug,
        pluginName: testPluginName,
        reviewId: reviewId1, // Same as first review
        title: "Updated Test Review 1",
        content: "This is an updated test review",
        rating: 3,
        author: "testuser1",
        date: new Date(),
        reviewUrl: "https://example.com/review1",
      });

      console.log("✅ Test 3 PASSED: Duplicate reviewId handled correctly (updated existing)");
      console.log(`   Document ID: ${result3._id}`);
    } catch (error) {
      console.error("❌ Test 3 FAILED:", error.message);
      throw error;
    }

    // Test 4: Verify the final state
    console.log("\n🧪 Test 4: Verifying final state");
    
    const finalDoc = await PluginReview.findOne({ pluginSlug: testPluginSlug });
    if (finalDoc) {
      console.log("✅ Test 4 PASSED: Final document retrieved");
      console.log(`   Plugin: ${finalDoc.pluginSlug}`);
      console.log(`   Total reviews: ${finalDoc.reviews.length}`);
      console.log(`   Review IDs: ${finalDoc.reviews.map(r => r.reviewId).join(', ')}`);
      
      // Verify no duplicate reviewIds
      const reviewIds = finalDoc.reviews.map(r => r.reviewId);
      const uniqueIds = [...new Set(reviewIds)];
      if (reviewIds.length === uniqueIds.length) {
        console.log("✅ No duplicate reviewIds found");
      } else {
        console.error("❌ Duplicate reviewIds detected!");
      }
    } else {
      console.error("❌ Test 4 FAILED: Could not retrieve final document");
    }

    // Cleanup: Remove test data
    console.log("\n🧹 Cleaning up test data");
    await PluginReview.deleteOne({ pluginSlug: testPluginSlug });
    console.log("✅ Test data cleaned up");

    console.log("\n🎉 All tests PASSED! The reviewId duplicate key error fix is working correctly.");

  } catch (error) {
    console.error("❌ Test failed with error:", error.message);
    console.error("Full error:", error);
    process.exit(1);
  } finally {
    await mongoose.disconnect();
    console.log("\n✅ Disconnected from MongoDB");
  }
}

// Run the test
testReviewFix();
