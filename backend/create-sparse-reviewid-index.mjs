#!/usr/bin/env node

/**
 * <PERSON><PERSON>t to create a sparse unique index on reviewId field (OPTIONAL)
 * 
 * Note: This is typically NOT needed for the current schema since reviews
 * are stored as embedded objects, not as separate documents with reviewId fields.
 * 
 * Use this only if you change the schema to store individual review documents.
 */

import mongoose from "mongoose";
import dotenv from "dotenv";

// Load environment variables
dotenv.config();

// Use environment variable or fallback to local
const MONGODB_URI =
  process.env.MONGODB_URI || "mongodb://localhost:27017/wpdev_plugin";

async function createSparseReviewIdIndex() {
  console.log("🔧 Creating Sparse ReviewId Index (Optional)");
  console.log("=".repeat(60));
  console.log("⚠️ WARNING: This is typically NOT needed for the current schema!");
  console.log("   Reviews are stored as embedded objects, not separate documents.");
  console.log("");
  
  const readline = await import('readline');
  const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
  });

  const answer = await new Promise((resolve) => {
    rl.question('Do you want to continue? (y/N): ', resolve);
  });
  
  rl.close();

  if (answer.toLowerCase() !== 'y' && answer.toLowerCase() !== 'yes') {
    console.log("Operation cancelled.");
    return;
  }

  console.log(
    `🔗 Connecting to: ${
      MONGODB_URI.includes("mongodb+srv")
        ? "MongoDB Atlas (Production)"
        : "Local MongoDB"
    }`
  );

  try {
    // Configure connection options for both local and production
    const options = {
      dbName: "wpdev_plugin",
      serverSelectionTimeoutMS: 10000,
      connectTimeoutMS: 10000,
    };

    // Connect to MongoDB
    await mongoose.connect(MONGODB_URI, options);
    console.log("✅ Connected to MongoDB");
    console.log(`📊 Database: ${mongoose.connection.db.databaseName}`);

    const db = mongoose.connection.db;
    const reviewsCollection = db.collection("pluginreviews");

    // Check current indexes
    console.log("\n📊 Current indexes:");
    const indexes = await reviewsCollection.indexes();
    indexes.forEach((index, i) => {
      console.log(`${i + 1}. ${index.name}: ${JSON.stringify(index.key)}`);
    });

    // Check if reviewId index already exists
    const reviewIdIndex = indexes.find((index) => index.name === "reviewId_1");
    
    if (reviewIdIndex) {
      console.log("\n⚠️ reviewId_1 index already exists!");
      console.log("You should drop it first using the fix-duplicate-reviewid-error.mjs script");
      return;
    }

    // Create sparse unique index on reviewId
    console.log("\n🔨 Creating sparse unique index on reviewId...");
    
    try {
      await reviewsCollection.createIndex(
        { reviewId: 1 },
        { 
          unique: true, 
          sparse: true,  // This allows multiple null values
          name: "reviewId_1_sparse"
        }
      );
      
      console.log("✅ Successfully created sparse unique index on reviewId");
      console.log("   - Index name: reviewId_1_sparse");
      console.log("   - Type: unique, sparse");
      console.log("   - This allows multiple documents with null reviewId");
      
    } catch (error) {
      console.error("❌ Failed to create sparse index:", error.message);
      throw error;
    }

    // Show final indexes
    console.log("\n📊 Final indexes:");
    const finalIndexes = await reviewsCollection.indexes();
    finalIndexes.forEach((index, i) => {
      console.log(`${i + 1}. ${index.name}: ${JSON.stringify(index.key)}`);
      if (index.sparse) {
        console.log(`   └─ (sparse: allows null values)`);
      }
      if (index.unique) {
        console.log(`   └─ (unique: prevents duplicates)`);
      }
    });

    console.log("\n📋 Summary:");
    console.log("✅ Sparse unique index created successfully");
    console.log("   - Multiple documents can have null reviewId");
    console.log("   - Non-null reviewId values must be unique");
    console.log("   - This prevents the E11000 duplicate key error");

  } catch (error) {
    console.error("❌ Operation failed with error:", error.message);
    console.error("Full error:", error);
  } finally {
    await mongoose.disconnect();
    console.log("\n✅ Disconnected from MongoDB");
  }
}

// Run the script
createSparseReviewIdIndex();
