import fetch from "node-fetch";

// Test configuration
const BASE_URL = "http://localhost:5001";

// Test credentials
const TEST_CREDENTIALS = {
  email: "<EMAIL>",
  password: "superadmin",
};

// Test data - using a different keyword
const testData = {
  pluginSlug: "betterlinks",
  pluginName: "BetterLinks",
  keyword: "url-manager", // Different keyword to avoid duplicates
};

// Helper function to get authentication token
async function getAuthToken() {
  console.log("🔑 Getting authentication token...");

  try {
    const response = await fetch(`${BASE_URL}/api/auth/login`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(TEST_CREDENTIALS),
    });

    const result = await response.json();

    if (response.status === 200 && result.success) {
      console.log("✅ Authentication successful");
      return result.token;
    } else {
      console.log("❌ Authentication failed:", result.message);
      return null;
    }
  } catch (error) {
    console.error("❌ Authentication error:", error.message);
    return null;
  }
}

async function testNewKeywordAddition() {
  console.log("🧪 Testing new keyword addition...\n");

  // Get authentication token
  const token = await getAuthToken();
  if (!token) {
    console.log("❌ Cannot proceed without authentication token");
    return;
  }

  console.log("\n" + "=".repeat(50) + "\n");

  try {
    // Test: Add a new keyword
    console.log(`📝 Adding new keyword: "${testData.keyword}"...`);
    const addResponse = await fetch(`${BASE_URL}/api/keywords`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
      body: JSON.stringify(testData),
    });

    const addResult = await addResponse.json();
    console.log("Response status:", addResponse.status);
    console.log("Response body:", JSON.stringify(addResult, null, 2));

    if (addResponse.status === 201) {
      console.log("✅ Keyword added successfully!");
      console.log("- Keyword:", addResult.keyword?.keyword);
      console.log("- Plugin:", addResult.keyword?.pluginSlug);
      console.log("- Source:", addResult.keyword?.source);
      console.log("- Rank:", addResult.rank);
      console.log("- Occurrences:", addResult.occurrences);
      console.log("- Rank Data Created:", addResult.rankData ? "Yes" : "No");
    } else {
      console.log("❌ Failed to add keyword");
      console.log("Error:", addResult.message || addResult.error);
    }

    console.log("\n" + "=".repeat(50) + "\n");

    // Test: Get keywords to verify it was added to pluginkeywords collection
    console.log("📝 Getting keywords to verify storage...");
    const getResponse = await fetch(
      `${BASE_URL}/api/keywords/plugin/${testData.pluginSlug}`,
      {
        method: "GET",
        headers: {
          Authorization: `Bearer ${token}`,
        },
      }
    );

    const getResult = await getResponse.json();
    console.log("Get keywords response status:", getResponse.status);

    if (getResponse.status === 200) {
      console.log("✅ Keywords retrieved successfully!");
      console.log("- Total count:", getResult.count);

      // Look for our newly added keyword
      const newKeyword = getResult.keywords?.find(
        (k) => k.keyword === testData.keyword
      );
      if (newKeyword) {
        console.log("✅ New keyword found in pluginkeywords collection!");
        console.log("- Keyword:", newKeyword.keyword);
        console.log("- Source:", newKeyword.source);
        console.log("- Type:", newKeyword.type);
        console.log("- Added At:", newKeyword.addedAt);
      } else {
        console.log("❌ New keyword NOT found in pluginkeywords collection");
        console.log(
          "Available keywords:",
          getResult.keywords?.map((k) => k.keyword)
        );
      }
    } else {
      console.log("❌ Failed to get keywords");
    }
  } catch (error) {
    console.error("❌ Test failed with error:", error.message);
    console.error("Stack trace:", error.stack);
  }
}

// Run the test
testNewKeywordAddition();
