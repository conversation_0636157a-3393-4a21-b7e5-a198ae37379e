import fetch from "node-fetch";

// Test configuration
const BASE_URL = "http://localhost:5001";

// Test credentials (using the superadmin created by createSuperAdmin.js)
const TEST_CREDENTIALS = {
  email: "<EMAIL>",
  password: "superadmin",
};

// Test data
const testData = {
  pluginSlug: "betterlinks",
  pluginName: "BetterLinks",
  keyword: "links",
};

// Helper function to get authentication token
async function getAuthToken() {
  console.log("🔑 Getting authentication token...");

  try {
    const response = await fetch(`${BASE_URL}/api/auth/login`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(TEST_CREDENTIALS),
    });

    const result = await response.json();

    if (response.status === 200 && result.success) {
      console.log("✅ Authentication successful");
      console.log("👤 User:", result.user.name, `(${result.user.email})`);
      return result.token;
    } else {
      console.log("❌ Authentication failed:", result.message);
      return null;
    }
  } catch (error) {
    console.error("❌ Authentication error:", error.message);
    return null;
  }
}

async function testKeywordAddition() {
  console.log("🧪 Testing keyword addition functionality...\n");

  // First, get authentication token
  const token = await getAuthToken();
  if (!token) {
    console.log("❌ Cannot proceed without authentication token");
    return;
  }

  console.log("\n" + "=".repeat(50) + "\n");

  try {
    // Test 0: Add the plugin first (if not already added)
    console.log("📝 Test 0: Adding plugin to user's list...");
    const addPluginResponse = await fetch(`${BASE_URL}/api/plugins/added`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
      body: JSON.stringify({ slug: testData.pluginSlug }),
    });

    const addPluginResult = await addPluginResponse.json();
    console.log("Add plugin response status:", addPluginResponse.status);

    if (addPluginResponse.status === 201) {
      console.log("✅ Plugin added successfully!");
    } else if (
      addPluginResponse.status === 400 &&
      addPluginResult.message?.includes("already exists")
    ) {
      console.log("✅ Plugin already exists in user's list");
    } else {
      console.log("⚠️ Plugin addition failed:", addPluginResult.message);
      console.log("Continuing with keyword test anyway...");
    }

    console.log("\n" + "=".repeat(50) + "\n");

    // Test 1: Add a new keyword
    console.log("📝 Test 1: Adding new keyword...");
    const addResponse = await fetch(`${BASE_URL}/api/keywords`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
      body: JSON.stringify(testData),
    });

    const addResult = await addResponse.json();
    console.log("Response status:", addResponse.status);
    console.log("Response body:", JSON.stringify(addResult, null, 2));

    if (addResponse.status === 201) {
      console.log("✅ Keyword added successfully!");
      console.log("- Keyword:", addResult.keyword?.keyword);
      console.log("- Plugin:", addResult.keyword?.pluginSlug);
      console.log("- Rank:", addResult.rank);
      console.log("- Occurrences:", addResult.occurrences);
      console.log(
        "- Rank Data:",
        addResult.rankData ? "Created" : "Not created"
      );
    } else {
      console.log("❌ Failed to add keyword");
      console.log("Error:", addResult.message || addResult.error);
    }

    console.log("\n" + "=".repeat(50) + "\n");

    // Test 2: Try to add the same keyword again (should fail)
    console.log("📝 Test 2: Adding duplicate keyword (should fail)...");
    const duplicateResponse = await fetch(`${BASE_URL}/api/keywords`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
      body: JSON.stringify(testData),
    });

    const duplicateResult = await duplicateResponse.json();
    console.log("Response status:", duplicateResponse.status);
    console.log("Response body:", JSON.stringify(duplicateResult, null, 2));

    if (
      duplicateResponse.status === 400 &&
      duplicateResult.message?.includes("already exists")
    ) {
      console.log("✅ Duplicate keyword correctly rejected!");
    } else {
      console.log("❌ Duplicate keyword handling failed");
    }

    console.log("\n" + "=".repeat(50) + "\n");

    // Test 3: Get keywords for the plugin
    console.log("📝 Test 3: Getting keywords for plugin...");
    const getResponse = await fetch(
      `${BASE_URL}/api/keywords/plugin/${testData.pluginSlug}`,
      {
        method: "GET",
        headers: {
          Authorization: `Bearer ${token}`,
        },
      }
    );

    const getResult = await getResponse.json();
    console.log("Response status:", getResponse.status);
    console.log("Response body:", JSON.stringify(getResult, null, 2));

    if (getResponse.status === 200) {
      console.log("✅ Keywords retrieved successfully!");
      console.log("- Count:", getResult.count);
      console.log("- Keywords found:", getResult.keywords?.length || 0);
    } else {
      console.log("❌ Failed to get keywords");
    }
  } catch (error) {
    console.error("❌ Test failed with error:", error.message);
    console.error("Stack trace:", error.stack);
  }
}

// Run the test
testKeywordAddition();
