#!/usr/bin/env node

// Test script to verify the new review fetching system
import fetch from 'node-fetch';

const BASE_URL = 'http://localhost:5001';

async function testReviewRefresh() {
  try {
    console.log('🧪 Testing review refresh functionality...');
    
    // First, let's get an admin token (you'll need to replace this with actual credentials)
    const loginResponse = await fetch(`${BASE_URL}/api/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: '<EMAIL>', // Replace with actual admin email
        password: 'admin123' // Replace with actual admin password
      }),
    });

    if (!loginResponse.ok) {
      console.log('❌ Login failed. Please update credentials in the test script.');
      console.log('You can test manually through the browser instead.');
      return;
    }

    const loginData = await loginResponse.json();
    const token = loginData.token;

    console.log('✅ Login successful');

    // Test the review refresh endpoint
    console.log('🔄 Testing review refresh...');
    const refreshResponse = await fetch(`${BASE_URL}/api/analytics/reviews/refresh`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json',
      },
    });

    if (!refreshResponse.ok) {
      throw new Error(`Refresh failed: ${refreshResponse.status}`);
    }

    const refreshData = await refreshResponse.json();
    console.log('✅ Refresh response:', refreshData);

    // Test fetching reviews for a specific plugin
    console.log('📊 Testing review retrieval...');
    const reviewsResponse = await fetch(`${BASE_URL}/api/analytics/reviews/betterlinks?page=1&limit=10`, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json',
      },
    });

    if (!reviewsResponse.ok) {
      throw new Error(`Reviews fetch failed: ${reviewsResponse.status}`);
    }

    const reviewsData = await reviewsResponse.json();
    console.log('✅ Reviews data:', {
      success: reviewsData.success,
      reviewCount: reviewsData.reviews?.length || 0,
      totalReviews: reviewsData.stats?.totalReviews || 0,
      averageRating: reviewsData.stats?.averageRating || 0,
    });

    if (reviewsData.reviews && reviewsData.reviews.length > 0) {
      console.log('📝 Sample review:', {
        reviewId: reviewsData.reviews[0].reviewId,
        title: reviewsData.reviews[0].title,
        rating: reviewsData.reviews[0].rating,
        author: reviewsData.reviews[0].author,
        date: reviewsData.reviews[0].date,
      });
    }

  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

// Run the test
testReviewRefresh();
