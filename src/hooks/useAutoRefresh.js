import { useEffect, useRef, useCallback } from "react";

/**
 * Custom hook for auto-refresh functionality at specific times
 * @param {Function} refreshFunction - Function to call when refresh is triggered
 * @param {string} targetTime - Target time in HH:MM format (24-hour, GMT+6)
 * @param {boolean} enabled - Whether auto-refresh is enabled
 * @param {string} refreshName - Name for logging purposes
 */
export const useAutoRefresh = (
  refreshFunction,
  targetTime,
  enabled = true,
  refreshName = "Auto Refresh"
) => {
  const intervalRef = useRef(null);
  const lastRefreshDateRef = useRef(null);

  const checkAndRefresh = useCallback(() => {
    if (!enabled || !refreshFunction) return;

    const now = new Date();

    // Convert to GMT+6 (Bangladesh time)
    const bangladeshTime = new Date(now.getTime() + 6 * 60 * 60 * 1000);

    const currentHour = bangladeshTime.getUTCHours();
    const currentMinute = bangladeshTime.getUTCMinutes();
    const currentDate = bangladeshTime.getUTCDate();

    // Parse target time
    const [targetHour, targetMinute] = targetTime.split(":").map(Number);

    // Check if we're at the target time (within 1 minute window)
    const isTargetTime =
      currentHour === targetHour && currentMinute === targetMinute;

    // Check if we haven't already refreshed today
    const hasRefreshedToday = lastRefreshDateRef.current === currentDate;

    if (isTargetTime && !hasRefreshedToday) {
      console.log(`🕐 ${refreshName} triggered at ${targetTime} GMT+6`);

      // Mark as refreshed for today
      lastRefreshDateRef.current = currentDate;

      // Call the refresh function
      try {
        refreshFunction();
      } catch (error) {
        console.error(`❌ ${refreshName} failed:`, error);
      }
    }
  }, [refreshFunction, targetTime, enabled, refreshName]);

  useEffect(() => {
    if (!enabled) {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
      return;
    }

    // Check every minute
    intervalRef.current = setInterval(checkAndRefresh, 60000);

    // Also check immediately on mount
    checkAndRefresh();

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
    };
  }, [checkAndRefresh, enabled]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, []);
};

/**
 * Hook specifically for keyword rank auto-refresh at 9:15 AM GMT+6
 */
export const useKeywordAutoRefresh = (refreshFunction, enabled = true) => {
  return useAutoRefresh(
    refreshFunction,
    "09:15",
    enabled,
    "Keyword Rank Auto Refresh"
  );
};

/**
 * Hook specifically for plugin download data auto-refresh at 9:25 AM GMT+6
 */
export const usePluginDataAutoRefresh = (refreshFunction, enabled = true) => {
  return useAutoRefresh(
    refreshFunction,
    "09:25",
    enabled,
    "Plugin Download Data Auto Refresh"
  );
};
