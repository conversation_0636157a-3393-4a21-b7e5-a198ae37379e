/**
 * HTML Sanitizer utility for safely rendering plugin content
 * Allows safe HTML elements and video embeds while removing dangerous content
 */

// List of allowed HTML tags
const ALLOWED_TAGS = [
  "p",
  "br",
  "strong",
  "b",
  "em",
  "i",
  "u",
  "h1",
  "h2",
  "h3",
  "h4",
  "h5",
  "h6",
  "ul",
  "ol",
  "li",
  "blockquote",
  "pre",
  "code",
  "a",
  "img",
  "div",
  "span",
  "table",
  "thead",
  "tbody",
  "tr",
  "td",
  "th",
  "iframe",
  "video",
  "source",
];

// List of allowed attributes for specific tags
const ALLOWED_ATTRIBUTES = {
  a: ["href", "title", "target", "rel"],
  img: ["src", "alt", "title", "width", "height", "class"],
  iframe: [
    "src",
    "width",
    "height",
    "frameborder",
    "allowfullscreen",
    "title",
    "class",
  ],
  video: [
    "src",
    "width",
    "height",
    "controls",
    "autoplay",
    "muted",
    "loop",
    "poster",
    "class",
  ],
  source: ["src", "type"],
  div: ["class", "id"],
  span: ["class", "id"],
  p: ["class"],
  h1: ["class"],
  h2: ["class"],
  h3: ["class"],
  h4: ["class"],
  h5: ["class"],
  h6: ["class"],
  ul: ["class"],
  ol: ["class"],
  li: ["class"],
  table: ["class"],
  thead: ["class"],
  tbody: ["class"],
  tr: ["class"],
  td: ["class"],
  th: ["class"],
  blockquote: ["class"],
  pre: ["class"],
  code: ["class"],
};

// List of trusted domains for iframe embeds
const TRUSTED_IFRAME_DOMAINS = [
  "youtube.com",
  "www.youtube.com",
  "youtu.be",
  "vimeo.com",
  "player.vimeo.com",
  "wordpress.tv",
  "videopress.com",
  "wistia.com",
  "fast.wistia.net",
  "embed.ted.com",
];

/**
 * Check if an iframe source is from a trusted domain
 */
function isTrustedIframeSrc(src) {
  if (!src) return false;

  try {
    const url = new URL(src);
    return TRUSTED_IFRAME_DOMAINS.some(
      (domain) => url.hostname === domain || url.hostname.endsWith("." + domain)
    );
  } catch (e) {
    return false;
  }
}

/**
 * Sanitize HTML content to allow safe elements and video embeds
 */
export function sanitizeHtml(html) {
  if (!html) return "";

  // Create a temporary DOM element to parse the HTML
  const tempDiv = document.createElement("div");
  tempDiv.innerHTML = html;

  // Function to recursively clean elements
  function cleanElement(element) {
    const tagName = element.tagName.toLowerCase();

    // Remove if tag is not allowed
    if (!ALLOWED_TAGS.includes(tagName)) {
      element.remove();
      return;
    }

    // Special handling for iframes - always replace with a link for better compatibility
    if (tagName === "iframe") {
      const src = element.getAttribute("src");
      const title = element.getAttribute("title") || "Video content";

      // Create a video placeholder with link
      const placeholder = document.createElement("div");
      placeholder.className =
        "bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-lg p-6 text-center my-4";

      // Determine video type and create appropriate icon and text
      let videoType = "Video";
      let iconSvg = `<svg class="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
      </svg>`;

      if (src && src.includes("youtube")) {
        videoType = "YouTube Video";
        iconSvg = `<svg class="w-8 h-8 text-red-600" fill="currentColor" viewBox="0 0 24 24">
          <path d="M23.498 6.186a3.016 3.016 0 0 0-2.122-2.136C19.505 3.545 12 3.545 12 3.545s-7.505 0-9.377.505A3.017 3.017 0 0 0 .502 6.186C0 8.07 0 12 0 12s0 3.93.502 5.814a3.016 3.016 0 0 0 2.122 2.136c1.871.505 9.376.505 9.376.505s7.505 0 9.377-.505a3.015 3.015 0 0 0 2.122-2.136C24 15.93 24 12 24 12s0-3.93-.502-5.814zM9.545 15.568V8.432L15.818 12l-6.273 3.568z"/>
        </svg>`;
      } else if (src && src.includes("vimeo")) {
        videoType = "Vimeo Video";
        iconSvg = `<svg class="w-8 h-8 text-blue-500" fill="currentColor" viewBox="0 0 24 24">
          <path d="M23.977 6.416c-.105 2.338-1.739 5.543-4.894 9.609-3.268 4.247-6.026 6.37-8.29 6.37-1.409 0-2.578-1.294-3.553-3.881L5.322 11.4C4.603 8.816 3.834 7.522 3.01 7.522c-.179 0-.806.378-1.881 1.132L0 7.197a315.065 315.065 0 0 0 4.192-3.729C5.978 2.4 7.333 1.718 8.222 1.718c2.104 0 3.391 1.262 3.863 3.783.508 2.27.861 3.683.861 4.235 0 1.288-.547 3.2-1.642 5.737-.832 1.96-1.747 2.94-2.747 2.94-.842 0-1.638-.79-2.387-2.37l-.318-.81c-.613-1.96-1.17-2.94-1.668-2.94-.498 0-1.225.562-2.178 1.688l-.951-1.4c1.588-1.96 3.176-2.94 4.764-2.94 1.588 0 2.823 1.225 3.706 3.676.883 2.45 1.225 3.676 1.225 3.676s.342 1.96 1.026 5.88c.684 3.92 1.026 5.88 1.026 5.88.342 1.96 1.026 2.94 2.052 2.94 1.026 0 2.394-.98 4.104-2.94 1.71-1.96 2.565-3.92 2.565-5.88z"/>
        </svg>`;
      }

      placeholder.innerHTML = `
        <div class="flex flex-col items-center space-y-3">
          ${iconSvg}
          <div>
            <h4 class="text-lg font-semibold text-gray-800 mb-1">${videoType}</h4>
            <p class="text-gray-600 text-sm mb-3">${title}</p>
            <a href="${src}" target="_blank" rel="noopener noreferrer"
               class="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
              <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"></path>
              </svg>
              Watch Video
            </a>
          </div>
        </div>
      `;

      element.parentNode.replaceChild(placeholder, element);
      return;
    }

    // Clean attributes
    const allowedAttrs = ALLOWED_ATTRIBUTES[tagName] || [];
    const attributes = Array.from(element.attributes);

    attributes.forEach((attr) => {
      if (!allowedAttrs.includes(attr.name)) {
        element.removeAttribute(attr.name);
      }
    });

    // Recursively clean child elements
    const children = Array.from(element.children);
    children.forEach((child) => cleanElement(child));
  }

  // Clean all elements
  const children = Array.from(tempDiv.children);
  children.forEach((child) => cleanElement(child));

  return tempDiv.innerHTML;
}

/**
 * Enhanced sanitizer that also handles common WordPress shortcodes
 */
export function sanitizeWordPressContent(html) {
  if (!html) return "";

  // First, handle common WordPress shortcodes and video URLs
  let processedHtml = html
    // Convert [video] shortcodes to proper video tags
    .replace(/\[video\s+([^\]]+)\]/g, (match, attrs) => {
      const srcMatch = attrs.match(/src="([^"]+)"/);
      if (srcMatch) {
        return `<video controls><source src="${srcMatch[1]}" type="video/mp4"></video>`;
      }
      return "";
    })
    // Convert [youtube] shortcodes to iframe embeds
    .replace(/\[youtube\s+([^\]]+)\]/g, (match, attrs) => {
      const idMatch = attrs.match(/(?:id="|v=)([^"&\s]+)/);
      if (idMatch) {
        return `<iframe src="https://www.youtube.com/embed/${idMatch[1]}" width="560" height="315" frameborder="0" allowfullscreen title="YouTube video"></iframe>`;
      }
      return "";
    })
    // Convert [vimeo] shortcodes to iframe embeds
    .replace(/\[vimeo\s+([^\]]+)\]/g, (match, attrs) => {
      const idMatch = attrs.match(/id="?([^"\s]+)"?/);
      if (idMatch) {
        return `<iframe src="https://player.vimeo.com/video/${idMatch[1]}" width="560" height="315" frameborder="0" allowfullscreen title="Vimeo video"></iframe>`;
      }
      return "";
    })
    // Convert direct YouTube URLs to iframe embeds
    .replace(
      /https?:\/\/(?:www\.)?youtube\.com\/watch\?v=([a-zA-Z0-9_-]+)/g,
      (match, videoId) => {
        return `<iframe src="https://www.youtube.com/embed/${videoId}" width="560" height="315" frameborder="0" allowfullscreen title="YouTube video"></iframe>`;
      }
    )
    // Convert YouTube short URLs to iframe embeds
    .replace(/https?:\/\/youtu\.be\/([a-zA-Z0-9_-]+)/g, (match, videoId) => {
      return `<iframe src="https://www.youtube.com/embed/${videoId}" width="560" height="315" frameborder="0" allowfullscreen title="YouTube video"></iframe>`;
    })
    // Convert direct Vimeo URLs to iframe embeds
    .replace(/https?:\/\/(?:www\.)?vimeo\.com\/(\d+)/g, (match, videoId) => {
      return `<iframe src="https://player.vimeo.com/video/${videoId}" width="560" height="315" frameborder="0" allowfullscreen title="Vimeo video"></iframe>`;
    });

  // Then sanitize the HTML
  return sanitizeHtml(processedHtml);
}
