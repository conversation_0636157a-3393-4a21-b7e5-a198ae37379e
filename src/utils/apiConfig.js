// Centralized API configuration
export const getApiBaseUrl = () => {
  // In production, use the same domain as the frontend
  if (import.meta.env.PROD) {
    return window.location.origin;
  }

  // In development, use environment variable or fallback to localhost
  return import.meta.env.VITE_API_BASE_URL || "http://localhost:5001";
};

// Helper function to make authenticated API calls
export const makeAuthenticatedRequest = async (endpoint, options = {}) => {
  const token = localStorage.getItem("adminToken");
  const baseUrl = getApiBaseUrl();

  const defaultOptions = {
    headers: {
      "Content-Type": "application/json",
      ...(token && { Authorization: `Bearer ${token}` }),
    },
  };

  const mergedOptions = {
    ...defaultOptions,
    ...options,
    headers: {
      ...defaultOptions.headers,
      ...options.headers,
    },
  };

  const response = await fetch(`${baseUrl}${endpoint}`, mergedOptions);

  // Handle authentication errors
  if (response.status === 401) {
    // Check if the error is specifically about token expiration
    let errorMessage = "Authentication failed. Please login again.";

    try {
      const errorData = await response.json();
      if (errorData.message && errorData.message.includes("expired")) {
        errorMessage = "Your session has expired. Please login again.";
      } else if (errorData.message && errorData.message.includes("token")) {
        errorMessage = "Invalid session. Please login again.";
      }
    } catch (e) {
      // If we can't parse the error response, use default message
    }

    // Trigger auto-logout through a custom event
    // This allows us to use the AuthContext's autoLogout function
    window.dispatchEvent(
      new CustomEvent("auth-error", {
        detail: { message: errorMessage },
      })
    );

    throw new Error(errorMessage);
  }

  return response;
};
