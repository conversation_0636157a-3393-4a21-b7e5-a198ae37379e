!function(n,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports):"function"==typeof define&&define.amd?define(["exports"],t):t((n=n||self).sift={})}(this,(function(n){"use strict";
/*! *****************************************************************************
    Copyright (c) Microsoft Corporation.

    Permission to use, copy, modify, and/or distribute this software for any
    purpose with or without fee is hereby granted.

    THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH
    REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY
    AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,
    INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM
    LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR
    OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR
    PERFORMANCE OF THIS SOFTWARE.
    ***************************************************************************** */var t=function(n,r){return(t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,t){n.__proto__=t}||function(n,t){for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(n[r]=t[r])})(n,r)};function r(n,r){if("function"!=typeof r&&null!==r)throw new TypeError("Class extends value "+String(r)+" is not a constructor or null");function i(){this.constructor=n}t(n,r),n.prototype=null===r?Object.create(r):(i.prototype=r.prototype,new i)}var i=function(n){var t="[object "+n+"]";return function(n){return u(n)===t}},u=function(n){return Object.prototype.toString.call(n)},e=function(n){return n instanceof Date?n.getTime():o(n)?n.map(e):n&&"function"==typeof n.toJSON?n.toJSON():n},o=i("Array"),f=i("Object"),s=i("Function"),c=function(n,t){if(null==n&&n==t)return!0;if(n===t)return!0;if(Object.prototype.toString.call(n)!==Object.prototype.toString.call(t))return!1;if(o(n)){if(n.length!==t.length)return!1;for(var r=0,i=n.length;r<i;r++)if(!c(n[r],t[r]))return!1;return!0}if(f(n)){if(Object.keys(n).length!==Object.keys(t).length)return!1;for(var u in n)if(!c(n[u],t[u]))return!1;return!0}return!1},h=function(n,t,r,i,u,e){var f=t[i];if(o(n)&&isNaN(Number(f)))for(var s=0,c=n.length;s<c;s++)if(!h(n[s],t,r,i,s,n))return!1;return i===t.length||null==n?r(n,u,e,0===i):h(n[f],t,r,i+1,f,n)},a=function(){function n(n,t,r,i){this.params=n,this.owneryQuery=t,this.options=r,this.name=i,this.init()}return n.prototype.init=function(){},n.prototype.reset=function(){this.done=!1,this.keep=!1},n}(),l=function(n){function t(t,r,i,u){var e=n.call(this,t,r,i)||this;return e.children=u,e}return r(t,n),t.prototype.reset=function(){this.keep=!1,this.done=!1;for(var n=0,t=this.children.length;n<t;n++)this.children[n].reset()},t.prototype.childrenNext=function(n,t,r,i){for(var u=!0,e=!0,o=0,f=this.children.length;o<f;o++){var s=this.children[o];if(s.done||s.next(n,t,r,i),s.keep||(e=!1),s.done){if(!s.keep)break}else u=!1}this.done=u,this.keep=e},t}(a),v=function(n){function t(t,r,i,u,e){var o=n.call(this,t,r,i,u)||this;return o.name=e,o}return r(t,n),t}(l),w=function(n){function t(){var t=null!==n&&n.apply(this,arguments)||this;return t.propop=!0,t}return r(t,n),t.prototype.next=function(n,t,r,i){this.childrenNext(n,t,r,i)},t}(l),p=function(n){function t(t,r,i,u,e){var o=n.call(this,r,i,u,e)||this;return o.keyPath=t,o.propop=!0,o.t=function(n,t,r,i){return o.childrenNext(n,t,r,i),!o.done},o}return r(t,n),t.prototype.next=function(n,t,r){h(n,this.keyPath,this.t,0,t,r)},t}(l),b=function(n,t){if(n instanceof Function)return n;if(n instanceof RegExp)return function(t){var r="string"==typeof t&&n.test(t);return n.lastIndex=0,r};var r=e(n);return function(n){return t(r,e(n))}},y=function(n){function t(){var t=null!==n&&n.apply(this,arguments)||this;return t.propop=!0,t}return r(t,n),t.prototype.init=function(){this.i=b(this.params,this.options.compare)},t.prototype.next=function(n,t,r){Array.isArray(r)&&!r.hasOwnProperty(t)||this.i(n,t,r)&&(this.done=!0,this.keep=!0)},t}(a),$=function(n){function t(){var t=null!==n&&n.apply(this,arguments)||this;return t.propop=!0,t}return r(t,n),t.prototype.next=function(){this.done=!0,this.keep=!1},t}(a),d=function(n){return t=function(t,r,i,u){var o=typeof e(t),f=n(t);return new y((function(n){return typeof e(n)===o&&f(n)}),r,i,u)},function(n,r,i,u){return null==n?new $(n,r,i,u):t(n,r,i,u)};var t},j=function(n,t,r,i){var u=i.operations[n];return u||m(n),u(t,r,i,n)},m=function(n){throw new Error("Unsupported operation: "+n)},O=function(n,t){for(var r in n)if(t.operations.hasOwnProperty(r)||"$"===r.charAt(0))return!0;return!1},g=function(n,t,r,i,u){if(O(t,u)){var e=x(t,r,u),o=e[0];if(e[1].length)throw new Error("Property queries must contain only operations, or exact objects.");return new p(n,t,i,u,o)}return new p(n,t,i,u,[new y(t,i,u)])},E=function(n,t,r){void 0===t&&(t=null);var i=void 0===r?{}:r,u=i.compare,e=i.operations,o={compare:u||c,operations:Object.assign({},e||{})},f=x(n,null,o),s=f[0],h=f[1],a=[];return s.length&&a.push(new p([],n,t,o,s)),a.push.apply(a,h),1===a.length?a[0]:new w(n,t,o,a)},x=function(n,t,r){var i,u=[],e=[];if(!(i=n)||i.constructor!==Object&&i.constructor!==Array&&"function Object() { [native code] }"!==i.constructor.toString()&&"function Array() { [native code] }"!==i.constructor.toString()||i.toJSON)return u.push(new y(n,n,r)),[u,e];for(var o in n)if(r.operations.hasOwnProperty(o)){var f=j(o,n[o],n,r);if(f&&!f.propop&&t&&!r.operations[t])throw new Error("Malformed query. "+o+" cannot be matched against property.");null!=f&&u.push(f)}else"$"===o.charAt(0)?m(o):e.push(g(o.split("."),n[o],o,n,r));return[u,e]},_=function(n){return function(t,r,i){return n.reset(),n.next(t,r,i),n.keep}},A=function(n){function t(){var t=null!==n&&n.apply(this,arguments)||this;return t.propop=!0,t}return r(t,n),t.prototype.init=function(){this.i=b(this.params,this.options.compare)},t.prototype.reset=function(){n.prototype.reset.call(this),this.keep=!0},t.prototype.next=function(n){this.i(n)&&(this.done=!0,this.keep=!1)},t}(a),M=function(n){function t(){var t=null!==n&&n.apply(this,arguments)||this;return t.propop=!0,t}return r(t,n),t.prototype.init=function(){if(!this.params||"object"!=typeof this.params)throw new Error("Malformed query. $elemMatch must by an object.");this.u=E(this.params,this.owneryQuery,this.options)},t.prototype.reset=function(){n.prototype.reset.call(this),this.u.reset()},t.prototype.next=function(n){if(o(n)){for(var t=0,r=n.length;t<r;t++){this.u.reset();var i=n[t];this.u.next(i,t,n,!1),this.keep=this.keep||this.u.keep}this.done=!0}else this.done=!1,this.keep=!1},t}(a),q=function(n){function t(){var t=null!==n&&n.apply(this,arguments)||this;return t.propop=!0,t}return r(t,n),t.prototype.init=function(){this.u=E(this.params,this.owneryQuery,this.options)},t.prototype.reset=function(){n.prototype.reset.call(this),this.u.reset()},t.prototype.next=function(n,t,r,i){this.u.next(n,t,r,i),this.done=this.u.done,this.keep=!this.u.keep},t}(a),k=function(n){function t(){var t=null!==n&&n.apply(this,arguments)||this;return t.propop=!0,t}return r(t,n),t.prototype.init=function(){},t.prototype.next=function(n){o(n)&&n.length===this.params&&(this.done=!0,this.keep=!0)},t}(a),z=function(n){if(0===n.length)throw new Error("$and/$or/$nor must be a nonempty array")},F=function(n){function t(){var t=null!==n&&n.apply(this,arguments)||this;return t.propop=!1,t}return r(t,n),t.prototype.init=function(){var n=this;z(this.params),this.o=this.params.map((function(t){return E(t,null,n.options)}))},t.prototype.reset=function(){this.done=!1,this.keep=!1;for(var n=0,t=this.o.length;n<t;n++)this.o[n].reset()},t.prototype.next=function(n,t,r){for(var i=!1,u=!1,e=0,o=this.o.length;e<o;e++){var f=this.o[e];if(f.next(n,t,r),f.keep){i=!0,u=f.keep;break}}this.keep=u,this.done=i},t}(a),N=function(n){function t(){var t=null!==n&&n.apply(this,arguments)||this;return t.propop=!1,t}return r(t,n),t.prototype.next=function(t,r,i){n.prototype.next.call(this,t,r,i),this.keep=!this.keep},t}(F),S=function(n){function t(){var t=null!==n&&n.apply(this,arguments)||this;return t.propop=!0,t}return r(t,n),t.prototype.init=function(){var n=this;this.h=this.params.map((function(t){if(O(t,n.options))throw new Error("cannot nest $ under "+n.name.toLowerCase());return b(t,n.options.compare)}))},t.prototype.next=function(n,t,r){for(var i=!1,u=!1,e=0,o=this.h.length;e<o;e++){if((0,this.h[e])(n)){i=!0,u=!0;break}}this.keep=u,this.done=i},t}(a),C=function(n){function t(t,r,i,u){var e=n.call(this,t,r,i,u)||this;return e.propop=!0,e.l=new S(t,r,i,u),e}return r(t,n),t.prototype.next=function(n,t,r,i){this.l.next(n,t,r),o(r)&&!i?this.l.keep?(this.keep=!1,this.done=!0):t==r.length-1&&(this.keep=!0,this.done=!0):(this.keep=!this.l.keep,this.done=!0)},t.prototype.reset=function(){n.prototype.reset.call(this),this.l.reset()},t}(a),D=function(n){function t(){var t=null!==n&&n.apply(this,arguments)||this;return t.propop=!0,t}return r(t,n),t.prototype.next=function(n,t,r){r.hasOwnProperty(t)===this.params&&(this.done=!0,this.keep=!0)},t}(a),P=function(n){function t(t,r,i,u){var e=n.call(this,t,r,i,t.map((function(n){return E(n,r,i)})),u)||this;return e.propop=!1,z(t),e}return r(t,n),t.prototype.next=function(n,t,r,i){this.childrenNext(n,t,r,i)},t}(v),R=function(n){function t(t,r,i,u){var e=n.call(this,t,r,i,t.map((function(n){return E(n,r,i)})),u)||this;return e.propop=!0,e}return r(t,n),t.prototype.next=function(n,t,r,i){this.childrenNext(n,t,r,i)},t}(v),T=function(n,t,r){return new y(n,t,r)},I=function(n,t,r,i){return new A(n,t,r,i)},U=function(n,t,r,i){return new F(n,t,r,i)},B=function(n,t,r,i){return new N(n,t,r,i)},G=function(n,t,r,i){return new M(n,t,r,i)},H=function(n,t,r,i){return new C(n,t,r,i)},J=function(n,t,r,i){return new S(n,t,r,i)},K=d((function(n){return function(t){return t<n}})),L=d((function(n){return function(t){return t<=n}})),Q=d((function(n){return function(t){return t>n}})),V=d((function(n){return function(t){return t>=n}})),W=function(n,t,r){var i=n[0],u=n[1];return new y((function(n){return e(n)%i===u}),t,r)},X=function(n,t,r,i){return new D(n,t,r,i)},Y=function(n,t,r){return new y(new RegExp(n,t.$options),t,r)},Z=function(n,t,r,i){return new q(n,t,r,i)},nn={number:function(n){return"number"==typeof n},string:function(n){return"string"==typeof n},bool:function(n){return"boolean"==typeof n},array:function(n){return Array.isArray(n)},null:function(n){return null===n},timestamp:function(n){return n instanceof Date}},tn=function(n,t,r){return new y((function(t){if("string"==typeof n){if(!nn[n])throw new Error("Type alias does not exist");return nn[n](t)}return null!=t&&(t instanceof n||t.constructor===n)}),t,r)},rn=function(n,t,r,i){return new P(n,t,r,i)},un=function(n,t,r,i){return new R(n,t,r,i)},en=function(n,t,r){return new k(n,t,r,"$size")},on=function(){return null},fn=function(n,t,r){var i;if(s(n))i=n;else{if(process.env.CSP_ENABLED)throw new Error('In CSP mode, sift does not support strings in "$where" condition');i=new Function("obj","return "+n)}return new y((function(n){return i.bind(n)(n)}),t,r)},sn=Object.freeze({__proto__:null,$Size:k,$eq:T,$ne:I,$or:U,$nor:B,$elemMatch:G,$nin:H,$in:J,$lt:K,$lte:L,$gt:Q,$gte:V,$mod:W,$exists:X,$regex:Y,$not:Z,$type:tn,$and:rn,$all:un,$size:en,$options:on,$where:fn}),cn=function(n,t,r){var i=void 0===r?{}:r,u=i.compare,e=i.operations;return E(n,t,{compare:u,operations:Object.assign({},sn,e||{})})};n.$Size=k,n.$all=un,n.$and=rn,n.$elemMatch=G,n.$eq=T,n.$exists=X,n.$gt=Q,n.$gte=V,n.$in=J,n.$lt=K,n.$lte=L,n.$mod=W,n.$ne=I,n.$nin=H,n.$nor=B,n.$not=Z,n.$options=on,n.$or=U,n.$regex=Y,n.$size=en,n.$type=tn,n.$where=fn,n.EqualsOperation=y,n.createDefaultQueryOperation=cn,n.createEqualsOperation=function(n,t,r){return new y(n,t,r)},n.createOperationTester=_,n.createQueryOperation=E,n.createQueryTester=function(n,t){return void 0===t&&(t={}),_(E(n,null,t))},n.default=function(n,t){void 0===t&&(t={});var r=cn(n,null,t);return _(r)},Object.defineProperty(n,"v",{value:!0})}));
//# sourceMappingURL=sift.min.js.map
