import { SocksClientOptions, SocksClientChainOptions } from './constants';
/**
 * Error wrapper for SocksClient
 */
declare class SocksClientError extends Error {
    options: SocksClientOptions | SocksClientChainOptions;
    constructor(message: string, options: SocksClientOptions | SocksClientChainOptions);
}
/**
 * Shuffles a given array.
 * @param array The array to shuffle.
 */
declare function shuffleArray(array: unknown[]): void;
export { SocksClientError, shuffleArray };
