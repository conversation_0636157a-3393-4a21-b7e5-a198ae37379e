{"name": "tr46", "version": "3.0.0", "engines": {"node": ">=12"}, "description": "An implementation of the Unicode UTS #46: Unicode IDNA Compatibility Processing", "main": "index.js", "files": ["index.js", "lib/mappingTable.json", "lib/regexes.js", "lib/statusMapping.js"], "scripts": {"test": "mocha", "lint": "eslint .", "pretest": "node scripts/getLatestTests.js", "prepublish": "node scripts/generateMappingTable.js && node scripts/generateRegexes.js"}, "repository": "https://github.com/jsdom/tr46", "keywords": ["unicode", "tr46", "uts46", "punycode", "url", "whatwg"], "author": "<PERSON> <<EMAIL>>", "contributors": ["<PERSON> <<EMAIL>>"], "license": "MIT", "dependencies": {"punycode": "^2.1.1"}, "devDependencies": {"@domenic/eslint-config": "^1.4.0", "@unicode/unicode-14.0.0": "^1.2.1", "eslint": "^7.32.0", "minipass-fetch": "^1.4.1", "mocha": "^9.1.1", "regenerate": "^1.4.2"}, "unicodeVersion": "14.0.0"}