import mongoose from "mongoose";
import jwt from "jsonwebtoken";

// Database connection for serverless
let isConnected = false;

const connectDB = async () => {
  if (mongoose.connection.readyState === 1) {
    return mongoose.connection;
  }

  if (mongoose.connection.readyState === 2) {
    await new Promise((resolve, reject) => {
      mongoose.connection.once("connected", resolve);
      mongoose.connection.once("error", reject);
      setTimeout(() => reject(new Error("Connection timeout")), 10000);
    });
    return mongoose.connection;
  }

  let mongoURI = process.env.MONGODB_URI;
  if (!mongoURI) {
    throw new Error("MONGODB_URI environment variable is not set");
  }

  const options = {
    maxPoolSize: 5,
    serverSelectionTimeoutMS: 8000,
    socketTimeoutMS: 30000,
    connectTimeoutMS: 10000,
    dbName: "wpdev_plugin",
    retryWrites: true,
    bufferCommands: false,
    maxIdleTimeMS: 30000,
    heartbeatFrequencyMS: 10000,
  };

  const conn = await mongoose.connect(mongoURI, options);
  console.log(`✅ MongoDB Connected: ${conn.connection.host}`);
  return conn;
};

const ensureDbConnection = async () => {
  if (!isConnected) {
    try {
      await connectDB();
      isConnected = true;
      console.log("Database connected successfully");
    } catch (error) {
      console.error("Database connection failed:", error);
      throw error;
    }
  }
};

// User Schema
const userSchema = new mongoose.Schema(
  {
    name: { type: String, required: true },
    email: { type: String, required: true, unique: true },
    password: { type: String, required: true },
    role: {
      type: String,
      enum: ["member", "admin", "superadmin"],
      default: "member",
    },
    isActive: { type: Boolean, default: true },
  },
  { timestamps: true }
);

// AddedPlugin Schema with rankHistory
const addedPluginSchema = new mongoose.Schema(
  {
    userId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "User",
      required: true,
    },
    pluginSlug: { type: String, required: true, trim: true, lowercase: true },
    pluginName: { type: String, required: true, trim: true },
    displayName: { type: String, required: true, trim: true },
    currentRank: { type: Number, default: null },
    rankGrowth: { type: Number, default: null },
    // Store rank history directly in addedplugins collection
    rankHistory: {
      type: [
        {
          date: {
            type: String,
            required: true,
          },
          previousRank: {
            type: Number,
            required: true,
          },
          fetchedAt: {
            type: Date,
            default: Date.now,
          },
        },
      ],
      default: [],
    },
    isActive: { type: Boolean, default: true },
    pluginData: {
      version: String,
      rating: Number,
      downloaded: Number,
      last_updated: String,
      author: String,
    },
    addedAt: { type: Date, default: Date.now, index: true },
    lastUpdated: { type: Date, default: Date.now },
  },
  { timestamps: true }
);

// Create models
const User = mongoose.models.User || mongoose.model("User", userSchema);
const AddedPlugin =
  mongoose.models.AddedPlugin ||
  mongoose.model("AddedPlugin", addedPluginSchema);

// Simplified authentication for serverless
const authenticateUser = async (req) => {
  const authHeader = req.headers.authorization;
  const token =
    authHeader && authHeader.startsWith("Bearer ")
      ? authHeader.substring(7)
      : null;

  if (!token) {
    throw new Error("Access denied. No token provided.");
  }

  // Check if token is malformed
  if (token.split(".").length !== 3) {
    throw new Error("Invalid token format.");
  }

  // Verify token
  const decoded = jwt.verify(
    token,
    process.env.JWT_SECRET || "fallback-secret"
  );

  // Get user from database
  const user = await User.findById(decoded.userId);
  if (!user) {
    throw new Error("Invalid token. User not found.");
  }

  if (!user.isActive) {
    throw new Error("Account is deactivated.");
  }

  return user;
};

export default async function handler(req, res) {
  // Set CORS headers and ensure JSON response
  res.setHeader("Content-Type", "application/json");
  res.setHeader(
    "Access-Control-Allow-Origin",
    "https://pluginsight.vercel.app"
  );
  res.setHeader("Access-Control-Allow-Methods", "GET, OPTIONS");
  res.setHeader("Access-Control-Allow-Headers", "Content-Type, Authorization");
  res.setHeader("Access-Control-Allow-Credentials", "true");

  // Handle preflight requests
  if (req.method === "OPTIONS") {
    return res.status(200).end();
  }

  // Only allow GET requests
  if (req.method !== "GET") {
    return res.status(405).json({
      success: false,
      message: "Method not allowed",
    });
  }

  try {
    // Ensure database connection
    await ensureDbConnection();

    // Authenticate user
    const user = await authenticateUser(req);

    const userId = user._id;
    const { days = 30, slugs } = req.query;

    console.log(
      `Getting plugin history for user ${userId}, days: ${days}, slugs: ${slugs}`
    );

    // Get user's added plugins
    let addedPlugins;
    if (slugs) {
      const slugArray = slugs.split(",");
      console.log(`Searching for plugins with slugs: ${slugArray}`);
      addedPlugins = await AddedPlugin.find({
        userId,
        pluginSlug: { $in: slugArray },
        isActive: true,
      });
    } else {
      addedPlugins = await AddedPlugin.find({
        userId,
        isActive: true,
      });
    }

    console.log(`Found ${addedPlugins.length} added plugins for user`);

    // Process rank history for each plugin - data is already in addedplugins collection
    const pluginsWithHistory = addedPlugins.map((addedPlugin) => {
      const pluginObj = addedPlugin.toObject();

      // Filter rank history by days if specified
      let filteredRankHistory = pluginObj.rankHistory || [];

      if (days && days > 0) {
        const cutoffDate = new Date();
        cutoffDate.setDate(cutoffDate.getDate() - parseInt(days));

        filteredRankHistory = filteredRankHistory.filter((entry) => {
          try {
            // Parse date in dd-mm-yyyy format
            const [day, month, year] = entry.date.split("-");
            const entryDate = new Date(year, month - 1, day);
            return entryDate >= cutoffDate;
          } catch (dateError) {
            console.error("Error parsing date:", entry.date, dateError);
            return false; // Exclude entries with invalid dates
          }
        });
      }

      return {
        ...pluginObj,
        rankHistory: filteredRankHistory,
      };
    });

    console.log(
      `Returning ${pluginsWithHistory.length} plugins with history data`
    );
    pluginsWithHistory.forEach((plugin) => {
      console.log(
        `Plugin ${plugin.pluginSlug}: ${plugin.rankHistory.length} history entries`
      );
    });

    res.json({
      success: true,
      plugins: pluginsWithHistory,
    });
  } catch (error) {
    console.error("Get plugin history error:", error);

    // Ensure proper JSON response
    if (!res.headersSent) {
      res.setHeader("Content-Type", "application/json");
    }

    // Handle authentication errors
    if (
      error.message &&
      (error.message.includes("token") ||
        error.message.includes("Access denied") ||
        error.message.includes("expired") ||
        error.name === "TokenExpiredError" ||
        error.name === "JsonWebTokenError")
    ) {
      return res.status(401).json({
        success: false,
        message:
          error.name === "TokenExpiredError"
            ? "Token expired. Please login again."
            : "Authentication failed. Please login again.",
        error: error.message,
      });
    }

    // Handle database connection errors
    if (error.message && error.message.includes("connection")) {
      return res.status(503).json({
        success: false,
        message: "Database connection failed",
        error:
          process.env.NODE_ENV === "production"
            ? "Service temporarily unavailable"
            : error.message,
      });
    }

    res.status(500).json({
      success: false,
      message: "Failed to get plugin history",
      error:
        process.env.NODE_ENV === "production"
          ? "Internal server error"
          : error.message,
    });
  }
}
