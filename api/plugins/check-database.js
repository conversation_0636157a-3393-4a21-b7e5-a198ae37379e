import mongoose from "mongoose";
import jwt from "jsonwebtoken";

// Database connection for serverless
let isConnected = false;

const connectDB = async () => {
  if (mongoose.connection.readyState === 1) {
    return mongoose.connection;
  }

  if (mongoose.connection.readyState === 2) {
    await new Promise((resolve, reject) => {
      mongoose.connection.once("connected", resolve);
      mongoose.connection.once("error", reject);
      setTimeout(() => reject(new Error("Connection timeout")), 10000);
    });
    return mongoose.connection;
  }

  let mongoURI = process.env.MONGODB_URI;
  if (!mongoURI) {
    throw new Error("MONGODB_URI environment variable is not set");
  }

  const options = {
    maxPoolSize: 5,
    serverSelectionTimeoutMS: 8000,
    socketTimeoutMS: 30000,
    connectTimeoutMS: 10000,
    dbName: "wpdev_plugin",
    retryWrites: true,
    bufferCommands: false,
    maxIdleTimeMS: 30000,
    heartbeatFrequencyMS: 10000,
  };

  const conn = await mongoose.connect(mongoURI, options);
  console.log(`✅ MongoDB Connected: ${conn.connection.host}`);
  return conn;
};

const ensureDbConnection = async () => {
  if (!isConnected) {
    try {
      await connectDB();
      isConnected = true;
      console.log("Database connected successfully");
    } catch (error) {
      console.error("Database connection failed:", error);
      throw error;
    }
  }
};

// User Schema
const userSchema = new mongoose.Schema(
  {
    name: { type: String, required: true },
    email: { type: String, required: true, unique: true },
    password: { type: String, required: true },
    role: {
      type: String,
      enum: ["member", "admin", "superadmin"],
      default: "member",
    },
    isActive: { type: Boolean, default: true },
  },
  { timestamps: true }
);

// Plugin Schema
const pluginSchema = new mongoose.Schema(
  {
    slug: { type: String, required: true, unique: true },
    name: { type: String, required: true },
    currentRank: { type: Number, default: null },
    short_description: String,
    lastFetched: { type: Date, default: Date.now },
    fetchCount: { type: Number, default: 0 },
  },
  { timestamps: true }
);

// Create models
const User = mongoose.models.User || mongoose.model("User", userSchema);
const Plugin = mongoose.models.Plugin || mongoose.model("Plugin", pluginSchema);

// Simplified authentication for serverless
const authenticateUser = async (req) => {
  const authHeader = req.headers.authorization;
  const token =
    authHeader && authHeader.startsWith("Bearer ")
      ? authHeader.substring(7)
      : null;

  if (!token) {
    throw new Error("Access denied. No token provided.");
  }

  // Check if token is malformed
  if (token.split(".").length !== 3) {
    throw new Error("Invalid token format.");
  }

  // Verify token
  const decoded = jwt.verify(
    token,
    process.env.JWT_SECRET || "fallback-secret"
  );

  // Get user from database
  const user = await User.findById(decoded.userId);
  if (!user) {
    throw new Error("Invalid token. User not found.");
  }

  if (!user.isActive) {
    throw new Error("Account is deactivated.");
  }

  return user;
};

// Check if user is admin or superadmin
const requireAdmin = (user) => {
  if (!["admin", "superadmin"].includes(user.role)) {
    throw new Error("Access denied. Admin privileges required.");
  }
};

export default async function handler(req, res) {
  // Set CORS headers
  res.setHeader(
    "Access-Control-Allow-Origin",
    "https://pluginsight.vercel.app"
  );
  res.setHeader("Access-Control-Allow-Methods", "GET, OPTIONS");
  res.setHeader("Access-Control-Allow-Headers", "Content-Type, Authorization");
  res.setHeader("Access-Control-Allow-Credentials", "true");

  // Handle preflight requests
  if (req.method === "OPTIONS") {
    return res.status(200).end();
  }

  // Only allow GET requests
  if (req.method !== "GET") {
    return res.status(405).json({
      success: false,
      message: "Method not allowed",
    });
  }

  try {
    // Ensure database connection
    await ensureDbConnection();

    // Authenticate user
    const user = await authenticateUser(req);

    // Note: Removed admin privilege requirement to allow all authenticated users to check database

    // Check plugin count
    const pluginCount = await Plugin.countDocuments();

    res.json({
      success: true,
      hasPlugins: pluginCount > 0,
      pluginCount: pluginCount,
      message:
        pluginCount > 0
          ? "Plugins exist in database"
          : "No plugins in database",
    });
  } catch (error) {
    console.error("Database check error:", error);

    // Handle authentication errors
    if (
      error.message &&
      (error.message.includes("token") ||
        error.message.includes("Access denied") ||
        error.message.includes("Admin privileges") ||
        error.message.includes("expired") ||
        error.name === "TokenExpiredError" ||
        error.name === "JsonWebTokenError")
    ) {
      return res.status(401).json({
        success: false,
        message:
          error.name === "TokenExpiredError"
            ? "Token expired. Please login again."
            : "Authentication failed. Please login again.",
        error: error.message,
      });
    }

    res.status(500).json({
      success: false,
      message: "Failed to check database",
      error:
        process.env.NODE_ENV === "production"
          ? "Internal server error"
          : error.message,
    });
  }
}
