import mongoose from "mongoose";
import jwt from "jsonwebtoken";

// Database connection for serverless
let isConnected = false;

const connectDB = async () => {
  if (mongoose.connection.readyState === 1) {
    return mongoose.connection;
  }

  if (mongoose.connection.readyState === 2) {
    await new Promise((resolve, reject) => {
      mongoose.connection.once("connected", resolve);
      mongoose.connection.once("error", reject);
      setTimeout(() => reject(new Error("Connection timeout")), 10000);
    });
    return mongoose.connection;
  }

  let mongoURI = process.env.MONGODB_URI;
  if (!mongoURI) {
    throw new Error("MONGODB_URI environment variable is not set");
  }

  const options = {
    maxPoolSize: 5,
    serverSelectionTimeoutMS: 8000,
    socketTimeoutMS: 30000,
    connectTimeoutMS: 10000,
    dbName: "wpdev_plugin",
    retryWrites: true,
    bufferCommands: false,
    maxIdleTimeMS: 30000,
    heartbeatFrequencyMS: 10000,
  };

  const conn = await mongoose.connect(mongoURI, options);
  console.log(`✅ MongoDB Connected: ${conn.connection.host}`);
  return conn;
};

const ensureDbConnection = async () => {
  if (!isConnected) {
    try {
      await connectDB();
      isConnected = true;
      console.log("Database connected successfully");
    } catch (error) {
      console.error("Database connection failed:", error);
      throw error;
    }
  }
};

// User Schema
const userSchema = new mongoose.Schema(
  {
    name: { type: String, required: true },
    email: { type: String, required: true, unique: true },
    password: { type: String, required: true },
    role: {
      type: String,
      enum: ["member", "admin", "superadmin"],
      default: "member",
    },
    isActive: { type: Boolean, default: true },
  },
  { timestamps: true }
);

// Plugin Schema
const pluginSchema = new mongoose.Schema(
  {
    slug: { type: String, required: true, unique: true },
    name: { type: String, required: true },
    currentRank: { type: Number, default: null },
    short_description: String,
    lastFetched: { type: Date, default: Date.now },
    fetchCount: { type: Number, default: 0 },
  },
  { timestamps: true }
);

// AddedPlugin Schema
const addedPluginSchema = new mongoose.Schema(
  {
    userId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "User",
      required: true,
    },
    pluginSlug: { type: String, required: true, trim: true, lowercase: true },
    pluginName: { type: String, required: true, trim: true },
    displayName: { type: String, required: true, trim: true },
    currentRank: { type: Number, default: null },
    rankGrowth: { type: Number, default: null },
    isActive: { type: Boolean, default: true },
    pluginData: {
      version: String,
      rating: Number,
      downloaded: Number,
      last_updated: String,
      author: String,
    },
    addedAt: { type: Date, default: Date.now, index: true },
    lastUpdated: { type: Date, default: Date.now },
  },
  { timestamps: true }
);

// PluginInformation Schema
const pluginInformationSchema = new mongoose.Schema(
  {
    pluginSlug: {
      type: String,
      required: [true, "Plugin slug is required"],
      trim: true,
      lowercase: true,
      unique: true,
    },
    pluginName: {
      type: String,
      required: [true, "Plugin name is required"],
      trim: true,
    },
    displayName: {
      type: String,
      required: [true, "Display name is required"],
      trim: true,
    },
    currentRank: {
      type: Number,
      default: null,
    },
    rankGrowth: {
      type: Number,
      default: null,
    },
    rankHistory: {
      type: [
        {
          date: {
            type: String,
            required: true,
          },
          previousRank: {
            type: Number,
            required: true,
          },
          fetchedAt: {
            type: Date,
            default: Date.now,
          },
        },
      ],
      default: [],
    },
    pluginInfo: {
      type: mongoose.Schema.Types.Mixed,
      default: {},
    },
    version: {
      type: String,
      default: null,
    },
    author: {
      type: String,
      default: null,
    },
    authorProfile: {
      type: String,
      default: null,
    },
    contributors: {
      type: mongoose.Schema.Types.Mixed,
      default: {},
    },
    requires: {
      type: String,
      default: null,
    },
    tested: {
      type: String,
      default: null,
    },
    requiresPhp: {
      type: String,
      default: null,
    },
    rating: {
      type: Number,
      default: null,
    },
    numRatings: {
      type: Number,
      default: 0,
    },
    supportThreads: {
      type: Number,
      default: 0,
    },
    supportThreadsResolved: {
      type: Number,
      default: 0,
    },
    downloaded: {
      type: Number,
      default: 0,
    },
    lastUpdated: {
      type: String,
      default: null,
    },
    added: {
      type: String,
      default: null,
    },
    homepage: {
      type: String,
      default: null,
    },
    sections: {
      type: mongoose.Schema.Types.Mixed,
      default: {},
    },
    downloadLink: {
      type: String,
      default: null,
    },
    tags: {
      type: mongoose.Schema.Types.Mixed,
      default: {},
    },
    versions: {
      type: mongoose.Schema.Types.Mixed,
      default: {},
    },
    donateLink: {
      type: String,
      default: null,
    },
    banners: {
      type: mongoose.Schema.Types.Mixed,
      default: {},
    },
    icons: {
      type: mongoose.Schema.Types.Mixed,
      default: {},
    },
    blocks: {
      type: mongoose.Schema.Types.Mixed,
      default: [],
    },
    blockAssets: {
      type: mongoose.Schema.Types.Mixed,
      default: [],
    },
    authorBlockCount: {
      type: Number,
      default: 0,
    },
    authorBlockRating: {
      type: Number,
      default: 0,
    },
    screenshots: {
      type: mongoose.Schema.Types.Mixed,
      default: [],
    },
    fetchedAt: {
      type: Date,
      default: Date.now,
    },
    isActive: {
      type: Boolean,
      default: true,
    },
  },
  {
    timestamps: true,
  }
);

// PluginDownloadData Schema
const pluginDownloadDataSchema = new mongoose.Schema(
  {
    pluginSlug: {
      type: String,
      required: [true, "Plugin slug is required"],
      trim: true,
      lowercase: true,
      unique: true,
    },
    pluginName: {
      type: String,
      required: [true, "Plugin name is required"],
      trim: true,
    },
    downloadData: {
      type: mongoose.Schema.Types.Mixed,
      default: {},
    },
    latestDownloads: {
      type: Number,
      default: 0,
    },
    latestDate: {
      type: String,
      default: null,
    },
    fetchedAt: {
      type: Date,
      default: Date.now,
    },
    isActive: {
      type: Boolean,
      default: true,
    },
  },
  {
    timestamps: true,
  }
);

// Create models
const User = mongoose.models.User || mongoose.model("User", userSchema);
const Plugin = mongoose.models.Plugin || mongoose.model("Plugin", pluginSchema);
const AddedPlugin =
  mongoose.models.AddedPlugin ||
  mongoose.model("AddedPlugin", addedPluginSchema);
const PluginInformation =
  mongoose.models.PluginInformation ||
  mongoose.model("PluginInformation", pluginInformationSchema);
const PluginDownloadData =
  mongoose.models.PluginDownloadData ||
  mongoose.model("PluginDownloadData", pluginDownloadDataSchema);

// Simplified authentication for serverless
const authenticateUser = async (req) => {
  const authHeader = req.headers.authorization;
  const token =
    authHeader && authHeader.startsWith("Bearer ")
      ? authHeader.substring(7)
      : null;

  if (!token) {
    throw new Error("Access denied. No token provided.");
  }

  // Check if token is malformed
  if (token.split(".").length !== 3) {
    throw new Error("Invalid token format.");
  }

  // Verify token
  const decoded = jwt.verify(
    token,
    process.env.JWT_SECRET || "fallback-secret"
  );

  // Get user from database
  const user = await User.findById(decoded.userId);
  if (!user) {
    throw new Error("Invalid token. User not found.");
  }

  if (!user.isActive) {
    throw new Error("Account is deactivated.");
  }

  return user;
};

export default async function handler(req, res) {
  // Set CORS headers
  res.setHeader(
    "Access-Control-Allow-Origin",
    "https://pluginsight.vercel.app"
  );
  res.setHeader("Access-Control-Allow-Methods", "GET, OPTIONS");
  res.setHeader("Access-Control-Allow-Headers", "Content-Type, Authorization");
  res.setHeader("Access-Control-Allow-Credentials", "true");

  // Handle preflight requests
  if (req.method === "OPTIONS") {
    return res.status(200).end();
  }

  // Only allow GET requests
  if (req.method !== "GET") {
    return res.status(405).json({
      success: false,
      message: "Method not allowed",
    });
  }

  try {
    // Ensure database connection
    await ensureDbConnection();

    // Authenticate user
    const user = await authenticateUser(req);

    // Extract query parameters
    const {
      sortBy = "addedAt",
      sortOrder = "desc",
      limit = 20,
      page = 1,
    } = req.query;

    console.log("Get added plugins for all users");

    const limitNum = parseInt(limit);
    const pageNum = parseInt(page);
    const skip = (pageNum - 1) * limitNum;

    // Get all plugins from plugininformations collection directly
    const pluginInformations = await PluginInformation.find({
      isActive: true,
    })
      .sort({ fetchedAt: sortOrder === "desc" ? -1 : 1 })
      .skip(skip)
      .limit(limitNum);

    // Enhance plugins with additional data from other collections
    const enhancedPlugins = await Promise.all(
      pluginInformations.map(async (pluginInfo) => {
        try {
          // Get corresponding added plugin data (if exists) for user tracking
          const adminUsers = await User.find({
            role: { $in: ["admin", "superadmin"] },
            isActive: true,
          }).select("_id");

          const adminUserIds = adminUsers.map((user) => user._id);

          const addedPlugin = await AddedPlugin.findOne({
            userId: { $in: adminUserIds },
            pluginSlug: pluginInfo.pluginSlug,
            isActive: true,
          }).populate("userId", "name email");

          // Get plugin rank data from plugins collection
          const pluginData = await Plugin.findOne({
            slug: addedPlugin.pluginSlug,
          });

          // Get download data from plugindownloaddatas collection
          const downloadData = await PluginDownloadData.findOne({
            pluginSlug: addedPlugin.pluginSlug,
            isActive: true,
          });

          // Build download data history first
          let downloadDataHistory = [];
          let downloadTrend = null;

          if (downloadData && downloadData.downloadData) {
            // Get last 15 days of download data for history
            const endDate = new Date();
            endDate.setDate(endDate.getDate() - 1); // Yesterday
            const startDate = new Date();
            startDate.setDate(startDate.getDate() - 15);

            const currentDate = new Date(startDate);
            while (currentDate <= endDate) {
              const dateStr = currentDate.toISOString().split("T")[0];
              const downloads = downloadData.downloadData[dateStr] || 0;
              downloadDataHistory.push({
                date: dateStr,
                downloads: downloads,
              });
              currentDate.setDate(currentDate.getDate() + 1);
            }

            // Calculate download trends from downloadDataHistory by filtering dates
            const today = new Date();
            const yesterday = new Date(today);
            yesterday.setDate(yesterday.getDate() - 1);
            const dayBefore = new Date(today);
            dayBefore.setDate(dayBefore.getDate() - 2);

            const yesterdayStr = yesterday.toISOString().split("T")[0];
            const dayBeforeStr = dayBefore.toISOString().split("T")[0];

            // Filter downloadDataHistory to get yesterday and day before data
            const yesterdayData = downloadDataHistory.find(
              (item) => item.date === yesterdayStr
            );
            const dayBeforeData = downloadDataHistory.find(
              (item) => item.date === dayBeforeStr
            );

            const yesterdayDownloads = yesterdayData
              ? yesterdayData.downloads
              : 0;
            const dayBeforeDownloads = dayBeforeData
              ? dayBeforeData.downloads
              : 0;

            // Calculate trend if we have data for both days
            if (yesterdayDownloads >= 0 && dayBeforeDownloads >= 0) {
              const change = yesterdayDownloads - dayBeforeDownloads;
              const changePercent =
                dayBeforeDownloads > 0
                  ? Math.round((Math.abs(change) / dayBeforeDownloads) * 100)
                  : 0;

              downloadTrend = {
                yesterdayDownloads,
                dayBeforeDownloads,
                change,
                changePercent,
                isPositive: change >= 0,
              };
            }
          }

          // Merge data from all collections with pluginInfo as primary source
          const mergedData = {
            // Primary data from plugininformations collection
            slug: pluginInfo.pluginSlug,
            name: pluginInfo.pluginName,
            displayName: pluginInfo.displayName,
            pluginSlug: pluginInfo.pluginSlug,
            pluginName: pluginInfo.pluginName,
            // Plugin information from plugininformations collection
            version: pluginInfo.version || null,
            lastReleaseDate: pluginInfo.lastUpdated || null,
            rating: pluginInfo.rating || null,
            numRatings: pluginInfo.numRatings || 0,
            currentVersion: pluginInfo.version || null,
            previousVersions: pluginInfo.versions
              ? Object.keys(pluginInfo.versions).slice(0, 5)
              : [],
            icons: pluginInfo.icons || {},
            author: pluginInfo.author || null,
            requires: pluginInfo.requires || null,
            tested: pluginInfo.tested || null,
            requiresPhp: pluginInfo.requiresPhp || null,
            downloaded: pluginInfo.downloaded || 0,
            homepage: pluginInfo.homepage || null,
            sections: pluginInfo.sections || {},
            tags: pluginInfo.tags || {},
            banners: pluginInfo.banners || {},
            screenshots: pluginInfo.screenshots || [],
            // Rank data from plugins collection or plugininformations
            currentRank: pluginData?.currentRank || pluginInfo.currentRank,
            short_description:
              pluginData?.short_description ||
              pluginInfo.pluginInfo?.short_description ||
              null,
            // User tracking data from addedplugins (if exists)
            userId: addedPlugin?.userId || null,
            addedAt: addedPlugin?.addedAt || pluginInfo.createdAt,
            lastUpdated: addedPlugin?.lastUpdated || pluginInfo.updatedAt,
            lastFetched: pluginInfo.fetchedAt,
            // Enhanced data structure for frontend compatibility
            rankHistory: {
              rankChange: addedPlugin?.rankGrowth || pluginInfo.rankGrowth || 0,
            },
            rankGrowth: addedPlugin?.rankGrowth || pluginInfo.rankGrowth || 0,
            downloadTrend: downloadTrend,
            downloadDataHistory: downloadDataHistory,
            reviewStats: null,
            versionInfo: {
              current: pluginInfo.version || null,
              previous: pluginInfo.versions
                ? Object.keys(pluginInfo.versions).slice(0, 5)
                : [],
            },
            pluginInformation: pluginInfo.toObject(),
          };

          return mergedData;
        } catch (error) {
          console.error(
            `Error fetching plugin data for ${pluginInfo.pluginSlug}:`,
            error
          );
          return {
            slug: pluginInfo.pluginSlug,
            name: pluginInfo.pluginName,
            displayName: pluginInfo.displayName,
            pluginSlug: pluginInfo.pluginSlug,
            pluginName: pluginInfo.pluginName,
            version: pluginInfo.version,
            icons: pluginInfo.icons || {},
            pluginData: null,
          };
        }
      })
    );

    // Get total count for pagination info from plugininformations collection
    const totalCount = await PluginInformation.countDocuments({
      isActive: true,
    });

    res.json({
      success: true,
      addedPlugins: enhancedPlugins,
      count: enhancedPlugins.length,
      totalCount,
      page: pageNum,
      totalPages: Math.ceil(totalCount / limitNum),
      hasMore: skip + enhancedPlugins.length < totalCount,
    });
  } catch (error) {
    console.error("Get added plugins error:", error);

    // Handle authentication errors
    if (
      error.message &&
      (error.message.includes("token") ||
        error.message.includes("Access denied") ||
        error.message.includes("expired") ||
        error.name === "TokenExpiredError" ||
        error.name === "JsonWebTokenError")
    ) {
      return res.status(401).json({
        success: false,
        message:
          error.name === "TokenExpiredError"
            ? "Token expired. Please login again."
            : "Authentication failed. Please login again.",
        error: error.message,
      });
    }

    res.status(500).json({
      success: false,
      message: "Failed to get added plugins",
      error:
        process.env.NODE_ENV === "production"
          ? "Internal server error"
          : error.message,
    });
  }
}
