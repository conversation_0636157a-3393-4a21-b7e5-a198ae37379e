import express from "express";
import cors from "cors";
import dotenv from "dotenv";
import connectDB from "../backend/config/database.js";
import authRoutes from "../backend/routes/auth.js";
import pluginRoutes from "../backend/routes/plugins.js";
import userRoutes from "../backend/routes/users.js";
import keywordRoutes from "../backend/routes/keywords.js";
import competitorRoutes from "../backend/routes/competitors.js";
import analyticsRoutes from "../backend/routes/analytics.js";
import settingsRoutes from "../backend/routes/settings.js";
import pluginScheduler from "../backend/services/scheduler.js";

dotenv.config();

const app = express();

// Database connection state
let isConnected = false;

// Function to ensure database connection
const ensureDbConnection = async () => {
  if (!isConnected) {
    try {
      await connectDB();
      isConnected = true;
      console.log("Database connected successfully");
    } catch (error) {
      console.error("Database connection failed:", error);
      throw error;
    }
  }
};

// Middleware
const allowedOrigins = [
  "http://localhost:5173",
  "http://127.0.0.1:5173",
  "https://pluginsight.vercel.app",
  process.env.FRONTEND_URL, // Production frontend URL
].filter(Boolean); // Remove undefined values

app.use(
  cors({
    origin: function (origin, callback) {
      // Allow requests with no origin (like mobile apps or curl requests)
      if (!origin) return callback(null, true);

      if (allowedOrigins.includes(origin)) {
        return callback(null, true);
      }

      console.log("CORS blocked origin:", origin);
      return callback(new Error("Not allowed by CORS"), false);
    },
    credentials: true,
    methods: ["GET", "POST", "PUT", "DELETE", "OPTIONS"],
    allowedHeaders: ["Content-Type", "Authorization"],
  })
);
// Configure body parser with increased limit for plugin data
app.use(express.json({ limit: "1mb" }));
app.use(express.urlencoded({ limit: "1mb", extended: true }));

// Database connection middleware
app.use(async (req, res, next) => {
  try {
    await ensureDbConnection();
    next();
  } catch (error) {
    console.error("Database connection error:", error);

    // Ensure proper JSON response
    if (!res.headersSent) {
      res.setHeader("Content-Type", "application/json");
      res.status(500).json({
        success: false,
        message: "Database connection failed",
        error:
          process.env.NODE_ENV === "production"
            ? "Internal server error"
            : error.message,
      });
    }
  }
});

// Initialize scheduler after database connection
let schedulerInitialized = false;
app.use(async (req, res, next) => {
  if (!schedulerInitialized && isConnected) {
    try {
      console.log("🚀 Initializing plugin scheduler...");
      pluginScheduler.start();
      schedulerInitialized = true;
      console.log("✅ Plugin scheduler initialized successfully");
    } catch (error) {
      console.error("❌ Failed to initialize scheduler:", error);
    }
  }
  next();
});

// Security headers middleware
app.use((req, res, next) => {
  res.setHeader("X-Content-Type-Options", "nosniff");
  res.setHeader("X-Frame-Options", "DENY");
  res.setHeader("X-XSS-Protection", "1; mode=block");
  res.setHeader("Referrer-Policy", "strict-origin-when-cross-origin");
  next();
});

// Serve static files for uploads
app.use("/uploads", express.static("uploads"));

// Global error handler for payload size errors
app.use((error, req, res, next) => {
  if (error.type === "entity.too.large") {
    console.error("Payload too large error:", {
      expected: error.expected,
      length: error.length,
      limit: error.limit,
      url: req.url,
      method: req.method,
    });

    return res.status(413).json({
      success: false,
      message:
        "Request payload is too large. Please reduce the data size and try again.",
      error: "Payload too large",
      details: {
        limit: error.limit,
        received: error.length,
      },
    });
  }
  next(error);
});

// Routes
app.use("/api/auth", authRoutes);
app.use("/api/plugins", pluginRoutes);
app.use("/api/users", userRoutes);
app.use("/api/keywords", keywordRoutes);
app.use("/api/competitors", competitorRoutes);
app.use("/api/analytics", analyticsRoutes);
app.use("/api/settings", settingsRoutes);

// Health check endpoint
app.get("/api/health", (req, res) => {
  res.json({
    status: "OK",
    message: "Server is running with MongoDB",
    dbConnected: isConnected,
    timestamp: new Date().toISOString(),
    env: process.env.NODE_ENV,
  });
});

// Scheduler status endpoint
app.get("/api/scheduler/status", (req, res) => {
  const status = pluginScheduler.getStatus();
  res.json({
    success: true,
    status: status.isRunning ? "Running" : "Idle",
    lastFetchTime: status.lastFetchTime,
    fetchStats: status.fetchStats,
    message: "Scheduler is active. Daily fetch at 10 AM GMT+6 (4 AM UTC)",
  });
});

// Manual trigger endpoint for scheduler
app.post("/api/scheduler/trigger", async (req, res) => {
  try {
    console.log("🔧 Manual scheduler trigger requested");
    await pluginScheduler.triggerManualFetch();
    res.json({
      success: true,
      message: "Plugin fetch triggered successfully",
    });
  } catch (error) {
    console.error("❌ Scheduler trigger error:", error);
    res.status(500).json({
      success: false,
      message: "Failed to trigger plugin fetch",
      error: error.message,
    });
  }
});

// Global error handler - must be after all routes
app.use((error, req, res, next) => {
  console.error("Global error handler caught:", error);

  // Ensure proper JSON response
  if (!res.headersSent) {
    res.setHeader("Content-Type", "application/json");
    res.status(500).json({
      success: false,
      message: "Internal server error",
      error:
        process.env.NODE_ENV === "production" ? "Server error" : error.message,
    });
  }
});

// 404 handler for API routes
app.use("/api/*", (req, res) => {
  res.setHeader("Content-Type", "application/json");
  res.status(404).json({
    success: false,
    message: "API endpoint not found",
    path: req.path,
  });
});

// Export the Express API as a Vercel serverless function
export default async (req, res) => {
  try {
    // Set CORS headers for all requests
    const origin = req.headers.origin;
    const allowedOrigins = [
      "http://localhost:5173",
      "http://127.0.0.1:5173",
      "https://pluginsight.vercel.app",
      process.env.FRONTEND_URL,
    ].filter(Boolean);

    if (allowedOrigins.includes(origin)) {
      res.setHeader("Access-Control-Allow-Origin", origin);
    }

    res.setHeader("Access-Control-Allow-Credentials", "true");
    res.setHeader(
      "Access-Control-Allow-Methods",
      "GET, POST, PUT, DELETE, OPTIONS"
    );
    res.setHeader(
      "Access-Control-Allow-Headers",
      "Content-Type, Authorization"
    );

    // Handle preflight requests
    if (req.method === "OPTIONS") {
      res.status(200).end();
      return;
    }

    // Ensure we always return JSON responses
    res.setHeader("Content-Type", "application/json");

    return app(req, res);
  } catch (error) {
    console.error("Serverless function error:", error);

    // Ensure we return a proper JSON error response
    res.setHeader("Content-Type", "application/json");
    res.status(500).json({
      success: false,
      message: "Internal server error",
      error:
        process.env.NODE_ENV === "production" ? "Server error" : error.message,
    });
  }
};
